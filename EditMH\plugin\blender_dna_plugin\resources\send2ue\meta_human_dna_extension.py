import os
import sys
import bpy
from pathlib import Path
from mathutils import Vector
from send2ue.core.extension import ExtensionBase # type: ignore
from send2ue.core.utilities import report_error # type: ignore
# from send2ue.core.formatting import auto_format_unreal_folder_path, auto_format_unreal_asset_path # type: ignore
from send2ue.dependencies.rpc.factory import make_remote # type: ignore
from send2ue.core.export import export_file # type: ignore


class MetaHumanDna(ExtensionBase):
    name = 'meta_human_dna'

    enabled: bpy.props.BoolProperty(default=False) # type: ignore
    mesh_object_name: bpy.props.StringProperty(default='') # type: ignore
    asset_path: bpy.props.StringProperty(default='') # type: ignore

    @staticmethod
    def get_active_rig_logic():
        try:
            # Import from our plugin's callbacks
            import sys
            import os
            plugin_path = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            if plugin_path not in sys.path:
                sys.path.append(plugin_path)
            from utils.callbacks import get_active_rig_logic
            return get_active_rig_logic()
        except ImportError:
            return None

    @staticmethod
    def deselect_all():
        try:
            # Import from our plugin's callbacks
            import sys
            import os
            plugin_path = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            if plugin_path not in sys.path:
                sys.path.append(plugin_path)
            from utils.callbacks import deselect_all
            deselect_all()
        except ImportError:
            # Fallback implementation
            for scene_object in bpy.data.objects:
                scene_object.select_set(False)

    @staticmethod
    def get_lod_index(name: str) -> int:
        try:
            # Import from our plugin's callbacks
            import sys
            import os
            plugin_path = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            if plugin_path not in sys.path:
                sys.path.append(plugin_path)
            from utils.callbacks import get_lod_index
            return get_lod_index(name)
        except ImportError:
            # Fallback implementation
            import re
            LOD_REGEX = r'.*[_\.]lod(\d+).*'
            result = re.search(LOD_REGEX, name, re.IGNORECASE)
            if result:
                lod = result.groups()[-1]
                return int(lod)
            return -1


    def pre_operation(self, properties):
        # prevent continuous evaluation of the dependency graph by rig logic
        # Note: Our plugin doesn't have this property, so we skip this step

        # clear the face board control and evaluate the rig so it is in a neutral position
        instance = self.get_active_rig_logic()
        if instance:
            # Get the faceboard armature if it exists
            faceboard_name = getattr(instance, 'faceboard_name', None)
            if faceboard_name:
                faceboard = bpy.data.objects.get(faceboard_name)
                if faceboard and faceboard.type == 'ARMATURE':
                    # Clear pose bone locations for control bones
                    for pose_bone in faceboard.pose.bones:
                        if not pose_bone.bone.children and pose_bone.name.startswith('CTRL_'):
                            pose_bone.location = Vector((0.0, 0.0, 0.0))


    def pre_validations(self, properties) -> bool:
        if self.enabled:
            instance = self.get_active_rig_logic()
            if instance:
                # Basic validation - check if we have the required objects
                head_mesh_name = getattr(instance, 'head_mesh_name', None)
                if not head_mesh_name:
                    report_error("No head mesh found in DNA tools")
                    return False

                head_mesh = bpy.data.objects.get(head_mesh_name)
                if not head_mesh:
                    report_error(f"Head mesh '{head_mesh_name}' not found in scene")
                    return False

                # Check if armature exists
                armature_name = getattr(instance, 'armature_name', None)
                if armature_name:
                    armature = bpy.data.objects.get(armature_name)
                    if not armature:
                        report_error(f"Armature '{armature_name}' not found in scene")
                        return False

                # Note: Our plugin doesn't have the same Unreal path validation as the example
                # This could be extended later if needed
        return True

    def pre_mesh_export(self, asset_data, properties):
        if self.enabled:
            instance = self.get_active_rig_logic()
            mesh_object_name = asset_data.get('_mesh_object_name', '')
            mesh_object = bpy.data.objects.get(mesh_object_name)

            # Only proceed if the mesh object is the head mesh
            if instance and instance.head_mesh == mesh_object:
                # Deselect all objects
                self.deselect_all()

                # Override the selected objects with the output item list
                for item in instance.output_item_list:
                    if item.scene_object and item.include:
                        # Select only LOD 0 and non-LODs (-1)
                        if self.get_lod_index(item.scene_object.name) in [-1, 0]:
                            # Object must be visible to be selected
                            item.scene_object.hide_set(False)
                            item.scene_object.select_set(True)

                # Rename the asset to match the instance name
                _, extension = os.path.splitext(asset_data['file_path'])

                file_path = Path(bpy.path.abspath(instance.output_folder_path)).absolute() / 'export' / f'{instance.name}{extension}'
                asset_folder = '/' + '/'.join([i for i in instance.unreal_content_folder.split('/') if i]) + '/'

                # Update the file path, asset folder and asset path
                self.update_asset_data({
                    'asset_folder': asset_folder,
                    'file_path': str(file_path),
                    'asset_path': f'{asset_folder}{instance.name}'
                })

    def post_mesh_export(self, asset_data, properties):
        if self.enabled:
            if asset_data.get('lods'):
                # make send2ue skip exporting the lods, we will do it manually in the pre_import
                self.update_asset_data({
                    'skip': True
                })

    def pre_import(self, asset_data, properties):
        if self.enabled:
            instance = self.get_active_rig_logic()
            mesh_object_name = asset_data.get('_mesh_object_name', '')
            mesh_object = bpy.data.objects.get(mesh_object_name)
            # only proceed if the mesh object is the head mesh
            if instance and instance.head_mesh == mesh_object:
                lods = {}
                # determine how many lods are available and their file paths
                lod_indexes = []
                for item in instance.output_item_list:
                    if item.scene_object and item.include:
                        lod_index = self.get_lod_index(item.scene_object.name)
                        if lod_index not in [0, -1] and lod_index not in lod_indexes:
                            main_file_path = Path(asset_data['file_path'])
                            file_path = main_file_path.parent / f'{main_file_path.stem}_lod{lod_index}_mesh.fbx'
                            lod_indexes.append(lod_index)
                            lods[str(lod_index)] = str(file_path)

                # update the lod data
                self.update_asset_data({
                    'lods': lods,
                    'skip': False
                })

                # now loop through each lod and export it
                for lod_index in lod_indexes:
                    if lod_index == 0:
                        continue

                    # deselect all objects
                    self.deselect_all()

                    # select the objects for the current lod
                    for item in instance.output_item_list:
                        if item.scene_object and item.include:
                            if self.get_lod_index(item.scene_object.name) == lod_index:
                                # object must be visible to be selected
                                item.scene_object.hide_set(False)
                                item.scene_object.select_set(True)

                    # also select the head rig
                    instance.head_rig.hide_set(False)
                    instance.head_rig.select_set(True)
                    # export the file
                    export_file(
                        properties=properties,
                        lod=lod_index
                    )

    def post_import(self, asset_data, properties):
        if self.enabled:
            self.mesh_object_name = asset_data.get('_mesh_object_name', '')
            self.asset_path = asset_data.get('asset_path', '')

    def post_operation(self, properties):
        # defer this till the lods are imported
        if self.enabled:
            try:
                # make sure that the unreal utilities are available
                import sys
                import os

                # Add our plugin's unreal utilities to the path
                plugin_path = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
                unreal_utils_path = os.path.join(plugin_path, 'resources', 'unreal')
                if unreal_utils_path not in sys.path:
                    sys.path.append(unreal_utils_path)

                from meta_human_dna_utilities import update_meta_human_face

                # remotely update the dna
                instance = self.get_active_rig_logic()
                mesh_object = bpy.data.objects.get(self.mesh_object_name)

                # Get material name if available
                material_name = ''
                head_mesh_name = getattr(instance, 'head_mesh_name', None)
                if head_mesh_name:
                    head_mesh = bpy.data.objects.get(head_mesh_name)
                    if head_mesh and head_mesh.data.materials:
                        material_name = head_mesh.data.materials[0].name

                # Check if this is our head mesh
                if instance and head_mesh_name:
                    head_mesh = bpy.data.objects.get(head_mesh_name)
                    if head_mesh and mesh_object == head_mesh:
                        # Export DNA file first
                        dna_file_path = self.export_dna_file(instance)

                        if dna_file_path:
                            remote_update_meta_human_face = make_remote(update_meta_human_face)
                            remote_update_meta_human_face(
                                self.asset_path,
                                str(dna_file_path),
                                material_name,
                                '',  # face_control_rig_asset_path - not used in our plugin yet
                                '',  # face_anim_bp_asset_path - not used in our plugin yet
                                '',  # blueprint_asset_path - not used in our plugin yet
                                '',  # unreal_level_sequence_asset_path - not used in our plugin yet
                                False,  # copy_assets - not used in our plugin yet
                                {}  # material_slot_to_instance_mapping - not used in our plugin yet
                            )
            except Exception as e:
                print(f"Error in post_operation: {e}")

        # Always disable the extension afterwards since we don't want this to be enabled
        # unless the explicitly enabled via code.
        self.enabled = False
        # Note: Our plugin doesn't have this property, so we skip this step

    def export_dna_file(self, instance):
        """Export DNA file for the current instance"""
        try:
            # Use our plugin's DNA export functionality
            dna_file_path = Path.home() / 'Documents' / 'MetaHuman_Export' / f'{instance.head_mesh_name}.dna'
            dna_file_path.parent.mkdir(parents=True, exist_ok=True)

            # This would need to be implemented using our plugin's DNA export functionality
            # For now, return a placeholder path
            return dna_file_path
        except Exception as e:
            print(f"Error exporting DNA file: {e}")
            return None