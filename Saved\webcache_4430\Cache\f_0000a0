{"Updater": {"Feed": {"Release": [{"Version": "2022.1.1", "Date": "2022-12-08", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2022.1.1_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2022.1.1_Beta.app.zip", "Bullet": ["This update includes some behind-the-scene changes for implementation of age-restriction features.", "A warning is displayed on some assets that may be considered graphic by some users.", "Users not meeting the minimum age requirement will have restricted access to the app.", "Multiple bug fixes."]}, {"Version": "2022.1.0", "Date": "2022-09-08", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2022.1.0_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2022.1.0_Beta.app.zip", "Bullet": ["Opacity map support\n        - Mix and export assets with opacity maps.\n        - Import custom decals to the local library.", "Emissive map support\n        - Import, mix, and export assets with emissive maps.", "Material Options\n        - New material options allow you to set up how different materials assigned to the model should render.\n        - Opaque: Default solid rendering.\n        - Cutout: Hard mask to determine if a pixel should be opaque or invisible.\n        - Fade opacity: A softly masked material that can completely fade parts of the model.\n        - Transparent: A reflective but see-through material.\n        - Double-sided: Drawing the back side of a material the same way as the front side.", "Smart Materials\n        - 47 new smart materials based on wood surfaces completely free to use.", "Display updates\n        - overall emissive intensity\n        - bloom effect with intensity, radius and threshold controls", "Preferences\n        - New UI scale option 1.5x (scales nicely for 27” 4K screens)", "Bug Fixing\n        - Multiple bug fixes across the board"]}, {"Version": "2021.1.3", "Date": "2022-04-04", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2021.1.3_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2021.1.3_Beta.app.zip", "Bullet": "We’ve consolidated our Terms of Service under the new Content License Agreement by Epic Games. Quixel and other various services belonging to the Epic Games ecosystem are now covered under a single, convenient agreement. You’ll be asked to accept the Content License Agreement to use our services."}, {"Version": "2021.1.2", "Date": "2021-08-31", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2021.1.2_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2021.1.1_Beta.app.zip", "Bullet": ["Use the Auto-Create Layer Sets feature to create one new Layer Set per Texture Set.", "Quickly select target texture sets by pressing 'Q' while a Layer Set is selected.", "Easily move or copy layers or groups to another Layer Set (existing or new).", "Added multi-action options for the entire list of assets available in the Asset Manager.", "In the 2D grid view, custom names will be visible next to the UDIMs number.", "Improved the Height Blending interface.", "Multiple UI/ UX improvements.", "Multiple bug fixes related to saving, exporting, painting, projection and downloads."]}, {"Version": "2021.1.1", "Date": "2021-05-05", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2021.1.1_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2021.1.1_Beta.app.zip", "Bullet": ["*HotFix* Fixed saving and loading of placement settings for decal and surfaces in a mix.", "Fixed an issue where opening a Sample Mix while the Library was syncing on Mix<PERSON>'s first launch would lead to an unexpected behavior.", "Fixed an issue where the changelog might not be visible inside of Mixer on Mac."]}, {"Version": "2021.1.0", "Date": "2021-04-29", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2021.1.0_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2021.1.0_Beta.app.zip", "Bullet": ["Introducing support for Multiple Texture Sets and UDIMs. Easily setup Texture Sets based on Materials or UDIMs and intuitively target different parts of your mesh with Layer Sets.", "Ability to paint across Texture Sets", "A brand new Texture Sets Editor, allowing user friendly and quick management of Texture Sets.", "Auto Focus - A new viewport feature that keeps you focused on the parts of the mesh you are texturing.", "Flatten Mix feature that helps you minimize resource usage.", "Export of 3D assets to the local library for one click exports to your DCC using Bridge.", "100 new smart materials based on plastic surfaces completely free for use.", "Enhanced UI responsiveness and layer dragging", "Multiple feature enhancements and performance improvement across the board"]}, {"Version": "2020.1.6", "Date": "2020-11-19", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2020.1.6_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2020.1.6_Beta.app.zip", "Bullet": ["Introducing Mask Exports feature as a simple yet handy tool that allows you to export your masks made inside Mixer to be used in whatever DCC renderer you prefer. Mixer allows you to custom pack multiple masks into one map export, making it much easier to create composite masks.", "This release includes Proxy Support for running Mixer on networks that use a proxy server.", "65 new smart materials have been added to the smart materials library and are available for free. This pack includes Machinery/ Mechanical materials such as Rusted Metal, Scratched Painted Metal, Corroding Steel, Heat Affected, and many more.", "Multiple bug fixes related to saving, painting, masks, asset downloads, and blending modes."]}, {"Version": "2020.1.5", "Date": "2020-09-14", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2020.1.5_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2020.1.5_Beta.app.zip", "Bullet": ["73 new smart materials available for free and ready to use. This pack includes Archviz collection such as galvanized furniture metal, clean white marble, granite block, Fabrics such as old oily fabric, blue medical scrubs, worn beige fabric and many more in Ceramics and Rocks.", "Fixed brush becoming invisible when painting on a plane.", "Addressed issues with saving and exporting while the application is in the background.", "Addressed issues occurring when downloading all acquired assets.", "Improved user experience when using Mixer while offline.", "Multiple minor fixes."]}, {"Version": "2020.1.4", "Date": "2020-08-05", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2020.1.4_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2020.1.4_Beta.app.zip", "Bullet": ["Added new Smart Material Pack consisting of 44 new Smart Materials.", "Minor backend updates"]}, {"Version": "2020.1.3", "Date": "2020-07-03", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2020.1.3_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2020.1.3_Beta.app.zip", "Bullet": "*HotFix* Resolved issues with saving mixes"}, {"Version": "2020.1.2", "Date": "2020-06-22", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2020.1.2_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2020.1.2_Beta.app.zip", "Bullet": ["50 new Smart Materials, including Metals such as aluminium, oxidized iron, and gold, Fabrics such as denim, stained lined, camo gear, and an array of Plastics, Rocks, Woods, and Leathers. All these are ready to go and are completely free to use.", "Introducing the Asset Manager, bringing speed and convenience to your layer stack by giving you an overview of all assets being used and the ability to replace or reload them with just a few clicks.", "An improved and streamlined installer, that allows you to download only the components you need.", "Updated Camera navigation in the viewport. Zoom and rotate now pivot around the mouse position by default.", "Multiple bug fixes"]}, {"Version": "2020.1.1", "Date": "2020-03-14", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2020.1.1_Beta.zip", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2020.1.1_Beta.dmg", "Bullet": ["Added support for Mac OSX Catalina", "Fixed model loading issues on Mac OSX High Sierra", "Fixed paint masks not working on groups", "Fixed a couple of issues related to login and linking accounts", "Other minor fixes"]}, {"Version": "2020.1.0", "Date": "2020-02-27", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2020.1.0_Beta.zip", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2020.1.0_Beta.dmg", "Bullet": ["Introducing Mixer 2020 with the first look at 3D support. Allowing 3D texturing with painting, sculpting, blending and procedural masking, Mixer gives you creative control and ability to define your unique look, be it stylized, photo-real or beyond", "Smart Materials: Apply smart materials in a single click from the built in Quixel Smart Materials library or create your own", "Multi-channel 3D painting: Unlocking seamless 3D painting across UVs and an improved brush system with more controls", "Import Megascans 3D assets directly from the library or import your own custom 3D models", "Real time curvature: Create realistic wear, tear and dirt accumulation with the new real-time curvature system.", "Channel specific controls for every layer. Mixer now gives you control over every texture map in every layer, offering powerful blending, matching, contrast, inversion, custom texture swapping and opacity control of your layers", "Box Projection: Giving you total freedom over the tiling, scale, and rotation of your texture", "New Map Component: Allowing you to create masks based on any map of any layer or a library asset", "Material ID support", "Updated Position Gradient Component", "Updated Normal Mask Component", "Groups", "Color Tags for layers and groups"]}, {"Version": "2019.2.4", "Date": "2020-02-01", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2019.2.4_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2019.2.4_Beta.dmg", "Bullet": "Minor backend updates"}, {"Version": "2019.2.3", "Date": "2019-11-12", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2019.2.3_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2019.2.3_Beta.dmg", "Bullet": "<PERSON><PERSON><PERSON><PERSON> is now part of the <PERSON> family. Read more about it on our blog."}, {"Version": "2019.2.2", "Date": "2019-08-02", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2019.2.2_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2019.2.2_Beta.dmg", "Bullet": ["*HotFix* Fixed OpenGL graphics API crashing on certain monitor configurations (High refresh rate issue).", "Fixed bugs related to exports, fast paint mode and Undo when matching colors to base"]}, {"Version": "2019.2.1", "Date": "2019-07-23", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2019.2.1_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2019.2.1_Beta.dmg", "Bullet": "*HotFix* Resolved error at start up on specific hardware configurations"}, {"Version": "2019.2.0", "Date": "2019-07-19", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2019.2.0_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2019.2.0_Beta.dmg", "Bullet": ["Undo/Redo for all actions affecting the current mix", "Faster asset imports and application launch", "Added scatter modifier in the mask stack", "Improved jitters in the pattern component", "New Blend modes for the mask stack"]}, {"Version": "2019.1.4.0", "Date": "2019-05-16", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2019.1.4.0_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2019.1.4.0_Beta.dmg", "Bullet": ["Significant performance improvements in browsing and loading Projects and Mixes", "Major performance improvements in loading times for Mixer in general", "Fixed bug where sometimes loading library would cause issues", "Improved accuracy of generation of missing maps"]}, {"Version": "2019.1.3.0", "Date": "2019-05-08", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2019.1.3.0_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2019.1.3.0_Beta.dmg", "Bullet": ["Fixed issues with Decal Normal Maps", "Fixed viewport accuracy issues when switching working resolution", "Fixed issues with download settings", "Online library now shows correct assets based on different user types"]}, {"Version": "2019.1.2.0", "Date": "2019-04-19", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2019.1.2.0_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2019.1.2.0_Beta.dmg", "Bullet": ["Fixed asset downloading issues on Mac OSX.", "Minor UI Improvements"]}, {"Version": "2019.1.1.0", "Date": "2019-04-11", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2019.1.1.0_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2019.1.1.0_Beta.dmg", "Bullet": ["Improved ViewPort Grid", "Improved Mask Stack performance", "Fixed resolutions of loaded textures when changing working/export resolutions", "Fixed issues in Importing Assets From Folder", "Fixed issues with orthographic camera", "Fixed rendering issues in Mask Stack", "Fixed issues with renaming projects", "Fixed issues in Download all acquired assets", "Fixed issues in selecting presets in download settings", "Minor UI Improvements"]}, {"Version": "2019.1.0.0", "Date": "2019-03-14", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2019.1.0.0_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2019.1.0.0_Beta.dmg", "Bullet": ["Introducing <PERSON> Stack; unlocking a whole new level of creativity through a total synergy between scans and procedurals", "Includes Several Mask types: Normal, ImageMask, Noise Generators (Perlin, Simplex, Worley), Positional Gradient, Patterns (Checker, Square, Circle, Gradient), Solid Value and Curvature", "Also includes Modifier Components: Blur (G<PERSON>sian, Directional), Bevel, Brightness/Contrast, Invert, Clamp, Gradient Remap, Normalize, Transform and Circular Transform.", "Added new mask visualisation modes (Active Mask, Layer Mask)", "Added support to save and load mask stack presets", "Added support to copy/paste individual mask stack elements and entire mask stacks", "Considerably improved memory usage overall and especially during exports", "New Viewport grid added", "Added \"exit without saving\" dialogue.", "Added Shortcuts Menu (Help->View Shortcuts)", "Added option to Enable/Disable Tool tips in User Interface Preferences.", "Improved Color Picker UI", "Option to open asset folder in local library.", "Added option to Rename in right click menu in layers", "Added option to rename Project right click menu", "Improved view change log menu", "Added option to scale viewport for better performance on high resolution screens", "Added various options to improve performance for lower-end systems (under performance settings).", "Added indication that <PERSON> is unsaved.", "Fixed issue with shift painting where the start point gets painted twice", "Fixed Issue in saving custom brushes", "Fixed Issue in saving mixes", "And Numerous other small bug fixes"]}, {"Version": "2018.2.4.0", "Date": "2019-01-30", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2018.2.4.0_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2018.2.4.0_Beta.dmg", "Bullet": "Quixel Mixer (Free Beta) is free for the duration of the beta and no longer requires an active trial or subscription. This free beta is expected to run for most of this year, and we will keep you posted on our progress as usual."}, {"Version": "2018.2.3.0", "Date": "2018-12-03", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2018.2.3.0_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2018.2.3.0_Beta.dmg", "Bullet": ["Fixed Mac build to work with Mac OS Sierra or below by packing it in Mac OS Extended rather than APFS", "Fixed crash when changing resolutions or ground size", "Fixed \"Download All Acquired Assets\" to work consistently", "Fixed layer not showing properly after matching its colors to base when other layers clip to it", "Fixed normals losing details after tweaking when “Tweaking Normal Fix” was low res / off and “Bake Underlying Layers” was on", "Fixed a bunch of cases where clipped layers could get rendered twice into their targets", "Fixed issues with quitting while saving/exporting/saving asset to library causing crashes or incomplete exports", "Fixed issue where resetting colors would sometimes not work", "Fixed issue where \"change paint layer resolution\" popup was shown when it was not needed", "Fixed a number of cases where Mixer would stop responding when exporting or saving and trying to quit before it finished", "Improved and optimized eyedropper behavior", "Fixed Overlay blend mode for painted gloss causing rendered normals to sometimes disappear", "Fixed circular loading bar not going away from Local Library when switched to it the first time", "Fixed issue where refreshing the library while it's being loaded would cause exceptions", "Fixed PBR rendering bugs for both rendering workflows with the fast paint preview, where colors would not be set appropriately", "Fixed clipping targets adjusting targets with hq blur when a stroke is ongoing", "Fixed preview hq blur not using hq blur when rendering clipped layers", "Fixed changing hq blur mode not updating the viewport if working on a new mix directly after launching the app", "Fixed issue where brush color would sometimes be red even though eraser is inactive"]}, {"Version": "2018.2.2.0", "Date": "2018-11-13", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2018.2.2.0_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2018.2.2.0_Beta.dmg", "Bullet": ["Added Eyedropper, both via quick access (shift-left click color swatch), via Color Picker and via Q", "Orthographic View, toggled via the shortcut “P” (Including Angle-based Snap Rotation)", "Added Straight Line Painting, which can be canvas-aligned or camera-aligned (Shift or Shift+S respectively)", "Change layer textures, without changing the current layer settings (Right-click menu on layer)", "Added several new HDRIs", "Match Color to Base functionality (Middle mouse click color swatch)", "Added Quick Export (Ctrl+Shift+E)", "Several new stock paint brushes added", "Major improvements to Mixing performance!", "Numerous other minor bug fixes and quality of life improvements!"]}, {"Version": "2018.2.1.1", "Date": "2018-10-29", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2018.2.1.1_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2018.2.1.1_Beta.dmg", "Bullet": "*HotFix* Fixed an issue where some Atlas/Decal assets were not importing correctly in Mixer"}, {"Version": "2018.2.1.0", "Date": "2018-09-12", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2018.2.1.0_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2018.2.1.0_Beta.dmg", "Bullet": ["Introducing paint masks and image masks for layers", "Invert paint maps and masks", "Improved normal blending speed and accuracy", "New Opacity blend mode for layers", "Copy Paste masks (right click menu)", "Paint-brush size can now go down to 1mm instead of 1cm", "Added tooltips"]}, {"Version": "2018.1.5.2", "Date": "2018-08-15", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2018.1.5.2_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2018.1.5.2_Beta.dmg", "Bullet": ["Fixed issue where brushes would not show up if not downoaded from within Mixer.", "Fixed big black artifacts in normal maps that could show up for some users mixing certain assets."]}, {"Version": "2018.1.5.1", "Date": "2018-08-14", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2018.1.5.1_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2018.1.5.1_Beta.dmg", "Bullet": ["Updated Library handling to improve networked Library speed", "Fixed bug with clamped base normals while wrapping a layer to base"]}, {"Version": "2018.1.04", "Date": "2018-07-16", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2018.1.04_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2018.1.04_Beta.dmg", "Bullet": ["Liquid threshold is now allowed to go higher than given max value.", "Fixed bug where some users were getting the subscription pop-up when trying to run the Mixer in a specific time-period on the date of their subscription renewal.", "Fixed minor bugs with UI/navigation.", "Fixed issue where Clicking on \"View Asset\" without opening up Online tab first resets the search.", "Fixed right click menu not showing when UI was scaled for HDPI screens.", "Fixed issue where trial customers were unable to download free assets.", "Fixed issue where loading a paint layer mix sometimes didn't show the correct channels for the workflow.", "Duplicate Layer (Ctrl/Cmd + D).", "Updated painting hotkeys (Middle mouse + S to change brush/eraser size, Middle mouse + F to change brush fill, Middle mouse + O to change brush opacity)", "Eraser outline got red color."]}, {"Version": "2018.1.02", "Date": "2018-05-24", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2018.1.02_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2018.1.02_Beta.dmg", "Bullet": ["Added Quixel Privacy Policy and Unity's Data Privacy Page URLs to the About page", "Fixed ColorPicker not updating with the Preview toggle in some cases", "Fixed bugs causing the Mixer to not load/create new mixes in some cases", "Separated out Brush and Eraser settings", "Added HDPI support on Windows, with a toggle in Preferences->User Interface", "Online search: selecting a category/subcategory clears the search field", "Online search: searching a keyword in the search field clears the selected category to All", "Fixed some bugs with library not refreshing correctly in some cases"]}, {"Version": "2018.1.01", "Date": "2018-05-17", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2018.1.01_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2018.1.01_Beta.dmg", "Bullet": ["Fixed issue where a trial user was unable to load an asset preview in the Online tab", "Fixed layer preview icon loading", "Fixed issues loading custom imported assets", "Fixed issue where some users were getting subscription/trial popup even though they were subscribed"]}, {"Version": "2018.1.0", "Date": "2018-05-16", "Url": "https://d2shgxa8i058x8.cloudfront.net/mixer/win/Quixel_Mixer_2018.1.0_Beta.exe", "UrlMac": "https://d2shgxa8i058x8.cloudfront.net/mixer/mac/Quixel_Mixer_2018.1.0_Beta.dmg", "Bullet": ["Added Paint layers", "Added Online asset browsing and downloading", "Added Layer clipping", "Added Noise layers", "Added support for atlas category", "Added undo/redo functionality (Currently just for paint)"]}]}}}