"""
Property definitions for the Blender MetaHuman DNA Plugin.
"""

import bpy
from bpy.props import String<PERSON>roper<PERSON>, <PERSON>ol<PERSON>roperty, EnumProperty, IntProperty, FloatProperty, PointerProperty
from bpy.types import PropertyGroup

# Import utility functions
from ..utils.ui_utils import update_ui

# Callback function for property updates
def update_dna_loaded(self, context):
    """Callback function for when the is_dna_loaded property changes"""
    print(f"is_dna_loaded changed to {self.is_dna_loaded}")
    update_ui()

# Try to import the DNA modules
try:
    import dna
    from dna import BinaryStreamReader
    DNA_MODULES_AVAILABLE = True
except ImportError:
    DNA_MODULES_AVAILABLE = False

class DNAToolsProperties(PropertyGroup):
    """Properties for the MetaHuman DNA Tools"""

    # File path for the DNA file
    dna_file_path: StringProperty(
        name="DNA File Path",
        description="Path to the DNA file",
        default="",
        subtype='FILE_PATH'
    )

    # Import options
    import_all_lods: BoolProperty(
        name="Import All LODs",
        description="Import all Levels of Detail",
        default=False
    )

    # LOD selection
    lod_level: EnumProperty(
        name="LOD Level",
        description="Level of Detail to import",
        items=[
            ('0', "LOD 0", "Highest level of detail"),
            ('1', "LOD 1", "Medium level of detail"),
            ('2', "LOD 2", "Low level of detail"),
            ('3', "LOD 3", "Lowest level of detail"),
        ],
        default='0'
    )

    # Data layer selection
    data_layer: EnumProperty(
        name="Data Layer",
        description="DNA data layer to import",
        items=[
            ('ALL', "All Layers", "Import all data layers"),
            ('GEOMETRY', "Geometry", "Import only geometry data"),
            ('DEFINITION', "Definition", "Import only definition data"),
            ('BEHAVIOR', "Behavior", "Import only behavior data"),
        ],
        default='ALL'
    )

    # DNA reader (not a Blender property, but stored here for convenience)
    dna_reader = None

    # DNA file information (for display in the UI)
    dna_name: StringProperty(
        name="DNA Name",
        description="Name of the DNA character",
        default=""
    )

    dna_archetype: StringProperty(
        name="Archetype",
        description="Archetype of the DNA character",
        default=""
    )

    dna_gender: StringProperty(
        name="Gender",
        description="Gender of the DNA character",
        default=""
    )

    dna_age: IntProperty(
        name="Age",
        description="Age of the DNA character",
        default=0,
        min=0,
        max=150
    )

    # Metadata properties
    dna_character_id: StringProperty(
        name="Character ID",
        description="Unique identifier for the character",
        default=""
    )

    dna_character_name: StringProperty(
        name="Character Name",
        description="Name of the character",
        default=""
    )

    dna_character_type: StringProperty(
        name="Character Type",
        description="Type of the character",
        default=""
    )

    dna_mesh_count: IntProperty(
        name="Mesh Count",
        description="Number of meshes in the DNA file",
        default=0,
        min=0
    )

    dna_joint_count: IntProperty(
        name="Joint Count",
        description="Number of joints in the DNA file",
        default=0,
        min=0
    )

    dna_blendshape_count: IntProperty(
        name="Blend Shape Count",
        description="Number of blend shapes in the DNA file",
        default=0,
        min=0
    )

    # Flag to indicate if a DNA file is loaded
    is_dna_loaded: BoolProperty(
        name="DNA Loaded",
        description="Whether a DNA file is loaded",
        default=False,
        update=update_dna_loaded
    )

    # Flag to indicate if a mesh has been created
    is_mesh_created: BoolProperty(
        name="Mesh Created",
        description="Whether a mesh has been created from the DNA file",
        default=False
    )

    # Flag to indicate if an armature has been created
    is_armature_created: BoolProperty(
        name="Armature Created",
        description="Whether an armature has been created from the DNA file",
        default=False
    )

    # Flag to indicate if weights have been applied
    is_weights_applied: BoolProperty(
        name="Weights Applied",
        description="Whether weights have been applied to the mesh",
        default=False
    )

    # Flag to indicate if materials have been created
    is_materials_created: BoolProperty(
        name="Materials Created",
        description="Whether materials have been created for the mesh",
        default=False
    )

    # Flag to indicate if a faceboard has been created
    is_faceboard_created: BoolProperty(
        name="Faceboard Created",
        description="Whether a faceboard has been created for the character",
        default=False
    )

    # Flag to indicate if faceboard evaluation is active
    is_faceboard_evaluation_active: BoolProperty(
        name="Faceboard Evaluation Active",
        description="Whether faceboard evaluation is currently active",
        default=False
    )

    # Socket connection for faceboard evaluation
    faceboard_socket = None

    # Armature creation options
    use_custom_bone_shapes: BoolProperty(
        name="Custom Bone Shapes",
        description="Create custom bone shapes for better visualization",
        default=True
    )

    organize_bone_collections: BoolProperty(
        name="Organize Bone Collections",
        description="Organize bones into collections for better management",
        default=True
    )

    # DEPRECATED: This property is no longer used as weight application has been removed from armature creation
    fit_bones_to_mesh: BoolProperty(
        name="Fit Bones to Mesh",
        description="Fit bones to the mesh for better alignment (DEPRECATED - no longer used)",
        default=False
    )

    # Status message for the UI
    status_message: StringProperty(
        name="Status Message",
        description="Status message to display in the UI",
        default="No DNA file loaded"
    )

    # Debug settings
    enable_debug_logging: BoolProperty(
        name="Enable Debug Logging",
        description="Enable debug logging for development purposes",
        default=False
    )

    save_response_json: BoolProperty(
        name="Save Response JSON",
        description="Save received JSON responses to the reports folder",
        default=False
    )

    save_sent_controls: BoolProperty(
        name="Save Sent Controls",
        description="Save sent faceboard controls to the reports folder",
        default=False
    )

    # Unreal Engine integration properties
    unreal_content_folder: StringProperty(
        name="Unreal Content Folder",
        description="The content folder path in Unreal Engine where assets will be imported",
        default="/Game/MetaHumans/Export/",
        subtype='DIR_PATH'
    )

    unreal_blueprint_asset_path: StringProperty(
        name="Blueprint Asset Path",
        description="Path to the Unreal Engine blueprint asset",
        default="",
        subtype='FILE_PATH'
    )

    unreal_face_control_rig_asset_path: StringProperty(
        name="Face Control Rig Asset Path",
        description="Path to the face control rig asset in Unreal Engine",
        default="",
        subtype='FILE_PATH'
    )

    unreal_face_anim_bp_asset_path: StringProperty(
        name="Face Animation Blueprint Asset Path",
        description="Path to the face animation blueprint asset in Unreal Engine",
        default="",
        subtype='FILE_PATH'
    )

    # UI state tracking
    model_panel_expanded: BoolProperty(
        name="Model Panel Expanded",
        description="Whether the Model Creation panel is expanded",
        default=False
    )

    # Faceboard evaluation mode
    faceboard_evaluation_mode: EnumProperty(
        name="Evaluation Mode",
        description="How the faceboard should be evaluated",
        items=[
            ('CLICK', "On Viewport Clicked", "Only send data when the viewport is clicked (default behavior)"),
            ('CONTINUOUS', "Continuous", "Continuously send data with adaptive rate based on response time")
        ],
        default='CLICK'
    )

# Classes to register
classes = [
    DNAToolsProperties,
]

def register():
    """Register the property classes"""
    for cls in classes:
        bpy.utils.register_class(cls)

    # Register the properties
    bpy.types.Scene.dna_tools = PointerProperty(type=DNAToolsProperties)

def unregister():
    """Unregister the property classes"""
    # Unregister the properties
    del bpy.types.Scene.dna_tools

    # Unregister the classes
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
