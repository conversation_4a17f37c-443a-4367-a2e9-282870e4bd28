﻿Log file open, 05/27/25 12:48:35
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=34632)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: BlenderLinkProject
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\Plugins\BlenderLinkProject\BlenderLinkProject.uproject""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.236392
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-C75797394409D60BE325BE889C45B395
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/Plugins/BlenderLinkProject/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading Android ini files took 0.05 seconds
LogConfig: Display: Loading IOS ini files took 0.05 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.05 seconds
LogConfig: Display: Loading Mac ini files took 0.05 seconds
LogAssetRegistry: Display: Asset registry cache read as 43.8 MiB from H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_0.bin
LogConfig: Display: Loading Windows ini files took 0.06 seconds
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogConfig: Display: Loading TVOS ini files took 0.06 seconds
LogConfig: Display: Loading Unix ini files took 0.06 seconds
LogConfig: Display: Loading VisionOS ini files took 0.06 seconds
LogConfig: Display: Loading Linux ini files took 0.06 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.06 seconds
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin Mutable
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SkeletalMerging
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin XRBase
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OpenColorIO
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LiveLinkControlRig
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RigLogicMutable
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin MetaHuman
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaIOFramework
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin LensComponent
LogPluginManager: Mounting Engine plugin CaptureData
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin ARUtilities
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Engine plugin AppleARKitFaceSupport
LogPluginManager: Mounting Engine plugin AppleARKit
LogPluginManager: Mounting Project plugin BlenderLink
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 2147483647 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: AMD Radeon RX 6900 XT
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: 
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.52ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalMerging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XRBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LiveLinkControlRig' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RigLogicMutable' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MetaHuman' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CaptureData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ARUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKitFaceSupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKit' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlenderLink' had been unloaded. Reloading on-demand took 0.06ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.05.27-07.18.35:766][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.05.27-07.18.35:766][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.05.27-07.18.35:766][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.05.27-07.18.35:766][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.05.27-07.18.35:766][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.05.27-07.18.35:766][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.05.27-07.18.35:766][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.05.27-07.18.35:766][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.05.27-07.18.35:767][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.05.27-07.18.35:767][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.05.27-07.18.35:767][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.05.27-07.18.35:768][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.05.27-07.18.35:768][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.05.27-07.18.35:768][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.05.27-07.18.35:768][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.05.27-07.18.35:768][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.05.27-07.18.35:769][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.05.27-07.18.35:769][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.05.27-07.18.35:769][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.05.27-07.18.35:769][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.05.27-07.18.35:769][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.05.27-07.18.35:771][  0]LogRHI: Using Default RHI: D3D12
[2025.05.27-07.18.35:771][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.27-07.18.35:771][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.27-07.18.35:775][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.05.27-07.18.35:775][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.27-07.18.35:871][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.05.27-07.18.35:871][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.27-07.18.35:871][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 3 output[s]
[2025.05.27-07.18.35:871][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.27-07.18.35:871][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.05.27-07.18.36:022][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.05.27-07.18.36:022][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.27-07.18.36:022][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.27-07.18.36:022][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.05.27-07.18.36:022][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.05.27-07.18.36:031][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.05.27-07.18.36:031][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.05.27-07.18.36:031][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.27-07.18.36:031][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.05.27-07.18.36:031][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.05.27-07.18.36:032][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.27-07.18.36:032][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.05.27-07.18.36:032][  0]LogHAL: Display: Platform has ~ 64 GB [68631805952 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.05.27-07.18.36:032][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.05.27-07.18.36:032][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.27-07.18.36:032][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.05.27-07.18.36:032][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.05.27-07.18.36:032][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.27-07.18.36:032][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.05.27-07.18.36:032][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.05.27-07.18.36:032][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.05.27-07.18.36:032][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.05.27-07.18.36:032][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.05.27-07.18.36:032][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.27-07.18.36:032][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.05.27-07.18.36:032][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.05.27-07.18.36:032][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/Plugins/BlenderLinkProject/Saved/Config/WindowsEditor/Editor.ini]
[2025.05.27-07.18.36:032][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.05.27-07.18.36:032][  0]LogInit: User: Shashank
[2025.05.27-07.18.36:032][  0]LogInit: CPU Page size=4096, Cores=16
[2025.05.27-07.18.36:032][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.05.27-07.18.36:285][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.05.27-07.18.36:285][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.05.27-07.18.36:285][  0]LogMemory: Process Physical Memory: 625.45 MB used, 642.98 MB peak
[2025.05.27-07.18.36:285][  0]LogMemory: Process Virtual Memory: 756.75 MB used, 756.75 MB peak
[2025.05.27-07.18.36:285][  0]LogMemory: Physical Memory: 24648.15 MB used,  40804.24 MB free, 65452.39 MB total
[2025.05.27-07.18.36:285][  0]LogMemory: Virtual Memory: 40669.70 MB used,  28878.69 MB free, 69548.39 MB total
[2025.05.27-07.18.36:285][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.05.27-07.18.36:289][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.05.27-07.18.36:294][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.05.27-07.18.36:294][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.05.27-07.18.36:295][  0]LogInit: Using OS detected language (en-GB).
[2025.05.27-07.18.36:295][  0]LogInit: Using OS detected locale (en-IN).
[2025.05.27-07.18.36:297][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.05.27-07.18.36:297][  0]LogInit: Setting process to per monitor DPI aware
[2025.05.27-07.18.36:563][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.05.27-07.18.36:563][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.05.27-07.18.36:563][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.05.27-07.18.36:575][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.05.27-07.18.36:575][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.05.27-07.18.36:654][  0]LogRHI: Using Default RHI: D3D12
[2025.05.27-07.18.36:654][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.27-07.18.36:654][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.27-07.18.36:654][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.27-07.18.36:654][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.27-07.18.36:654][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.05.27-07.18.36:655][  0]LogWindows: Attached monitors:
[2025.05.27-07.18.36:655][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY5'
[2025.05.27-07.18.36:655][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY1' [PRIMARY]
[2025.05.27-07.18.36:655][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY6'
[2025.05.27-07.18.36:655][  0]LogWindows: Found 3 attached monitors.
[2025.05.27-07.18.36:655][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.27-07.18.36:655][  0]LogRHI: RHI Adapter Info:
[2025.05.27-07.18.36:655][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.05.27-07.18.36:655][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.27-07.18.36:655][  0]LogRHI:      Driver Date: 4-25-2025
[2025.05.27-07.18.36:655][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.05.27-07.18.36:681][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.05.27-07.18.36:744][  0]LogNvidiaAftermath: Aftermath initialized
[2025.05.27-07.18.36:744][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.05.27-07.18.36:819][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: Bindless resources are supported
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: Raster order views are supported
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.05.27-07.18.36:819][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.05.27-07.18.36:845][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000066431535300)
[2025.05.27-07.18.36:845][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000066431535580)
[2025.05.27-07.18.36:845][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000066431535800)
[2025.05.27-07.18.36:845][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.05.27-07.18.36:845][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.05.27-07.18.36:845][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.05.27-07.18.36:846][  0]LogRHI: Texture pool is 9808 MB (70% of 14012 MB)
[2025.05.27-07.18.36:846][  0]LogD3D12RHI: Async texture creation enabled
[2025.05.27-07.18.36:846][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.05.27-07.18.36:858][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.05.27-07.18.36:862][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.05.27-07.18.36:868][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all'
[2025.05.27-07.18.36:868][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all" ]
[2025.05.27-07.18.36:886][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.05.27-07.18.36:886][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.05.27-07.18.36:886][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.05.27-07.18.36:886][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.05.27-07.18.36:886][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.05.27-07.18.36:886][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.05.27-07.18.36:886][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.05.27-07.18.36:886][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.05.27-07.18.36:886][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.05.27-07.18.36:908][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.05.27-07.18.36:908][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.05.27-07.18.36:908][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.05.27-07.18.36:908][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.05.27-07.18.36:908][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.05.27-07.18.36:908][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.05.27-07.18.36:908][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.05.27-07.18.36:908][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.05.27-07.18.36:908][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.05.27-07.18.36:908][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.05.27-07.18.36:921][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.05.27-07.18.36:921][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.05.27-07.18.36:934][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.05.27-07.18.36:934][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.05.27-07.18.36:934][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.05.27-07.18.36:934][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.05.27-07.18.36:947][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.05.27-07.18.36:947][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.05.27-07.18.36:947][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.05.27-07.18.36:959][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.05.27-07.18.36:959][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.05.27-07.18.36:959][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.05.27-07.18.36:959][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.05.27-07.18.36:971][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.05.27-07.18.36:971][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.05.27-07.18.36:986][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.05.27-07.18.36:986][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.05.27-07.18.36:986][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.05.27-07.18.36:986][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.05.27-07.18.36:986][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.05.27-07.18.37:023][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   SF_METAL
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   VVM_1_0
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.05.27-07.18.37:026][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.05.27-07.18.37:026][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.05.27-07.18.37:026][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.05.27-07.18.37:028][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.05.27-07.18.37:028][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.05.27-07.18.37:028][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.05.27-07.18.37:028][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.27-07.18.37:028][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.05.27-07.18.37:084][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.05.27-07.18.37:084][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.27-07.18.37:084][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.05.27-07.18.37:084][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.05.27-07.18.37:085][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.27-07.18.37:085][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.27-07.18.37:085][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.05.27-07.18.37:085][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 38144 --child-id Zen_38144_Startup'
[2025.05.27-07.18.37:144][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.05.27-07.18.37:144][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.059 seconds
[2025.05.27-07.18.37:145][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.05.27-07.18.37:148][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.00 seconds.
[2025.05.27-07.18.37:148][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.02ms. RandomReadSpeed=2504.55MBs, RandomWriteSpeed=390.08MBs. Assigned SpeedClass 'Local'
[2025.05.27-07.18.37:149][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.05.27-07.18.37:149][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.05.27-07.18.37:149][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.05.27-07.18.37:149][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.05.27-07.18.37:149][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.05.27-07.18.37:149][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.05.27-07.18.37:149][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.05.27-07.18.37:150][  0]LogShaderCompilers: Guid format shader working directory is 19 characters bigger than the processId version (H:/Plugins/BlenderLinkProject/Intermediate/Shaders/WorkingDirectory/38144/).
[2025.05.27-07.18.37:150][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/DEC232D244FA9DB73D664E9C6C6C4F0D/'.
[2025.05.27-07.18.37:150][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.05.27-07.18.37:150][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.05.27-07.18.37:151][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/Plugins/BlenderLinkProject/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.05.27-07.18.37:151][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.05.27-07.18.37:563][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.05.27-07.18.38:059][  0]LogSlate: Using FreeType 2.10.0
[2025.05.27-07.18.38:059][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.05.27-07.18.38:060][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.27-07.18.38:060][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.27-07.18.38:060][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.27-07.18.38:060][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.27-07.18.38:060][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.27-07.18.38:060][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.27-07.18.38:080][  0]LogAssetRegistry: FAssetRegistry took 0.0019 seconds to start up
[2025.05.27-07.18.38:082][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.05.27-07.18.38:086][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin.
[2025.05.27-07.18.38:087][  0]LogAssetRegistry: Error: Package is unloadable: H:/Plugins/BlenderLinkProject/Content/SM_Rock_ukxmdhuga.uasset. Reason: Invalid value for PACKAGE_FILE_TAG at start of file.
[2025.05.27-07.18.38:267][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-07.18.38:268][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.05.27-07.18.38:268][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.05.27-07.18.38:268][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.05.27-07.18.38:279][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.05.27-07.18.38:279][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.05.27-07.18.38:302][  0]LogDeviceProfileManager: Active device profile: [000006644F838200][000006644D770000 66] WindowsEditor
[2025.05.27-07.18.38:302][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.05.27-07.18.38:302][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.05.27-07.18.38:304][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.05.27-07.18.38:304][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.05.27-07.18.38:332][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:332][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.05.27-07.18.38:332][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-07.18.38:332][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:332][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.05.27-07.18.38:332][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-07.18.38:332][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:333][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.05.27-07.18.38:333][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-07.18.38:333][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:333][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.05.27-07.18.38:333][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-07.18.38:334][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:334][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:334][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.05.27-07.18.38:334][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.27-07.18.38:334][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:334][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.05.27-07.18.38:334][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.27-07.18.38:334][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:335][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:335][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.05.27-07.18.38:335][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-07.18.38:335][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:335][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.05.27-07.18.38:335][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-07.18.38:335][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:335][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-07.18.38:335][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:335][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.05.27-07.18.38:335][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.27-07.18.38:335][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:335][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.05.27-07.18.38:335][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.27-07.18.38:335][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-07.18.38:336][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.05.27-07.18.38:336][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-07.18.38:336][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:336][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.05.27-07.18.38:336][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-07.18.38:336][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.05.27-07.18.38:336][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.27-07.18.38:336][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:336][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.05.27-07.18.38:336][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.27-07.18.38:337][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.05.27-07.18.38:337][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-07.18.38:337][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-07.18.38:337][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.05.27-07.18.38:337][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-07.18.38:481][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.05.27-07.18.38:481][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.05.27-07.18.38:481][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.05.27-07.18.38:481][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.05.27-07.18.38:481][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.05.27-07.18.38:599][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.44ms
[2025.05.27-07.18.38:615][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.43ms
[2025.05.27-07.18.38:626][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.42ms
[2025.05.27-07.18.38:627][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.45ms
[2025.05.27-07.18.38:807][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.05.27-07.18.38:807][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.05.27-07.18.38:811][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.05.27-07.18.38:811][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.05.27-07.18.38:811][  0]LogLiveCoding: Display: First instance in process group "UE_BlenderLinkProject_0xe99fe6f9", spawning console
[2025.05.27-07.18.38:814][  0]LogLiveCoding: Display: Waiting for server
[2025.05.27-07.18.38:826][  0]LogSlate: Border
[2025.05.27-07.18.38:827][  0]LogSlate: BreadcrumbButton
[2025.05.27-07.18.38:827][  0]LogSlate: Brushes.Title
[2025.05.27-07.18.38:827][  0]LogSlate: Default
[2025.05.27-07.18.38:827][  0]LogSlate: Icons.Save
[2025.05.27-07.18.38:827][  0]LogSlate: Icons.Toolbar.Settings
[2025.05.27-07.18.38:827][  0]LogSlate: ListView
[2025.05.27-07.18.38:827][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.05.27-07.18.38:827][  0]LogSlate: SoftwareCursor_Grab
[2025.05.27-07.18.38:827][  0]LogSlate: TableView.DarkRow
[2025.05.27-07.18.38:827][  0]LogSlate: TableView.Row
[2025.05.27-07.18.38:827][  0]LogSlate: TreeView
[2025.05.27-07.18.38:880][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.05.27-07.18.38:886][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.05.27-07.18.38:888][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 1.693 ms
[2025.05.27-07.18.38:896][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.43ms
[2025.05.27-07.18.38:909][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.05.27-07.18.38:909][  0]LogInit: XR: MultiViewport is Disabled
[2025.05.27-07.18.38:909][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.05.27-07.18.38:909][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.05.27-07.18.39:081][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.05.27-07.18.39:081][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.05.27-07.18.39:081][  0]LogNNERuntimeORT: 1: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.05.27-07.18.39:081][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.05.27-07.18.39:081][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.05.27-07.18.39:178][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.05.27-07.18.39:178][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.05.27-07.18.39:191][  0]LogMetaSound: MetaSound Engine Initialized
[2025.05.27-07.18.39:389][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.27-07.18.39:389][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.27-07.18.39:420][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.46ms
[2025.05.27-07.18.39:543][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: A34E470836584D61800000000000ED00 | Instance: AF2E67A3473603326D276D9C474B4527 (DESKTOP-E41IK6R-38144).
[2025.05.27-07.18.39:606][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.05.27-07.18.39:714][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.05.27-07.18.39:714][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group *********:6666.
[2025.05.27-07.18.39:715][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:59630'.
[2025.05.27-07.18.39:717][  0]LogUdpMessaging: Display: Added local interface '192.168.31.37' to multicast group '*********:6666'
[2025.05.27-07.18.39:717][  0]LogUdpMessaging: Display: Added local interface '************' to multicast group '*********:6666'
[2025.05.27-07.18.39:717][  0]LogUdpMessaging: Display: Added local interface '************' to multicast group '*********:6666'
[2025.05.27-07.18.39:723][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.05.27-07.18.39:783][  0]LogMutable: Creating Mutable Customizable Object System.
[2025.05.27-07.18.39:818][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.05.27-07.18.39:826][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.05.27-07.18.39:837][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.27-07.18.39:837][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.27-07.18.39:904][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.05.27-07.18.39:904][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.05.27-07.18.39:904][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.05.27-07.18.39:904][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.05.27-07.18.39:905][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.05.27-07.18.39:905][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.05.27-07.18.39:906][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.05.27-07.18.39:906][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.05.27-07.18.39:906][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.05.27-07.18.39:906][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.05.27-07.18.39:906][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.05.27-07.18.39:906][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.05.27-07.18.39:907][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.05.27-07.18.39:907][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.05.27-07.18.39:907][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.05.27-07.18.39:907][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.05.27-07.18.39:907][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.05.27-07.18.39:907][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.05.27-07.18.39:908][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.05.27-07.18.39:908][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.05.27-07.18.39:908][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.05.27-07.18.39:909][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.05.27-07.18.39:909][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.05.27-07.18.39:909][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.05.27-07.18.39:909][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.05.27-07.18.39:909][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.05.27-07.18.39:909][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.05.27-07.18.39:910][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.05.27-07.18.39:910][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.05.27-07.18.39:910][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.05.27-07.18.39:910][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.05.27-07.18.39:910][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.05.27-07.18.39:910][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.05.27-07.18.39:911][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.05.27-07.18.39:911][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.05.27-07.18.39:984][  0]SourceControl: Revision control is disabled
[2025.05.27-07.18.39:993][  0]SourceControl: Revision control is disabled
[2025.05.27-07.18.40:010][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.44ms
[2025.05.27-07.18.40:016][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.41ms
[2025.05.27-07.18.40:229][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.27-07.18.40:229][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.27-07.18.40:274][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.05.27-07.18.40:699][  0]LogSkeletalMesh: Building Skeletal Mesh Face_Archetype...
[2025.05.27-07.18.40:768][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (Face_Archetype) ...
[2025.05.27-07.18.46:772][  0]LogSkeletalMesh: Skeletal mesh [/MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-07.18.46:784][  0]LogSkeletalMesh: Built Skeletal Mesh [6.09s] /MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype
[2025.05.27-07.18.46:801][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.27-07.18.46:801][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.27-07.18.46:802][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.27-07.18.46:802][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.27-07.18.46:802][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.27-07.18.46:802][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.27-07.18.46:809][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.27-07.18.46:809][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.27-07.18.46:891][  0]LogCollectionManager: Loaded 0 collections in 0.000728 seconds
[2025.05.27-07.18.46:892][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Saved/Collections/' took 0.00s
[2025.05.27-07.18.46:895][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Developers/Shashank/Collections/' took 0.00s
[2025.05.27-07.18.46:897][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Collections/' took 0.00s
[2025.05.27-07.18.46:927][  0]LogBlenderLink: Initializing BlenderLink socket listener
[2025.05.27-07.18.46:928][  0]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.27-07.18.46:928][  0]LogBlenderLink: Set socket buffer sizes to 524288 bytes
[2025.05.27-07.18.46:928][  0]LogBlenderLink: Binding socket to 127.0.0.1:2907
[2025.05.27-07.18.46:928][  0]LogBlenderLink: Warning: Socket bind success: true
[2025.05.27-07.18.46:928][  0]LogBlenderLink: Warning: Socket listen success: true
[2025.05.27-07.18.46:928][  0]LogBlenderLink: Waiting for client connection...
[2025.05.27-07.18.46:943][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.27-07.18.46:943][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.27-07.18.46:943][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.27-07.18.46:943][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.27-07.18.46:943][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.27-07.18.46:943][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.27-07.18.46:949][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.27-07.18.46:949][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.27-07.18.46:965][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-05-27T07:18:46.965Z using C
[2025.05.27-07.18.46:965][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=BlenderLinkProject, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.05.27-07.18.46:965][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.27-07.18.46:966][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.05.27-07.18.46:971][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.05.27-07.18.46:971][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.05.27-07.18.46:971][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.05.27-07.18.46:971][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000063
[2025.05.27-07.18.46:971][  0]LogFab: Display: Logging in using persist
[2025.05.27-07.18.46:973][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.05.27-07.18.47:002][  0]LogUObjectArray: 52283 objects as part of root set at end of initial load.
[2025.05.27-07.18.47:002][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.05.27-07.18.47:013][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 38255 public script object entries (1030.17 KB)
[2025.05.27-07.18.47:013][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.05.27-07.18.47:118][  0]LogEngine: Initializing Engine...
[2025.05.27-07.18.47:121][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.05.27-07.18.47:121][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.05.27-07.18.47:190][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.05.27-07.18.47:202][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.05.27-07.18.47:211][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.05.27-07.18.47:211][  0]LogInit: Texture streaming: Enabled
[2025.05.27-07.18.47:218][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.05.27-07.18.47:225][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.05.27-07.18.47:229][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.05.27-07.18.47:230][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.05.27-07.18.47:230][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.05.27-07.18.47:230][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.05.27-07.18.47:230][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.05.27-07.18.47:230][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.05.27-07.18.47:230][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.05.27-07.18.47:230][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.05.27-07.18.47:230][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.05.27-07.18.47:230][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.05.27-07.18.47:230][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.05.27-07.18.47:230][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.05.27-07.18.47:230][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.05.27-07.18.47:230][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.05.27-07.18.47:230][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.05.27-07.18.47:234][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.05.27-07.18.47:286][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter Input (VB-Audio Voicemeeter VAIO)
[2025.05.27-07.18.47:287][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.05.27-07.18.47:287][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.05.27-07.18.47:287][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.05.27-07.18.47:288][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.05.27-07.18.47:288][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.05.27-07.18.47:290][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.05.27-07.18.47:290][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.05.27-07.18.47:290][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.05.27-07.18.47:290][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.05.27-07.18.47:290][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.05.27-07.18.47:295][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.05.27-07.18.47:297][  0]LogInit: Undo buffer set to 256 MB
[2025.05.27-07.18.47:297][  0]LogInit: Transaction tracking system initialized
[2025.05.27-07.18.47:307][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded H:/Plugins/BlenderLinkProject/Saved/SourceControl/UncontrolledChangelists.json
[2025.05.27-07.18.47:347][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.46ms
[2025.05.27-07.18.47:347][  0]LocalizationService: Localization service is disabled
[2025.05.27-07.18.47:357][  0]LogTimingProfiler: Initialize
[2025.05.27-07.18.47:357][  0]LogTimingProfiler: OnSessionChanged
[2025.05.27-07.18.47:357][  0]LoadingProfiler: Initialize
[2025.05.27-07.18.47:357][  0]LoadingProfiler: OnSessionChanged
[2025.05.27-07.18.47:357][  0]LogNetworkingProfiler: Initialize
[2025.05.27-07.18.47:357][  0]LogNetworkingProfiler: OnSessionChanged
[2025.05.27-07.18.47:357][  0]LogMemoryProfiler: Initialize
[2025.05.27-07.18.47:357][  0]LogMemoryProfiler: OnSessionChanged
[2025.05.27-07.18.47:477][  0]LogAutoReimportManager: Warning: Unable to watch directory H:/Plugins/BlenderLinkProject/Content/ as it will conflict with another watching H:/Plugins/BlenderLinkProject/Content/.
[2025.05.27-07.18.47:486][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/' took 0.01s
[2025.05.27-07.18.47:515][  0]LogPython: Using Python 3.11.8
[2025.05.27-07.18.48:452][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.05.27-07.18.48:464][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.05.27-07.18.48:468][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.05.27-07.18.48:525][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.05.27-07.18.48:525][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.05.27-07.18.48:566][  0]LogEditorDataStorage: Initializing
[2025.05.27-07.18.48:566][  0]LogEditorDataStorage: Initialized
[2025.05.27-07.18.48:567][  0]LogWindows: Attached monitors:
[2025.05.27-07.18.48:568][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY5'
[2025.05.27-07.18.48:568][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY1' [PRIMARY]
[2025.05.27-07.18.48:568][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY6'
[2025.05.27-07.18.48:568][  0]LogWindows: Found 3 attached monitors.
[2025.05.27-07.18.48:568][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.27-07.18.48:577][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.05.27-07.18.48:579][  0]SourceControl: Revision control is disabled
[2025.05.27-07.18.48:579][  0]LogUnrealEdMisc: Loading editor; pre map load, took 13.512
[2025.05.27-07.18.48:580][  0]Cmd: MAP LOAD FILE="H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/TestLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.05.27-07.18.48:581][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.05.27-07.18.48:581][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-07.18.48:602][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.05.27-07.18.48:623][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.05.27-07.18.48:624][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.54ms
[2025.05.27-07.18.48:630][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'TestLevel'.
[2025.05.27-07.18.48:630][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world TestLevel
[2025.05.27-07.18.48:631][  0]LogWorldPartition: ULevel::OnLevelLoaded(TestLevel)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.05.27-07.18.48:631][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.05.27-07.18.48:631][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.05.27-07.18.49:374][  0]LogAssetRegistry: Display: Asset registry cache written as 43.8 MiB to H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin
[2025.05.27-07.18.51:504][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.27-07.18.51:507][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.27-07.18.51:509][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.27-07.18.51:510][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.05.27-07.18.51:510][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.05.27-07.18.51:510][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.27-07.18.51:512][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.05.27-07.18.53:454][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.05.27-07.18.53:498][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.05.27-07.18.53:877][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-07.18.53:879][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.05.27-07.18.53:891][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.05.27-07.18.53:892][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.05.27-07.18.54:253][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-07.18.54:256][  0]LogSkeletalMesh: Built Skeletal Mesh [0.36s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.05.27-07.18.55:330][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-07.18.55:333][  0]LogSkeletalMesh: Built Skeletal Mesh [1.44s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.05.27-07.18.55:417][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.05.27-07.18.55:625][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.05.27-07.18.55:627][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-07.18.55:631][  0]LogSkeletalMesh: Built Skeletal Mesh [0.22s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.05.27-07.18.56:005][  0]LogWorldPartition: Display: WorldPartition initialize took 7.37 sec
[2025.05.27-07.18.56:078][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.05.27-07.19.00:615][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-07.19.00:630][  0]LogSkeletalMesh: Built Skeletal Mesh [5.00s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.05.27-07.19.01:242][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.05.27-07.19.01:455][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.05ms
[2025.05.27-07.19.01:456][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.05.27-07.19.01:457][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 1.403ms to complete.
[2025.05.27-07.19.01:465][  0]LogUnrealEdMisc: Total Editor Startup Time, took 26.398
[2025.05.27-07.19.01:636][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.05.27-07.19.01:710][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.27-07.19.01:763][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.27-07.19.01:821][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.27-07.19.01:871][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.27-07.19.01:902][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-07.19.01:902][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.05.27-07.19.01:902][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-07.19.01:902][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.05.27-07.19.01:902][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-07.19.01:902][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.05.27-07.19.01:903][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-07.19.01:903][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.05.27-07.19.01:903][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-07.19.01:904][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.05.27-07.19.01:904][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-07.19.01:904][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.05.27-07.19.01:904][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-07.19.01:904][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.05.27-07.19.01:904][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-07.19.01:904][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.05.27-07.19.01:904][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-07.19.01:904][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.05.27-07.19.01:905][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-07.19.01:905][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.05.27-07.19.01:951][  0]LogSlate: Took 0.000066 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.05.27-07.19.02:555][  0]LogSlate: External Image Picker: DecompressImage failed
[2025.05.27-07.19.02:770][  0]LogStall: Startup...
[2025.05.27-07.19.02:772][  0]LogStall: Startup complete.
[2025.05.27-07.19.02:777][  0]LogLoad: (Engine Initialization) Total time: 27.71 seconds
[2025.05.27-07.19.02:930][  0]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.05.27-07.19.02:947][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.05.27-07.19.02:949][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.05.27-07.19.02:950][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.05.27-07.19.02:950][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.27-07.19.03:013][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.27-07.19.03:013][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.05.27-07.19.03:014][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.05.27-07.19.03:014][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.05.27-07.19.03:014][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.27-07.19.03:071][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.27-07.19.03:071][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.05.27-07.19.03:127][  0]LogAssetRegistry: AssetRegistryGather time 0.0821s: AssetDataDiscovery 0.0140s, AssetDataGather 0.0136s, StoreResults 0.0546s. Wall time 25.0470s.
	NumCachedDirectories 0. NumUncachedDirectories 1854. NumCachedFiles 7965. NumUncachedFiles 0.
	BackgroundTickInterruptions 0.
[2025.05.27-07.19.03:142][  0]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.05.27-07.19.03:147][  0]LogSourceControl: Uncontrolled asset enumeration started...
[2025.05.27-07.19.03:274][  0]LogSourceControl: Uncontrolled asset enumeration finished in 0.126483 seconds (Found 7941 uncontrolled assets)
[2025.05.27-07.19.03:334][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.05.27-07.19.03:334][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.05.27-07.19.03:494][  0]LogSlate: Took 0.000109 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.05.27-07.19.03:497][  0]LogSlate: Took 0.000098 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.05.27-07.19.03:498][  0]LogSlate: Took 0.000065 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.05.27-07.19.03:547][  0]LogStreaming: Display: FlushAsyncLoading(511): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-07.19.03:577][  0]LogSlate: Took 0.000548 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.05.27-07.19.03:636][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-07.19.03:643][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.05.27-07.19.03:643][  0]LogFab: Display: Logging in using exchange code
[2025.05.27-07.19.03:643][  0]LogFab: Display: Reading exchange code from commandline
[2025.05.27-07.19.03:643][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.05.27-07.19.03:644][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.05.27-07.19.03:677][  0]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 19.78 ms. Compile time 14.73 ms, link time 4.53 ms.
[2025.05.27-07.19.03:709][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.05.27-07.19.03:719][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 75.676 ms
[2025.05.27-07.19.03:729][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BlenderLinkProjectEditor Win64 Development
[2025.05.27-07.19.03:999][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.05.27-07.19.04:480][  3]LogSlate: External Image Picker: DecompressImage failed
[2025.05.27-07.19.04:759][  5]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 17.680470
[2025.05.27-07.19.04:760][  5]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.05.27-07.19.04:760][  5]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 17.788074
[2025.05.27-07.19.05:230][ 10]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.27-07.19.05:811][ 19]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 18.784601
[2025.05.27-07.19.05:812][ 19]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 607272702
[2025.05.27-07.19.05:812][ 19]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 18.784601, Update Interval: 335.913574
[2025.05.27-07.19.12:879][116]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.19.22:954][252]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.19.32:889][436]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.19.42:888][636]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.19.52:892][236]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.20.02:897][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.20.05:582][380]LogRenderer: Display: Invalidated clipmap due to WPO threshold change. This can occur due to resolution or FOV changes.
[2025.05.27-07.20.12:982][106]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.20.22:995][258]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.20.28:360][353]LogSlate: Window 'Editor Preferences' being destroyed
[2025.05.27-07.20.28:495][353]LogSlate: Window 'Editor Preferences' being destroyed
[2025.05.27-07.20.32:958][765]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.20.37:150][181]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.05.27-07.20.42:961][758]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.20.55:119][727]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.21.05:121][531]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.21.15:121][561]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.21.25:124][595]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.21.35:221][980]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.21.42:003][ 89]Cmd: Interchange.FeatureFlags.Import.FBX False
[2025.05.27-07.21.42:088][ 89]Interchange.FeatureFlags.Import.FBX = "false"
[2025.05.27-07.21.45:152][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.21.47:072][432]Cmd: Interchange.FeatureFlags.Import.FBX False
[2025.05.27-07.21.47:073][432]Interchange.FeatureFlags.Import.FBX = "false"
[2025.05.27-07.21.53:661][ 64]LogStreaming: Display: FlushAsyncLoading(512): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-07.21.53:661][ 64]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/MetaHumans/Export/custom_head_PhysicsAsset (0xAC7400E680B3670A) - The package to load does not exist on disk or in the loader
[2025.05.27-07.21.53:661][ 64]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/MetaHumans/Export/custom_head_PhysicsAsset'
[2025.05.27-07.21.53:908][ 64]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/Documents/MetaHuman_Export/custom_head.fbx)
[2025.05.27-07.21.54:104][ 64]LogFbx: Loading FBX Scene from C:/Users/<USER>/Documents/MetaHuman_Export/custom_head.fbx
[2025.05.27-07.22.02:178][ 64]LogFbx: FBX Scene Loaded Succesfully
[2025.05.27-07.22.02:218][ 64]LogFbx: Warning: Not valid bind pose for Pose (head_lod0_mesh.001) - Node custom_head_lod0_mesh : Not all the deformers are stored in this BindPose
[2025.05.27-07.22.02:225][ 64]LogFbx: Warning: Not valid bind pose for Pose (head_lod0_mesh.001) - Node custom_head_lod0_mesh : The relative matrices do not match
[2025.05.27-07.22.02:225][ 64]LogFbx: Warning: Getting valid bind pose failed. Try to recreate bind pose
[2025.05.27-07.22.02:239][ 64]LogFbx: Valid bind pose for Pose (FbxSDKBindPose) - custom_head_lod0_mesh
[2025.05.27-07.22.02:240][ 64]LogFbx: Warning: Recreating bind pose succeeded.
[2025.05.27-07.22.02:273][ 64]LogMaterial: Display: Material /Game/MetaHumans/Export/MH_Friend_head_shader.MH_Friend_head_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.27-07.22.02:354][ 64]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.27-07.22.02:454][ 64]LogTexture: Display: Building textures: /Game/MetaHumans/Export/head_roughness_map.head_roughness_map (TFO_AutoDXT, 4096x4096 x1x1x1) (Required Memory Estimate: 1120.999983 MB), EncodeSpeed: Fast
[2025.05.27-07.22.02:589][ 64]LogFbx: Triangulating skeletal mesh custom_head_lod0_mesh
[2025.05.27-07.22.07:652][ 64]LogFbx: Bones digested - 875  Depth of hierarchy - 18
[2025.05.27-07.22.08:007][ 64]LogSkeletalMesh: Display: /Game/MetaHumans/Export/custom_head.custom_head ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.27-07.22.08:657][ 64]LogSkeletalMesh: Section 0: Material=0, 48004 triangles
[2025.05.27-07.22.08:739][ 64]LogSkeletalMesh: Building Skeletal Mesh custom_head...
[2025.05.27-07.22.08:783][ 64]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (custom_head) ...
[2025.05.27-07.22.08:815][ 64]LogSkeletalMesh: Display: /Game/MetaHumans/Export/custom_head.custom_head ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.27-07.22.09:490][ 64]LogSkeletalMesh: Built Skeletal Mesh [0.75s] /Game/MetaHumans/Export/custom_head.custom_head
[2025.05.27-07.22.09:512][ 64]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.05.27-07.22.09:586][ 64]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.05.27-07.22.09:586][ 64]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-07.22.12:859][ 64]LogSkeletalMesh: Building Skeletal Mesh custom_head...
[2025.05.27-07.22.12:875][ 64]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (custom_head) ...
[2025.05.27-07.22.13:201][ 64]LogSkeletalMesh: Display: /Game/MetaHumans/Export/custom_head.custom_head ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.27-07.22.16:988][ 64]LogSkeletalMesh: Built Skeletal Mesh [4.13s] /Game/MetaHumans/Export/custom_head.custom_head
[2025.05.27-07.22.17:306][ 65]LogShaderCompilers: Display: ================================================
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: === FShaderJobCache stats ===
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Total job queries 352, among them cache hits 26 (7.39%), DDC hits 302 (85.80%), Duplicates 5 (1.42%)
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Tracking 321 distinct input hashes that result in 260 distinct outputs (81.00%)
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: RAM used: 161.72 KiB of 3.20 GiB budget. Usage: 0.00%
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: === Shader Compilation stats ===
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Shaders Compiled: 19
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Jobs assigned 19, completed 19 (100%)
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Time job spent in pending queue: average 0.02 s, longest 0.02 s
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Job execution time: average 3.30 s, max 3.68 s
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Job life time (pending + execution): average 3.32 s, max 3.70
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Shader code size: average 8.432 KiB, min 4.121 KiB, max 10.91 KiB
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Time at least one job was in flight (either pending or executed): 3.71 s
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Mutex wait stall in FShaderJobCache::SubmitJobs:  0.33%
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Jobs were issued in 10 batches (only local compilation was used), average 1.90 jobs/batch
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Average processing rate: 5.13 jobs/sec
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Total thread time: 12.286 s
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Total thread preprocess time: 6.522 s
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Percentage time preprocessing: 53.08%
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Effective parallelization: 3.32 (times faster than compiling all shaders on one thread). Compare with number of workers: 16
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Top 5 most expensive shader types by average time:
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display:                                             FDebugViewModePS (compiled    2 times, average 1.30 sec, max 1.37 sec, min 1.24 sec)
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display:                                TMaterialCHSFNoLightMapPolicy (compiled    1 times, average 1.27 sec, max 1.27 sec, min 1.27 sec)
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display:                                      FPathTracingMaterialCHS (compiled    1 times, average 1.15 sec, max 1.15 sec, min 1.15 sec)
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy (compiled    3 times, average 0.67 sec, max 0.71 sec, min 0.60 sec)
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display:                         TBasePassPSFNoLightMapPolicySkylight (compiled    3 times, average 0.57 sec, max 0.62 sec, min 0.51 sec)
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Top 5 shader types by total compile time:
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display:                                             FDebugViewModePS - 21.23% of total time (compiled    2 times, average 1.30 sec, max 1.37 sec, min 1.24 sec)
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy - 16.29% of total time (compiled    3 times, average 0.67 sec, max 0.71 sec, min 0.60 sec)
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display:                         TBasePassPSFNoLightMapPolicySkylight - 13.86% of total time (compiled    3 times, average 0.57 sec, max 0.62 sec, min 0.51 sec)
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display:                                             FDebugViewModeVS - 11.04% of total time (compiled    3 times, average 0.45 sec, max 0.45 sec, min 0.45 sec)
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display:                                TMaterialCHSFNoLightMapPolicy - 10.31% of total time (compiled    1 times, average 1.27 sec, max 1.27 sec, min 1.27 sec)
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: === Material stats ===
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Materials Cooked:        0
[2025.05.27-07.22.17:307][ 65]LogShaderCompilers: Display: Materials Translated:    162
[2025.05.27-07.22.17:308][ 65]LogShaderCompilers: Display: Material Total Translate Time: 0.07 s
[2025.05.27-07.22.17:308][ 65]LogShaderCompilers: Display: Material Translation Only: 0.04 s (60%)
[2025.05.27-07.22.17:308][ 65]LogShaderCompilers: Display: Material DDC Serialization Only: 0.00 s (5%)
[2025.05.27-07.22.17:308][ 65]LogShaderCompilers: Display: Material Cache Hits: 51 (31%)
[2025.05.27-07.22.17:308][ 65]LogShaderCompilers: Display: ================================================
[2025.05.27-07.22.17:394][ 65]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.05.27-07.22.17:405][ 65]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.22.27:324][ 16]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.22.37:324][ 13]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.22.47:332][ 10]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.22.57:333][  5]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.23.07:337][  1]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.23.17:398][753]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.23.22:862][900]LogSlate: Warning: Unable to rasterize '../../../Engine/Content/Editor/Slate/Starship/AssetIcons/SkeletalMesh_64.svg'. File could not be found
[2025.05.27-07.23.24:621][963]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.05.27-07.23.24:641][964]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.05.27-07.23.24:714][966]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.05.27-07.23.27:360][232]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.23.35:355][975]LogActorFactory: Actor Factory attempting to spawn SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head
[2025.05.27-07.23.35:355][975]LogActorFactory: Actor Factory attempting to spawn SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head
[2025.05.27-07.23.35:355][975]LogActorFactory: Actor Factory spawned SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head as actor: SkeletalMeshActor /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.SkeletalMeshActor_0
[2025.05.27-07.23.35:356][975]LogActorFactory: Actor Factory spawned SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head as actor: SkeletalMeshActor /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.SkeletalMeshActor_0
[2025.05.27-07.23.36:231][ 34]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.05.27-07.23.36:232][ 34]LogEditorActor: Deleted 0 Actors (0.015 secs)
[2025.05.27-07.23.36:696][ 44]LogActorFactory: Actor Factory attempting to spawn SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head
[2025.05.27-07.23.36:696][ 44]LogActorFactory: Actor Factory attempting to spawn SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head
[2025.05.27-07.23.36:696][ 44]LogActorFactory: Actor Factory spawned SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head as actor: SkeletalMeshActor /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.SkeletalMeshActor_1
[2025.05.27-07.23.36:697][ 44]LogActorFactory: Actor Factory spawned SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head as actor: SkeletalMeshActor /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.SkeletalMeshActor_1
[2025.05.27-07.23.37:252][105]LogUObjectHash: Compacting FUObjectHashTables data took   0.36ms
[2025.05.27-07.23.37:254][105]LogEditorActor: Deleted 0 Actors (0.015 secs)
[2025.05.27-07.23.37:260][105]LogActorFactory: Actor Factory attempting to spawn SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head
[2025.05.27-07.23.37:260][105]LogActorFactory: Actor Factory attempting to spawn SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head
[2025.05.27-07.23.37:276][105]LogActorFactory: Actor Factory spawned SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head as actor: SkeletalMeshActor /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.SkeletalMeshActor_UAID_00155DE42E5C536C02_1268125151
[2025.05.27-07.23.37:277][105]LogActorFactory: Actor Factory spawned SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head as actor: SkeletalMeshActor /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.SkeletalMeshActor_UAID_00155DE42E5C536C02_1268125151
[2025.05.27-07.23.37:497][106]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.23.47:447][ 36]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.23.47:680][ 50]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.05.27-07.23.57:451][782]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.23.59:293][983]Cmd: DELETE
[2025.05.27-07.23.59:293][983]Cmd: ACTOR DELETE
[2025.05.27-07.24.01:144][983]LogSlate: Window 'Delete Referenced Actor' being destroyed
[2025.05.27-07.24.01:165][983]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.27-07.24.01:169][983]LogEditorActor: Deleted 0 Actors (0.056 secs)
[2025.05.27-07.24.05:037][401]Cmd: DELETE
[2025.05.27-07.24.05:037][401]Cmd: ACTOR DELETE
[2025.05.27-07.24.14:048][401]LogSlate: Window 'Delete Referenced Actor' being destroyed
[2025.05.27-07.24.14:068][401]LogUObjectHash: Compacting FUObjectHashTables data took   0.36ms
[2025.05.27-07.24.14:070][401]LogEditorActor: Deleted 0 Actors (0.018 secs)
[2025.05.27-07.24.14:087][402]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.24.22:014][ 65]LogRenderer: Display: Invalidated clipmap due to WPO threshold change. This can occur due to resolution or FOV changes.
[2025.05.27-07.24.24:101][231]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.24.33:556][973]Cmd: TRANSACTION UNDO
[2025.05.27-07.24.33:556][973]LogEditorTransaction: Undo Set Location
[2025.05.27-07.24.34:148][980]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.24.40:458][317]Cmd: TRANSACTION UNDO
[2025.05.27-07.24.40:458][317]LogEditorTransaction: Undo Set Location
[2025.05.27-07.24.41:748][339]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 354.777100
[2025.05.27-07.24.42:154][347]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-07.24.42:154][347]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 355.132965, Update Interval: 346.057922
[2025.05.27-07.24.44:264][378]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.24.46:019][442]LogAssetEditorSubsystem: Opening Asset editor for LevelSequence /Game/MetaHumans/Test/Test.Test
[2025.05.27-07.24.46:102][442]LogStreaming: Display: FlushAsyncLoading(525): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-07.24.49:688][618]LogRenderer: Display: Invalidated clipmap due to WPO threshold change. This can occur due to resolution or FOV changes.
[2025.05.27-07.24.49:751][619]LogStreaming: Display: FlushAsyncLoading(526): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-07.24.49:808][619]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_Hexagon_1mm) ...
[2025.05.27-07.24.49:850][619]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_Arrow_1mm) ...
[2025.05.27-07.24.49:928][619]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_Square_3mm) ...
[2025.05.27-07.24.49:990][619]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_Octagon_1mm) ...
[2025.05.27-07.24.50:064][619]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_Circle_3mm) ...
[2025.05.27-07.24.50:129][619]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_Box_3mm) ...
[2025.05.27-07.24.50:195][619]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_Sphere_solid) ...
[2025.05.27-07.24.50:251][619]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_RoundedSquare_3mm) ...
[2025.05.27-07.24.50:310][619]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_RoundedSquare_1mm) ...
[2025.05.27-07.24.50:362][619]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_Arrow_solid) ...
[2025.05.27-07.24.50:417][619]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_Arrow_3mm) ...
[2025.05.27-07.24.50:452][619]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_Arrow4_3mm) ...
[2025.05.27-07.24.50:501][619]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_Sphere_3mm) ...
[2025.05.27-07.24.50:575][619]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_Arrow4_1mm) ...
[2025.05.27-07.24.50:628][619]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_Box_1mm) ...
[2025.05.27-07.24.54:267][785]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.25.04:288][173]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.25.07:691][281]LogStreaming: Display: FlushAsyncLoading(546): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-07.25.08:144][281]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.05.27-07.25.08:172][281]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.6.dll
[2025.05.27-07.25.08:191][281]LogSkeletalMesh: Building Skeletal Mesh SKM_Face_Preview...
[2025.05.27-07.25.08:208][281]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (SKM_Face_Preview) ...
[2025.05.27-07.25.08:397][281]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Face/SKM_Face_Preview.SKM_Face_Preview]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-07.25.08:399][281]LogSkeletalMesh: Built Skeletal Mesh [0.21s] /Game/MetaHumans/Common/Face/SKM_Face_Preview.SKM_Face_Preview
[2025.05.27-07.25.08:416][281]LogActorFactory: Actor Factory attempting to spawn AnimSequence /Game/MetaHumans/Test/Face_Archetype_Skeleton_Sequence.Face_Archetype_Skeleton_Sequence
[2025.05.27-07.25.08:416][281]LogActorFactory: Actor Factory attempting to spawn AnimSequence /Game/MetaHumans/Test/Face_Archetype_Skeleton_Sequence.Face_Archetype_Skeleton_Sequence
[2025.05.27-07.25.08:417][281]LogActorFactory: Actor Factory spawned SkeletalMesh /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh as actor: SkeletalMeshActor /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.SkeletalMeshActor_2
[2025.05.27-07.25.08:442][281]LogActorFactory: Actor Factory spawned SkeletalMesh /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh as actor: SkeletalMeshActor /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.SkeletalMeshActor_2
[2025.05.27-07.25.08:485][281]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.05.27-07.25.09:323][315]LogUObjectHash: Compacting FUObjectHashTables data took   0.84ms
[2025.05.27-07.25.09:327][315]LogEditorActor: Deleted 0 Actors (0.115 secs)
[2025.05.27-07.25.13:358][401]LogActorFactory: Actor Factory attempting to spawn AnimSequence /Game/MetaHumans/Test/Face_Archetype_Skeleton_Sequence.Face_Archetype_Skeleton_Sequence
[2025.05.27-07.25.13:359][401]LogActorFactory: Actor Factory spawned SkeletalMesh /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh as actor: SkeletalMeshActor /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.Face_Archetype_Skeleton_Sequence_0
[2025.05.27-07.25.14:299][410]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: ================================================
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: === FShaderJobCache stats ===
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Total job queries 404, among them cache hits 41 (10.15%), DDC hits 334 (82.67%), Duplicates 5 (1.24%)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Tracking 358 distinct input hashes that result in 292 distinct outputs (81.56%)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: RAM used: 173.16 KiB of 3.20 GiB budget. Usage: 0.01%
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: === Shader Compilation stats ===
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Shaders Compiled: 24
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Jobs assigned 24, completed 24 (100%)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Average time worker was idle: 77.40 s
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Time job spent in pending queue: average 0.04 s, longest 0.11 s
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Job execution time: average 2.85 s, max 3.68 s
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Job life time (pending + execution): average 2.89 s, max 3.70
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Shader code size: average 8.303 KiB, min 4.121 KiB, max 11.5 KiB
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Time at least one job was in flight (either pending or executed): 5.33 s
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Mutex wait stall in FShaderJobCache::SubmitJobs:  0.31%
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Jobs were issued in 15 batches (only local compilation was used), average 1.60 jobs/batch
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Average processing rate: 4.50 jobs/sec
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Total thread time: 14.065 s
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Total thread preprocess time: 7.095 s
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Percentage time preprocessing: 50.44%
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Effective parallelization: 2.64 (times faster than compiling all shaders on one thread). Compare with number of workers: 16
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Top 5 most expensive shader types by average time:
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display:                                             FDebugViewModePS (compiled    2 times, average 1.30 sec, max 1.37 sec, min 1.24 sec)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display:                                      FPathTracingMaterialCHS (compiled    1 times, average 1.15 sec, max 1.15 sec, min 1.15 sec)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display:                                TMaterialCHSFNoLightMapPolicy (compiled    2 times, average 0.99 sec, max 1.27 sec, min 0.72 sec)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy (compiled    4 times, average 0.59 sec, max 0.71 sec, min 0.35 sec)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display:                         TBasePassPSFNoLightMapPolicySkylight (compiled    3 times, average 0.57 sec, max 0.62 sec, min 0.51 sec)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Top 5 shader types by total compile time:
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display:                                             FDebugViewModePS - 18.55% of total time (compiled    2 times, average 1.30 sec, max 1.37 sec, min 1.24 sec)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy - 16.75% of total time (compiled    4 times, average 0.59 sec, max 0.71 sec, min 0.35 sec)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display:                                TMaterialCHSFNoLightMapPolicy - 14.11% of total time (compiled    2 times, average 0.99 sec, max 1.27 sec, min 0.72 sec)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display:                         TBasePassPSFNoLightMapPolicySkylight - 12.11% of total time (compiled    3 times, average 0.57 sec, max 0.62 sec, min 0.51 sec)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display:                                             FDebugViewModeVS - 9.65% of total time (compiled    3 times, average 0.45 sec, max 0.45 sec, min 0.45 sec)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: === Material stats ===
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Materials Cooked:        0
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Materials Translated:    167
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Material Total Translate Time: 0.09 s
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Material Translation Only: 0.04 s (48%)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Material DDC Serialization Only: 0.00 s (4%)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: Material Cache Hits: 54 (32%)
[2025.05.27-07.25.17:329][552]LogShaderCompilers: Display: ================================================
[2025.05.27-07.25.24:313][867]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.25.29:755][126]Cmd: TRANSACTION UNDO
[2025.05.27-07.25.29:755][126]LogEditorTransaction: Undo Drop Assets
[2025.05.27-07.25.30:066][126]LogOutputDevice: Warning: 

Script Stack (0 frames) :

[2025.05.27-07.25.30:144][126]LogStats: FPlatformStackWalk::StackWalkAndDump -  0.077 s
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: === Handled ensure: ===
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: 
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: Ensure condition failed: !bRegistered || GetAttachParent()->GetAttachChildren().Contains(this)  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\Engine\Private\Components\SceneComponent.cpp] [Line: 2375] 
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: Attempt to detach SceneComponent 'SceneComponent0' owned by 'ControlRigShapeActor_620' from AttachParent 'SkeletalMeshComponent0' while not attached.
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: Stack: 
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffefb6ae0a7 UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef9708545 UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00000283d2cede13 UnrealEditor-ControlRigEditor.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00000283d2cedb51 UnrealEditor-ControlRigEditor.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00000283d2d2c10c UnrealEditor-ControlRigEditor.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef60a3a70 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef60c9021 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef60ed166 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef60a2a61 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef60d16b4 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef60b8e85 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef617cf28 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef6101b12 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef60b84a2 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef60b40d6 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffefe2c4b56 UnrealEditor-Core.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffefaeb4b84 UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef6d5f792 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00000283b8e94ee1 UnrealEditor-MainFrame.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00000283b8e68ac2 UnrealEditor-MainFrame.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00000283b8e95e41 UnrealEditor-MainFrame.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef82973a5 UnrealEditor-Slate.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef827afd7 UnrealEditor-Slate.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef82dfb2b UnrealEditor-Slate.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00000283b8ea461e UnrealEditor-MainFrame.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00000283b8e94c35 UnrealEditor-MainFrame.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef820b6a3 UnrealEditor-Slate.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007ffef81ffcd2 UnrealEditor-Slate.dll!UnknownFunction []
[2025.05.27-07.25.30:144][126]LogOutputDevice: Error: [Callstack] 0x00007fffafffa625 UnrealEditor-ApplicationCore.dll!UnknownFunction []
[2025.05.27-07.25.30:145][126]LogOutputDevice: Error: [Callstack] 0x00007fffaffe5786 UnrealEditor-ApplicationCore.dll!UnknownFunction []
[2025.05.27-07.25.30:145][126]LogOutputDevice: Error: [Callstack] 0x00007fffafffd85e UnrealEditor-ApplicationCore.dll!UnknownFunction []
[2025.05.27-07.25.30:145][126]LogOutputDevice: Error: [Callstack] 0x00007fffaffe05b7 UnrealEditor-ApplicationCore.dll!UnknownFunction []
[2025.05.27-07.25.30:145][126]LogOutputDevice: Error: [Callstack] 0x00007ff805f983f1 USER32.dll!UnknownFunction []
[2025.05.27-07.25.30:145][126]LogOutputDevice: Error: [Callstack] 0x00007ff805f97eb1 USER32.dll!UnknownFunction []
[2025.05.27-07.25.30:145][126]LogOutputDevice: Error: [Callstack] 0x00007fffb001bb26 UnrealEditor-ApplicationCore.dll!UnknownFunction []
[2025.05.27-07.25.30:145][126]LogOutputDevice: Error: [Callstack] 0x00007ff7927e632d UnrealEditor.exe!UnknownFunction []
[2025.05.27-07.25.30:145][126]LogOutputDevice: Error: [Callstack] 0x00007ff7928057ac UnrealEditor.exe!UnknownFunction []
[2025.05.27-07.25.30:145][126]LogOutputDevice: Error: [Callstack] 0x00007ff79280589a UnrealEditor.exe!UnknownFunction []
[2025.05.27-07.25.30:145][126]LogOutputDevice: Error: [Callstack] 0x00007ff792809114 UnrealEditor.exe!UnknownFunction []
[2025.05.27-07.25.30:145][126]LogOutputDevice: Error: [Callstack] 0x00007ff79281bd04 UnrealEditor.exe!UnknownFunction []
[2025.05.27-07.25.30:145][126]LogOutputDevice: Error: [Callstack] 0x00007ff79281f0ba UnrealEditor.exe!UnknownFunction []
[2025.05.27-07.25.30:145][126]LogOutputDevice: Error: [Callstack] 0x00007ff806c2259d KERNEL32.DLL!UnknownFunction []
[2025.05.27-07.25.30:145][126]LogOutputDevice: Error: [Callstack] 0x00007ff807eeaf38 ntdll.dll!UnknownFunction []
[2025.05.27-07.25.30:145][126]LogOutputDevice: Error: 
[2025.05.27-07.25.30:151][126]LogStats:                SubmitErrorReport -  0.000 s
[2025.05.27-07.25.31:107][126]LogStats:                    SendNewReport -  0.956 s
[2025.05.27-07.25.31:107][126]LogStats:             FDebug::EnsureFailed -  1.040 s
[2025.05.27-07.25.34:407][175]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.25.41:775][392]LogSlate: Took 0.000089 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.05.27-07.25.44:450][427]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.25.54:388][692]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.25.56:332][779]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head
[2025.05.27-07.25.56:352][779]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.05.27-07.25.56:387][779]LogStreaming: Display: FlushAsyncLoading(548): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-07.25.56:432][779]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_7:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.27-07.26.04:441][903]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.26.14:465][ 55]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.26.16:675][ 86]LogAssetEditorSubsystem: Opening Asset editor for Skeleton /Game/MetaHumans/Export/custom_head_Skeleton.custom_head_Skeleton
[2025.05.27-07.26.16:676][ 86]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.05.27-07.26.16:731][ 86]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.27-07.26.17:005][ 86]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.05.27-07.26.17:245][ 86]LogSlate: Took 0.000094 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/FontAwesome.ttf' (139K)
[2025.05.27-07.26.22:103][160]LogWorld: UWorld::CleanupWorld for World_8, bSessionEnded=true, bCleanupResources=true
[2025.05.27-07.26.22:103][160]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-07.26.22:130][160]LogWorld: UWorld::CleanupWorld for World_9, bSessionEnded=true, bCleanupResources=true
[2025.05.27-07.26.22:130][160]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-07.26.22:321][160]LogUObjectHash: Compacting FUObjectHashTables data took   0.90ms
[2025.05.27-07.26.24:489][198]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.26.25:581][216]LogSlate: Window 'custom_head' being destroyed
[2025.05.27-07.26.25:607][216]LogWorld: UWorld::CleanupWorld for World_7, bSessionEnded=true, bCleanupResources=true
[2025.05.27-07.26.25:607][216]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-07.26.25:664][216]LogUObjectHash: Compacting FUObjectHashTables data took   0.73ms
[2025.05.27-07.26.30:609][447]LogAssetEditorSubsystem: Opening Asset editor for Skeleton /Game/MetaHumans/Export/custom_head_Skeleton.custom_head_Skeleton
[2025.05.27-07.26.30:609][447]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.05.27-07.26.30:742][447]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_10:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.27-07.26.31:021][447]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.05.27-07.26.34:623][496]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.26.44:583][662]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.26.48:425][730]LogSlate: Window 'custom_head_Skeleton' being destroyed
[2025.05.27-07.26.48:464][730]LogWorld: UWorld::CleanupWorld for World_10, bSessionEnded=true, bCleanupResources=true
[2025.05.27-07.26.48:464][730]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-07.26.48:494][730]LogWorld: UWorld::CleanupWorld for World_11, bSessionEnded=true, bCleanupResources=true
[2025.05.27-07.26.48:494][730]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-07.26.48:537][730]LogUObjectHash: Compacting FUObjectHashTables data took   0.86ms
[2025.05.27-07.26.54:567][ 29]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.26.57:053][132]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.05.27-07.26.59:072][132]LogSlate: Window 'Delete Assets' being destroyed
[2025.05.27-07.27.03:277][212]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head
[2025.05.27-07.27.03:277][212]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
[2025.05.27-07.27.03:343][212]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_12:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.27-07.27.04:616][225]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.27.10:091][311]LogAssetEditorSubsystem: Opening Asset editor for Skeleton /Game/MetaHumans/Export/custom_head_Skeleton.custom_head_Skeleton
[2025.05.27-07.27.10:092][311]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
[2025.05.27-07.27.10:147][311]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_13:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.27-07.27.10:377][311]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
[2025.05.27-07.27.14:718][365]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.27.24:708][548]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.27.34:737][686]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.27.44:751][841]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.27.54:848][987]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.28.04:867][117]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.28.14:876][271]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.28.24:900][433]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.28.34:929][605]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.28.37:828][644]LogWorld: UWorld::CleanupWorld for World_12, bSessionEnded=true, bCleanupResources=true
[2025.05.27-07.28.37:828][644]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-07.28.37:915][644]LogUObjectHash: Compacting FUObjectHashTables data took   0.91ms
[2025.05.27-07.28.38:816][656]LogSlate: Window 'custom_head_Skeleton' being destroyed
[2025.05.27-07.28.38:854][656]LogWorld: UWorld::CleanupWorld for World_13, bSessionEnded=true, bCleanupResources=true
[2025.05.27-07.28.38:854][656]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-07.28.38:884][656]LogWorld: UWorld::CleanupWorld for World_14, bSessionEnded=true, bCleanupResources=true
[2025.05.27-07.28.38:884][656]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-07.28.38:910][656]LogUObjectHash: Compacting FUObjectHashTables data took   0.87ms
[2025.05.27-07.28.44:946][857]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.29.05:630][ 34]LogSlate: Window 'Choose Skeleton' being destroyed
[2025.05.27-07.29.05:746][ 35]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.29.12:315][231]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head
[2025.05.27-07.29.12:315][231]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_15
[2025.05.27-07.29.12:387][231]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_15:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.27-07.29.15:416][272]LogAssetEditorSubsystem: Opening Asset editor for Skeleton /Game/MetaHumans/Common/Face/Face_Archetype_Skeleton.Face_Archetype_Skeleton
[2025.05.27-07.29.15:417][272]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_16
[2025.05.27-07.29.15:518][272]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_16:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.27-07.29.15:756][272]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_17
[2025.05.27-07.29.15:984][272]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_18
[2025.05.27-07.29.16:104][273]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.29.20:375][338]LogSlate: Window 'Face_Archetype_Skeleton' being destroyed
[2025.05.27-07.29.20:384][338]LogSlate: Window 'Face_Archetype_Skeleton' being destroyed
[2025.05.27-07.29.20:405][338]LogWorld: UWorld::CleanupWorld for World_15, bSessionEnded=true, bCleanupResources=true
[2025.05.27-07.29.20:405][338]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-07.29.20:480][338]LogWorld: UWorld::CleanupWorld for World_16, bSessionEnded=true, bCleanupResources=true
[2025.05.27-07.29.20:481][338]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-07.29.20:515][338]LogWorld: UWorld::CleanupWorld for World_17, bSessionEnded=true, bCleanupResources=true
[2025.05.27-07.29.20:515][338]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-07.29.20:585][338]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.05.27-07.29.26:177][527]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.29.36:215][859]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.29.37:238][872]LogMaterial: Display: Material /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.SkeletalMeshActor_UAID_00155DE42E5C536C02_1268125151.SkeletalMeshComponent0.MID_MH_Friend_head_shader_0 needed to have new flag set bUsedWithMorphTargets !
[2025.05.27-07.29.43:987][216]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.05.27-07.29.45:014][271]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.05.27-07.29.45:167][279]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.05.27-07.29.45:300][285]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.05.27-07.29.45:855][313]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.05.27-07.29.46:171][330]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.29.56:189][863]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.30.06:194][377]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.30.16:106][671]Cmd: TRANSACTION UNDO
[2025.05.27-07.30.16:106][671]LogEditorTransaction: Undo Add Animation
[2025.05.27-07.30.16:226][672]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.30.26:219][ 59]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.30.31:919][360]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 704.950928
[2025.05.27-07.30.32:223][376]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-07.30.32:223][376]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 705.235657, Update Interval: 335.922729
[2025.05.27-07.30.36:227][586]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.30.43:440][976]Cmd: TRANSACTION UNDO
[2025.05.27-07.30.43:440][976]LogEditorTransaction: Undo Add Animation
[2025.05.27-07.30.46:314][ 18]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.30.50:594][204]Cmd: TRANSACTION UNDO
[2025.05.27-07.30.50:594][204]LogEditorTransaction: Undo Add Animation
[2025.05.27-07.30.56:322][385]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.31.06:391][866]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.31.06:818][872]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_19
[2025.05.27-07.31.07:731][883]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_20
[2025.05.27-07.31.08:107][888]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_21
[2025.05.27-07.31.08:454][893]LogStreaming: Display: FlushAsyncLoading(553): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-07.31.16:351][163]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: ================================================
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: === FShaderJobCache stats ===
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Total job queries 454, among them cache hits 55 (12.11%), DDC hits 355 (78.19%), Duplicates 6 (1.32%)
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Tracking 393 distinct input hashes that result in 311 distinct outputs (79.13%)
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: RAM used: 179.42 KiB of 3.20 GiB budget. Usage: 0.01%
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: === Shader Compilation stats ===
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Shaders Compiled: 38
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Jobs assigned 38, completed 38 (100%)
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Average time worker was idle: 300.74 s
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Time job spent in pending queue: average 0.03 s, longest 0.11 s
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Job execution time: average 2.68 s, max 3.68 s
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Job life time (pending + execution): average 2.71 s, max 3.70
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Shader code size: average 8.828 KiB, min 4.121 KiB, max 12.784 KiB
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Time at least one job was in flight (either pending or executed): 8.31 s
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Mutex wait stall in FShaderJobCache::SubmitJobs:  0.29%
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Jobs were issued in 29 batches (only local compilation was used), average 1.31 jobs/batch
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Average processing rate: 4.57 jobs/sec
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Total thread time: 22.39 s
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Total thread preprocess time: 7.659 s
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Percentage time preprocessing: 34.21%
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Effective parallelization: 2.70 (times faster than compiling all shaders on one thread). Compare with number of workers: 16
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display: Top 5 most expensive shader types by average time:
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display:                                             FDebugViewModePS (compiled    4 times, average 1.24 sec, max 1.37 sec, min 1.17 sec)
[2025.05.27-07.31.17:513][214]LogShaderCompilers: Display:                                      FPathTracingMaterialCHS (compiled    2 times, average 1.03 sec, max 1.15 sec, min 0.90 sec)
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display:                                TMaterialCHSFNoLightMapPolicy (compiled    2 times, average 0.99 sec, max 1.27 sec, min 0.72 sec)
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy (compiled    6 times, average 0.58 sec, max 0.71 sec, min 0.35 sec)
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display:                         TBasePassPSFNoLightMapPolicySkylight (compiled    6 times, average 0.56 sec, max 0.62 sec, min 0.51 sec)
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display: Top 5 shader types by total compile time:
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display:                                             FDebugViewModePS - 22.13% of total time (compiled    4 times, average 1.24 sec, max 1.37 sec, min 1.17 sec)
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy - 15.61% of total time (compiled    6 times, average 0.58 sec, max 0.71 sec, min 0.35 sec)
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display:                         TBasePassPSFNoLightMapPolicySkylight - 14.98% of total time (compiled    6 times, average 0.56 sec, max 0.62 sec, min 0.51 sec)
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display:                                             FDebugViewModeVS - 11.16% of total time (compiled    6 times, average 0.42 sec, max 0.45 sec, min 0.37 sec)
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display:                                      FPathTracingMaterialCHS - 9.16% of total time (compiled    2 times, average 1.03 sec, max 1.15 sec, min 0.90 sec)
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display: === Material stats ===
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display: Materials Cooked:        0
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display: Materials Translated:    172
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display: Material Total Translate Time: 0.10 s
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display: Material Translation Only: 0.04 s (46%)
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display: Material DDC Serialization Only: 0.00 s (3%)
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display: Material Cache Hits: 54 (31%)
[2025.05.27-07.31.17:514][214]LogShaderCompilers: Display: ================================================
[2025.05.27-07.31.26:365][598]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.31.36:382][ 30]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.31.39:333][121]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_22
[2025.05.27-07.31.46:381][262]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.31.56:401][723]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.32.06:403][153]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.32.16:485][385]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.32.26:411][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.32.36:413][ 55]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.32.38:307][128]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head
[2025.05.27-07.32.38:307][128]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_23
[2025.05.27-07.32.38:379][128]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_23:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.27-07.32.46:502][263]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.32.56:504][442]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.33.02:488][549]Cmd: TRANSACTION UNDO
[2025.05.27-07.33.02:489][549]LogEditorTransaction: Undo Select None
[2025.05.27-07.33.02:710][549]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_23:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.27-07.33.06:580][598]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.33.10:787][671]LogTemp: Warning: UI Min (1.0) >= UI Max (.01) for Ranged Numeric property /Script/Engine.PostProcessSettings:LumenDiffuseColorBoost
[2025.05.27-07.33.11:937][692]LogTemp: Warning: UI Min (1.0) >= UI Max (.01) for Ranged Numeric property /Script/Engine.PostProcessSettings:LumenDiffuseColorBoost
[2025.05.27-07.33.16:347][766]LogAssetEditorSubsystem: Opening Asset editor for Skeleton /Game/MetaHumans/Common/Face/Face_Archetype_Skeleton.Face_Archetype_Skeleton
[2025.05.27-07.33.16:347][766]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_24
[2025.05.27-07.33.16:443][766]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_24:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.27-07.33.16:709][766]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_25
[2025.05.27-07.33.17:053][767]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.33.27:107][940]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.33.28:040][957]LogTemp: Warning: UI Min (1.0) >= UI Max (.01) for Ranged Numeric property /Script/Engine.PostProcessSettings:LumenDiffuseColorBoost
[2025.05.27-07.33.37:166][ 93]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.33.47:243][275]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.33.57:178][446]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.34.07:202][611]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.34.10:559][671]LogTemp: Warning: UI Min (1.0) >= UI Max (.01) for Ranged Numeric property /Script/Engine.PostProcessSettings:LumenDiffuseColorBoost
[2025.05.27-07.34.14:419][740]LogTemp: Warning: UI Min (1.0) >= UI Max (.01) for Ranged Numeric property /Script/Engine.PostProcessSettings:LumenDiffuseColorBoost
[2025.05.27-07.34.17:208][790]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.34.27:264][967]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.34.37:299][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.34.40:268][184]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_26
[2025.05.27-07.34.47:338][308]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.34.55:782][451]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_27
[2025.05.27-07.34.57:434][468]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.34.57:838][472]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_28
[2025.05.27-07.34.58:039][474]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_29
[2025.05.27-07.35.07:505][571]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.35.17:573][676]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.35.27:585][780]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.35.37:635][885]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.35.47:697][990]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.35.57:763][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.36.07:781][198]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.36.13:592][259]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1046.622314
[2025.05.27-07.36.14:065][264]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-07.36.14:065][264]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1046.998901, Update Interval: 316.260254
[2025.05.27-07.36.17:865][304]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.36.27:907][406]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.36.37:987][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.36.48:021][619]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.36.58:031][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.37.08:073][829]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.37.18:137][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.37.28:206][ 40]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.37.38:213][146]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.37.48:221][252]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.37.58:278][358]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.38.08:371][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.38.18:397][569]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.38.28:454][673]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.38.38:462][777]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.38.48:468][881]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.38.58:481][985]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.39.08:488][ 92]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.39.18:564][199]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.39.25:900][288]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (custom_head) ...
[2025.05.27-07.39.28:563][335]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.39.38:525][555]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.39.48:641][952]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.39.58:728][ 72]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.40.08:686][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.40.18:691][ 29]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.40.28:699][509]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.40.38:710][906]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.40.48:718][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.40.58:732][886]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.41.08:771][171]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.41.11:328][209]LogUObjectHash: Compacting FUObjectHashTables data took   1.21ms
[2025.05.27-07.41.11:382][209]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/__ExternalActors__/MetaHumans/Test/TestLevel/D/AZ/T9GPWZTOO0KKK1DHAXIO7T_Auto1
[2025.05.27-07.41.11:382][209]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/T9GPWZTOO0KKK1DHAXIO7T_Auto176E9D16247FC5F246563E6B3F38118B1.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/__ExternalActors__/MetaHumans/Test/TestLevel/D/AZ/T9GPWZTOO0KKK1DHAXIO7T_Auto1.uasset'
[2025.05.27-07.41.11:402][209]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/__ExternalActors__/MetaHumans/Test/TestLevel/3/TA/XSJV71BHDGA7SMN57PS0ZX_Auto1
[2025.05.27-07.41.11:402][209]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/XSJV71BHDGA7SMN57PS0ZX_Auto1FAAC972D4D163051070438A91D1F3976.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/__ExternalActors__/MetaHumans/Test/TestLevel/3/TA/XSJV71BHDGA7SMN57PS0ZX_Auto1.uasset'
[2025.05.27-07.41.11:488][209]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/MetaHumans/Test/TestLevel' took 0.517
[2025.05.27-07.41.11:488][209]LogFileHelpers: Editor autosave (incl. sublevels & external actors) for all levels took 0.517
[2025.05.27-07.41.11:488][209]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/MetaHumans/Test/Test] ([1] browsable assets)...
[2025.05.27-07.41.11:488][209]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Test/Test]
[2025.05.27-07.41.11:488][209]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/MetaHumans/Test/Test" FILE="H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Test/Test_Auto1.uasset" SILENT=false AUTOSAVING=true
[2025.05.27-07.41.11:516][209]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Test/Test_Auto1
[2025.05.27-07.41.11:516][209]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/Test_Auto117C5991B412AADDC61990484860E1489.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Test/Test_Auto1.uasset'
[2025.05.27-07.41.11:517][209]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Export/custom_head_PhysicsAsset] ([1] browsable assets)...
[2025.05.27-07.41.11:587][209]OBJ SavePackage:     Rendered thumbnail for [PhysicsAsset /Game/MetaHumans/Export/custom_head_PhysicsAsset.custom_head_PhysicsAsset]
[2025.05.27-07.41.11:587][209]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Export/custom_head_PhysicsAsset]
[2025.05.27-07.41.11:605][209]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Export/custom_head_PhysicsAsset_Auto1
[2025.05.27-07.41.11:605][209]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/custom_head_PhysicsAsset_Auto1492D546A470B3B789CDC40BB7FE9D84D.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Export/custom_head_PhysicsAsset_Auto1.uasset'
[2025.05.27-07.41.11:607][209]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Export/custom_head] ([1] browsable assets)...
[2025.05.27-07.41.11:633][209]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/MetaHumans/Export/custom_head.custom_head]
[2025.05.27-07.41.11:633][209]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Export/custom_head]
[2025.05.27-07.41.11:633][209]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/MetaHumans/Export/custom_head" FILE="H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Export/custom_head_Auto1.uasset" SILENT=false AUTOSAVING=true
[2025.05.27-07.41.11:874][209]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Export/custom_head_Auto1
[2025.05.27-07.41.11:874][209]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/custom_head_Auto114529C304FB33ABD2D2229B5D4DEED89.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Export/custom_head_Auto1.uasset'
[2025.05.27-07.41.11:874][209]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Export/MH_Friend_head_shader] ([1] browsable assets)...
[2025.05.27-07.41.11:915][209]OBJ SavePackage:     Rendered thumbnail for [Material /Game/MetaHumans/Export/MH_Friend_head_shader.MH_Friend_head_shader]
[2025.05.27-07.41.11:915][209]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Export/MH_Friend_head_shader]
[2025.05.27-07.41.11:917][209]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Export/MH_Friend_head_shader_Auto1
[2025.05.27-07.41.11:917][209]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_head_shader_Auto11445CFD545B745B8454430BB7A481B75.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Export/MH_Friend_head_shader_Auto1.uasset'
[2025.05.27-07.41.11:917][209]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Export/head_roughness_map] ([1] browsable assets)...
[2025.05.27-07.41.11:918][209]OBJ SavePackage:     Rendered thumbnail for [Texture2D /Game/MetaHumans/Export/head_roughness_map.head_roughness_map]
[2025.05.27-07.41.11:918][209]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Export/head_roughness_map]
[2025.05.27-07.41.12:056][209]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Export/head_roughness_map_Auto1
[2025.05.27-07.41.12:056][209]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/head_roughness_map_Auto13AC1754D4018633978E74487195732F5.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Export/head_roughness_map_Auto1.uasset'
[2025.05.27-07.41.12:059][209]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/MetaHumans/Export/custom_head_Skeleton] ([1] browsable assets)...
[2025.05.27-07.41.12:059][209]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Export/custom_head_Skeleton]
[2025.05.27-07.41.12:077][209]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Export/custom_head_Skeleton_Auto1
[2025.05.27-07.41.12:077][209]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/custom_head_Skeleton_Auto1C6590A944E609322DA9B28883E8BC25F.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Export/custom_head_Skeleton_Auto1.uasset'
[2025.05.27-07.41.12:078][209]LogFileHelpers: Auto-saving content packages took 0.590
[2025.05.27-07.41.18:755][466]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.41.28:757][983]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.41.38:763][501]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.41.41:679][652]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1374.707520
[2025.05.27-07.41.41:969][667]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-07.41.41:970][667]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1374.979492, Update Interval: 339.266327
[2025.05.27-07.41.48:781][ 21]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.41.58:797][542]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.42.08:814][ 59]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.42.18:814][557]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.42.28:819][ 67]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.42.38:823][584]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.42.48:838][103]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.42.58:855][622]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.43.08:873][141]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.43.18:885][661]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.43.28:887][182]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.43.38:901][700]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.43.48:916][205]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.43.58:918][722]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.44.08:931][241]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.44.18:946][761]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.44.28:955][275]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.44.38:971][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.44.48:989][315]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.44.58:994][833]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.45.09:001][351]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.45.19:009][868]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.45.29:024][387]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.45.39:039][908]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.45.49:055][429]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.45.59:067][949]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.46.09:076][467]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.46.19:079][985]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.46.29:092][501]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.46.39:094][ 20]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.46.49:112][540]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.46.59:116][ 59]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.47.09:128][576]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.47.19:130][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.47.29:145][612]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.47.39:151][132]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.47.47:516][567]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1740.540894
[2025.05.27-07.47.47:828][583]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-07.47.47:828][583]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1740.834106, Update Interval: 352.063965
[2025.05.27-07.47.49:167][653]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.47.59:186][166]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.48.09:199][657]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.48.19:218][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.48.29:231][680]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.48.39:240][192]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.48.49:254][702]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.48.59:268][219]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.49.09:282][737]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.49.19:293][253]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.49.29:305][770]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.49.39:306][288]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.49.49:313][808]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.49.59:333][327]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.50.09:347][847]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.50.19:359][365]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.50.29:369][882]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.50.39:372][401]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.50.49:374][920]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.50.59:386][440]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.51.09:405][956]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.51.19:412][477]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.51.29:417][996]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.51.39:425][501]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.51.49:433][  7]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.51.59:471][495]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.52.09:479][956]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.52.19:498][396]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.52.29:573][806]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.52.34:994][906]Cmd: MODE SCALEGRID=0
[2025.05.27-07.52.39:558][128]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.52.49:562][551]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.52.59:577][990]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.53.09:581][456]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.53.19:587][967]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.53.29:591][480]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.53.39:628][809]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.53.43:236][872]LogUObjectHash: Compacting FUObjectHashTables data took   0.93ms
[2025.05.27-07.53.43:242][872]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/MetaHumans/Test/TestLevel' took 0.034
[2025.05.27-07.53.43:242][872]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/MetaHumans/Test/Test] ([1] browsable assets)...
[2025.05.27-07.53.43:242][872]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Test/Test]
[2025.05.27-07.53.43:242][872]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/MetaHumans/Test/Test" FILE="H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Test/Test_Auto2.uasset" SILENT=false AUTOSAVING=true
[2025.05.27-07.53.43:270][872]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Test/Test_Auto2
[2025.05.27-07.53.43:270][872]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/Test_Auto2FC7742784F4BDBBAB2AB4EB087CF7922.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Test/Test_Auto2.uasset'
[2025.05.27-07.53.43:271][872]LogFileHelpers: Auto-saving content packages took 0.028
[2025.05.27-07.53.49:609][ 66]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.53.59:619][529]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.54.09:624][999]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.54.18:046][404]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2131.074707
[2025.05.27-07.54.18:358][420]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-07.54.18:358][420]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2131.366699, Update Interval: 334.604340
[2025.05.27-07.54.19:642][486]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.54.29:662][961]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.54.39:677][455]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.54.49:685][968]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.54.59:696][476]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.55.09:721][938]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.55.19:722][349]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.55.29:735][805]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.55.39:736][292]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.55.49:750][768]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.55.59:772][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.56.09:790][710]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.56.19:795][191]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.56.29:814][668]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.56.39:825][141]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.56.49:832][656]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.56.59:839][175]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.57.09:852][688]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.57.19:871][207]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.57.29:884][728]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.57.39:897][248]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.57.49:901][769]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.57.59:908][287]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.58.09:912][803]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.58.19:927][323]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.58.29:945][841]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.58.39:954][362]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.58.49:955][882]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.58.59:958][401]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.59.09:974][919]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.59.19:991][438]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.59.30:002][958]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.59.40:014][478]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.59.50:030][  0]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.00.00:046][520]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.00.10:051][ 38]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.00.20:052][557]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.00.30:065][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.00.40:077][593]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.00.50:081][112]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.00.52:867][257]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2525.892822
[2025.05.27-08.00.53:176][273]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-08.00.53:176][273]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2526.181641, Update Interval: 323.734863
[2025.05.27-08.01.00:088][631]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.01.10:097][148]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.01.20:100][666]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.01.30:103][185]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.01.40:121][705]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.01.50:130][227]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.02.00:136][746]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.02.10:151][265]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.02.20:163][786]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.02.30:176][305]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.02.40:195][825]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.02.50:206][347]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.03.00:212][866]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.03.10:210][398]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.03.20:215][931]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.03.30:229][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.03.40:232][998]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.03.50:239][531]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.04.00:249][ 64]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.04.10:263][595]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.04.20:277][126]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.04.30:295][658]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.04.40:298][189]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.04.50:314][723]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.05.00:325][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.05.10:343][786]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.05.20:354][319]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.05.30:368][853]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.05.40:380][386]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.05.50:388][919]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.06.00:405][450]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.06.10:411][959]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.06.20:415][467]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.06.30:434][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.06.40:449][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.06.50:450][993]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.06.54:969][225]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2887.990479
[2025.05.27-08.06.55:525][253]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-08.06.55:525][253]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2888.526855, Update Interval: 341.511292
[2025.05.27-08.07.00:456][506]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.07.10:471][ 15]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.07.20:488][526]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.07.30:490][ 35]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.07.40:501][545]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.07.50:503][ 57]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.08.00:520][568]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.08.10:535][ 78]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.08.20:549][586]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.08.30:557][ 92]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.08.40:566][600]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.08.50:581][109]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.09.00:596][619]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.09.10:615][128]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.09.20:621][638]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.09.30:629][147]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.09.40:649][657]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.09.50:662][167]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.10.00:667][675]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.10.10:672][183]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.10.20:672][689]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.10.30:680][194]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.10.40:694][705]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.10.50:695][215]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.11.00:708][723]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.11.10:726][230]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.11.20:738][741]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.11.30:758][252]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.11.40:760][761]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.11.50:763][269]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.12.00:775][779]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.12.10:786][289]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.12.20:791][801]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.12.30:806][305]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.12.40:816][815]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.12.50:831][326]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.13.00:833][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.13.10:838][343]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.13.19:270][773]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3272.280273
[2025.05.27-08.13.19:571][788]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-08.13.19:571][788]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3272.562500, Update Interval: 335.536377
[2025.05.27-08.13.20:843][853]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.13.30:851][362]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.13.40:863][872]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.13.50:862][383]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.14.00:878][895]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.14.10:879][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.14.20:894][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.14.30:901][420]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.14.40:906][927]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.14.50:911][438]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.15.00:916][947]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.15.10:933][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.15.20:943][985]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.15.30:958][506]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.15.40:971][ 29]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.15.50:971][549]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.16.00:975][ 70]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.16.10:982][590]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.16.21:000][111]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.16.31:014][631]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.16.41:013][151]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.16.51:020][672]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.17.01:028][195]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.17.11:040][716]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.17.21:048][236]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.17.31:061][758]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.17.41:081][281]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.17.51:090][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.18.01:098][325]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.18.11:099][845]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.18.21:108][367]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.18.31:137][887]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.18.41:131][408]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.18.51:146][930]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.19.01:156][452]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.19.11:176][972]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.19.21:186][490]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.19.31:187][ 12]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.19.41:204][535]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.19.51:213][ 56]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.19.51:869][ 90]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3664.883545
[2025.05.27-08.19.52:161][105]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-08.19.52:161][105]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3665.155518, Update Interval: 351.247284
[2025.05.27-08.20.01:216][577]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.20.11:232][101]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.20.21:248][622]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.20.31:269][140]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.20.37:150][448]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.05.27-08.20.41:270][662]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.20.51:277][181]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.21.01:285][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.21.11:302][226]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.21.21:319][746]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.21.31:328][269]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.21.41:338][790]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.21.51:341][312]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.22.01:351][833]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.22.11:356][355]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.22.21:361][877]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.22.31:392][397]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.22.41:381][918]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.22.51:403][441]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.23.01:410][963]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.23.11:427][486]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.23.21:430][  5]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.23.31:436][527]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.23.41:448][ 49]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.23.51:467][569]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.24.01:467][ 87]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.24.11:474][609]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.24.21:480][130]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.24.31:514][650]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.24.41:506][170]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.24.51:509][690]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.25.01:521][211]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.25.11:536][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.25.21:554][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.25.31:555][775]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.25.41:561][297]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.25.51:575][819]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.26.01:582][340]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.26.11:599][864]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.26.21:608][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.26.31:629][905]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.26.34:650][ 61]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4067.668457
[2025.05.27-08.26.34:925][ 75]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-08.26.34:925][ 75]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4067.921387, Update Interval: 305.374298
[2025.05.27-08.26.41:618][423]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.26.51:635][946]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.27.01:642][466]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.27.11:652][987]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.27.21:669][508]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.27.31:674][ 28]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.27.41:692][551]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.27.51:693][ 70]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.28.01:704][592]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.28.11:713][113]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.28.21:715][633]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.28.31:745][155]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.28.41:731][673]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.28.51:733][195]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.29.01:746][717]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.29.11:749][236]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.29.21:763][757]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.29.31:773][278]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.29.41:787][800]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.29.51:798][322]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.30.01:804][843]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.30.11:811][366]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.30.21:810][885]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.30.31:825][406]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.30.41:830][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.30.51:830][445]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.31.01:847][968]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.31.11:851][490]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.31.21:858][ 10]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.31.31:868][531]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.31.41:885][ 55]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.31.51:898][578]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.32.01:910][ 99]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.32.10:408][542]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4403.433594
[2025.05.27-08.32.10:698][557]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-08.32.10:698][557]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4403.703613, Update Interval: 313.883484
[2025.05.27-08.32.11:921][621]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.32.21:937][141]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.32.31:941][662]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.32.41:959][181]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.32.51:961][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.33.01:979][228]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.33.11:984][749]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.33.21:993][269]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.33.31:992][790]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.33.42:004][312]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.33.52:016][835]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.34.02:027][356]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.34.12:038][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.34.22:056][392]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.34.32:059][913]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.34.42:067][432]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.34.52:078][953]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.35.02:089][472]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.35.12:108][991]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.35.22:121][511]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.35.32:136][ 32]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.35.42:152][548]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.35.52:154][ 68]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.36.02:155][587]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.36.12:164][106]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.36.22:168][622]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.36.32:184][139]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.36.42:187][652]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.36.52:194][169]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.37.02:195][685]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.37.12:207][202]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.37.22:210][719]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.37.32:228][237]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.37.42:246][756]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.37.52:256][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.38.02:268][791]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.38.12:269][309]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.38.19:781][696]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4772.805176
[2025.05.27-08.38.20:070][711]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-08.38.20:070][711]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4773.074707, Update Interval: 343.864258
[2025.05.27-08.38.22:282][826]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.38.32:287][342]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.38.42:295][856]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.38.52:311][375]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.39.02:313][891]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.39.12:328][409]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.39.22:334][925]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.39.32:336][441]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.39.42:342][959]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.39.52:346][477]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.40.02:356][995]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.40.12:363][513]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.40.22:368][ 29]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.40.32:381][548]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.40.42:395][ 62]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.40.52:404][581]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.41.02:421][ 96]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.41.12:438][611]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.41.22:450][128]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.41.32:452][645]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.41.42:470][162]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.41.52:475][679]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.42.02:485][195]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.42.12:502][712]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.42.22:518][229]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.42.32:526][747]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.42.42:540][261]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.42.52:557][778]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.43.02:569][295]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.43.12:582][812]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.43.22:594][327]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.43.32:596][844]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.43.42:608][361]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.43.52:612][879]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.44.02:629][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.44.12:635][911]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.44.22:653][429]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.44.32:667][947]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.44.42:673][461]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.44.52:681][977]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.44.52:777][982]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5165.793945
[2025.05.27-08.44.53:087][998]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-08.44.53:087][998]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5166.083496, Update Interval: 324.752960
[2025.05.27-08.45.02:682][495]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.45.12:694][ 11]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.45.22:708][525]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.45.32:718][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.45.42:731][560]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.45.52:740][ 76]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.46.02:741][590]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.46.12:760][105]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.46.22:769][622]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.46.32:783][141]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.46.42:785][656]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.46.52:797][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.47.02:805][690]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.47.12:818][206]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.47.22:829][721]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.47.32:834][237]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.47.42:839][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.47.52:851][269]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.48.02:869][786]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.48.12:883][303]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.48.22:888][819]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.48.32:888][336]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.48.42:893][849]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.48.52:911][365]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.49.02:923][882]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.49.12:918][399]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.49.22:928][913]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.49.32:932][430]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.49.42:940][947]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.49.52:941][410]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.50.02:951][923]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.50.12:959][438]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.50.22:971][953]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.50.32:980][468]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.50.42:993][981]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.50.48:502][265]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5521.526855
[2025.05.27-08.50.48:795][280]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-08.50.48:795][280]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5521.800781, Update Interval: 347.390961
[2025.05.27-08.50.52:998][496]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.51.03:015][ 11]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.51.13:029][526]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.51.23:037][ 41]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.51.33:037][556]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.51.43:055][ 73]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.51.53:067][588]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.52.03:079][101]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.52.13:087][617]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.52.23:103][132]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.52.33:112][648]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.52.43:120][161]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.52.53:132][676]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.53.03:142][191]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.53.13:147][706]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.53.23:156][222]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.53.33:164][738]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.53.43:180][255]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.53.53:181][770]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.54.03:195][285]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.54.13:206][801]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.54.23:207][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.54.33:212][829]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.54.43:228][343]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.54.53:243][858]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.55.03:261][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.55.13:277][890]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.55.23:286][405]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.55.33:286][920]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.55.43:296][436]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.55.53:298][952]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.56.03:318][468]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.56.13:329][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.56.23:330][501]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.56.33:343][ 15]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.56.43:363][530]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.56.53:374][ 46]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.57.03:394][563]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.57.13:392][ 78]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.57.23:401][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.57.28:765][871]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5921.732422
[2025.05.27-08.57.29:057][886]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-08.57.29:057][886]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5922.005371, Update Interval: 337.105621
[2025.05.27-08.57.33:404][110]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.57.43:416][627]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.57.53:431][144]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.58.03:441][660]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.58.13:446][175]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.58.23:460][691]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.58.33:458][208]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.58.43:468][723]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.58.53:467][240]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.59.03:472][755]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.59.13:476][272]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.59.23:477][789]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.59.33:477][306]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.59.43:486][823]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-08.59.53:492][339]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.00.03:507][856]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.00.13:522][373]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.00.23:523][888]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.00.33:527][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.00.43:540][919]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.00.53:546][436]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.01.03:551][951]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.01.13:564][469]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.01.23:567][986]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.01.33:573][501]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.01.43:581][ 18]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.01.53:595][537]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.02.03:599][ 52]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.02.13:617][569]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.02.23:621][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.02.33:632][601]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.02.43:635][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.02.53:651][633]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.03.03:667][150]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.03.13:679][667]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.03.23:682][182]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.03.33:699][700]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.03.42:922][176]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6295.869141
[2025.05.27-09.03.43:555][209]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-09.03.43:555][209]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6296.482422, Update Interval: 307.090057
[2025.05.27-09.03.43:713][217]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.03.53:714][735]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.04.03:722][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.04.13:736][766]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.04.23:743][279]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.04.33:751][796]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.04.43:767][310]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.04.53:778][826]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.05.03:787][341]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.05.13:804][856]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.05.23:807][372]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.05.33:810][888]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.05.43:825][405]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.05.53:834][921]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.06.03:851][436]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.06.13:862][953]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.06.23:864][469]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.06.33:872][987]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.06.43:874][500]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.06.53:888][ 16]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.07.03:890][531]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.07.13:897][ 48]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.07.23:902][565]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.07.33:912][ 81]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.07.43:920][597]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.07.53:921][113]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.08.03:934][629]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.08.13:952][148]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.08.23:970][665]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.08.33:972][181]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.08.43:977][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.08.53:984][211]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.09.03:996][728]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.09.14:007][243]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.09.24:022][759]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.09.34:032][277]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.09.44:034][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.09.45:308][860]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6658.228027
[2025.05.27-09.09.45:602][875]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-09.09.45:602][875]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6658.502441, Update Interval: 317.867981
[2025.05.27-09.09.54:038][311]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.10.04:046][827]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.10.14:054][343]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.10.24:057][858]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.10.34:060][375]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.10.44:062][891]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.10.54:072][408]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.11.04:088][925]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.11.14:089][443]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.11.24:094][957]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.11.34:101][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.11.44:111][991]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.11.54:127][508]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.12.04:130][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.12.14:139][541]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.12.24:150][ 58]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.12.34:154][573]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.12.44:166][ 88]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.12.54:178][605]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.13.04:180][120]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.13.14:181][638]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.13.24:187][155]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.13.34:202][673]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.13.44:208][191]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.13.54:228][709]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.14.04:234][226]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.14.14:239][741]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.14.24:248][243]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.14.34:251][743]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.14.44:252][235]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.14.54:263][731]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.15.04:269][237]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.15.14:283][755]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.15.24:288][270]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.15.34:294][787]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.15.44:303][305]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.15.54:322][822]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.15.58:857][ 56]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7031.753906
[2025.05.27-09.15.59:170][ 72]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-09.15.59:170][ 72]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 7032.046387, Update Interval: 345.594666
[2025.05.27-09.16.04:339][338]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.16.14:353][855]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.16.24:369][370]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.16.34:368][887]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.16.44:370][400]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.16.54:374][916]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.17.04:393][432]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.17.14:407][948]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.17.24:424][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.17.34:437][979]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.17.44:456][495]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.17.54:470][ 13]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.18.04:477][529]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.18.14:490][ 47]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.18.24:498][561]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.18.34:501][ 76]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.18.44:510][591]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.18.54:523][107]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.19.04:541][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.19.14:558][139]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.19.24:575][653]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.19.34:587][169]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.19.44:595][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.19.54:597][200]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.20.04:600][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.20.14:615][232]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.20.24:619][747]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.20.34:637][263]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.20.37:151][391]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.05.27-09.20.44:647][778]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.20.54:666][295]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.21.04:669][811]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.21.14:672][327]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.21.24:689][842]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.21.34:698][358]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.21.44:715][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.21.54:724][390]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.22.04:741][905]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.22.14:749][421]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.22.16:900][532]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7409.797852
[2025.05.27-09.22.17:191][547]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-09.22.17:191][547]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 7410.069824, Update Interval: 353.353058
[2025.05.27-09.22.24:767][937]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.22.34:768][453]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.22.44:770][966]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.22.54:783][483]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.23.04:787][997]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.23.14:792][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.23.24:802][ 28]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.23.34:804][545]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.23.44:814][ 62]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.23.54:824][579]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.24.04:831][ 95]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.24.14:831][612]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.24.24:845][130]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.24.34:856][647]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.24.44:869][160]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.24.54:889][678]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.25.04:908][195]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.25.14:919][711]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.25.24:933][227]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.25.34:933][742]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.25.44:947][259]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.25.54:967][776]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.26.04:968][290]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.26.14:971][806]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.26.24:984][322]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.26.34:989][838]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.26.44:996][351]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.26.55:008][868]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.27.05:020][383]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.27.15:033][899]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.27.25:041][416]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.27.35:055][933]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.27.45:067][450]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.27.55:084][966]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.28.05:103][482]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.28.15:113][998]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.28.25:126][512]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.28.35:128][ 29]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.28.45:134][542]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.28.55:147][ 60]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.29.05:152][576]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.29.10:460][851]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7823.326172
[2025.05.27-09.29.10:999][879]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-09.29.10:999][879]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 7823.845703, Update Interval: 303.420502
[2025.05.27-09.29.15:168][ 92]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.29.25:187][608]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.29.35:197][125]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.29.45:214][642]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.29.55:223][159]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.30.05:236][676]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.30.15:242][192]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.30.25:246][707]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.30.35:258][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.30.45:265][736]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.30.55:271][252]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.31.05:286][770]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.31.15:304][288]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.31.25:311][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.31.35:329][322]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.31.45:334][838]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.31.55:343][355]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.32.05:361][872]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.32.15:379][389]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.32.25:381][906]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.32.35:401][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.32.45:418][940]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.32.55:427][457]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.33.05:431][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.33.15:432][492]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.33.25:435][  8]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.33.35:438][524]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.33.45:456][ 42]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.33.55:470][558]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.34.05:479][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.34.15:481][589]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.34.25:491][102]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.34.35:496][620]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.34.45:514][136]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.34.55:505][651]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8168.339844
[2025.05.27-09.34.55:524][652]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.34.55:796][666]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-09.34.55:796][666]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8168.611816, Update Interval: 301.794495
[2025.05.27-09.35.05:527][166]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.35.15:529][682]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.35.25:537][197]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.35.35:545][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.35.45:561][229]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.35.55:567][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.36.05:575][260]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.36.15:580][776]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.36.25:592][293]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.36.35:605][809]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.36.45:621][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.36.55:631][840]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.37.05:648][355]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.37.15:650][872]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.37.25:654][389]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.37.35:667][905]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.37.45:677][423]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.37.55:678][939]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.38.05:681][456]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.38.15:682][970]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.38.25:684][487]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.38.35:701][  3]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.38.45:706][518]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.38.55:724][ 36]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.39.05:736][552]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.39.15:735][ 69]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.39.25:748][587]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.39.35:751][103]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.39.45:766][616]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.39.55:773][131]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.40.05:782][646]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.40.15:789][162]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.40.25:805][678]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.40.35:810][195]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.40.36:797][246]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8509.903320
[2025.05.27-09.40.38:481][330]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-09.40.38:481][330]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8511.569336, Update Interval: 346.894745
[2025.05.27-09.40.45:813][709]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.40.55:813][223]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.41.05:831][742]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.41.15:852][259]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.41.25:869][776]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.41.35:886][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.41.45:901][811]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.41.55:902][328]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.42.05:907][845]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.42.15:921][363]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.42.25:935][880]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.42.35:952][397]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.42.45:956][911]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.42.55:969][428]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.43.05:979][947]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.43.15:980][465]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.43.25:980][982]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.43.35:991][498]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.43.45:992][ 13]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.43.56:008][530]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.44.06:017][ 46]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.44.16:037][563]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.44.26:055][ 80]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.44.36:057][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.44.46:066][108]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.44.56:083][626]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.45.06:096][142]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.45.16:115][659]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.45.26:128][176]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.45.36:137][693]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.45.46:148][211]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.45.56:153][727]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.46.06:154][242]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.46.16:153][758]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.46.26:160][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.46.36:170][790]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.46.46:191][303]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.46.56:206][821]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.47.06:225][338]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.47.16:234][856]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.47.16:835][887]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8910.259766
[2025.05.27-09.47.17:375][915]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-09.47.17:375][915]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8910.779297, Update Interval: 359.626465
[2025.05.27-09.47.26:249][373]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.47.36:256][889]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.47.46:274][406]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.47.56:274][922]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.48.06:289][437]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.48.16:292][952]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.48.26:299][470]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.48.36:317][987]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.48.46:326][501]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.48.56:343][ 19]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.49.06:350][535]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.49.16:367][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.49.26:370][565]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.49.36:382][ 80]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.49.46:389][597]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.49.56:390][113]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.50.06:398][629]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.50.16:408][146]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.50.26:419][662]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.50.36:438][179]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.50.46:449][692]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.50.56:452][208]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.51.06:458][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.51.16:462][240]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.51.26:480][758]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.51.36:491][275]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.51.46:496][792]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.51.56:502][308]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.52.06:513][823]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.52.16:523][341]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.52.26:538][857]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.52.36:543][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.52.46:559][887]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.52.56:569][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.53.06:583][922]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.53.16:589][438]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.53.26:604][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.53.36:617][471]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.53.46:628][989]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.53.56:630][505]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.54.06:646][ 22]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.54.06:667][ 23]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 9320.444336
[2025.05.27-09.54.06:978][ 39]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-09.54.06:978][ 39]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 9320.737305, Update Interval: 319.459198
[2025.05.27-09.54.16:657][540]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.54.26:660][ 55]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.54.36:668][572]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.54.46:683][ 86]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.54.56:692][602]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.55.06:707][120]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.55.16:721][638]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.55.26:724][152]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.55.36:740][668]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.55.46:755][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.55.56:774][702]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.56.06:789][218]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.56.16:793][735]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.56.26:808][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.56.36:815][770]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.56.46:828][286]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.56.56:839][803]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.57.06:844][319]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.57.16:851][836]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.57.26:856][352]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.57.36:861][868]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.57.46:864][386]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.57.56:873][902]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.58.06:881][420]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.58.16:888][936]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.58.26:898][452]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.58.36:912][969]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.58.46:928][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.58.56:942][  2]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.59.06:945][519]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.59.16:946][ 36]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.59.26:949][552]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.59.36:957][ 68]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.59.46:975][586]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.59.56:978][101]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-09.59.57:907][149]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 9671.933594
[2025.05.27-09.59.58:198][164]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-09.59.58:198][164]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 9672.207031, Update Interval: 338.299500
[2025.05.27-10.00.06:986][619]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.00.16:997][136]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.00.27:008][653]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.00.37:017][171]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.00.47:019][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.00.57:030][202]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.01.07:047][719]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.01.17:050][237]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.01.27:060][755]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.01.37:063][270]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.01.47:065][789]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.01.57:084][307]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.02.07:091][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.02.17:097][342]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.02.27:107][859]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.02.37:121][376]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.02.47:134][889]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.02.57:134][407]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.03.07:146][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.03.17:152][442]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.03.27:157][959]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.03.37:165][475]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.03.47:177][992]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.03.57:186][507]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.04.07:188][ 22]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.04.17:192][538]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.04.27:206][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.04.37:221][571]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.04.47:241][ 85]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.04.57:242][599]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.05.07:259][117]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.05.17:271][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.05.27:287][152]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.05.37:302][671]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.05.47:317][191]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.05.57:322][711]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.06.07:322][232]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.06.17:333][751]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.06.27:337][249]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.06.28:450][306]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 10062.627930
[2025.05.27-10.06.28:764][323]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-10.06.28:764][323]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 10062.922852, Update Interval: 328.272339
[2025.05.27-10.06.37:350][771]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.06.47:362][300]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.06.57:380][839]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.07.07:385][380]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.07.17:392][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.07.27:410][435]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.07.37:423][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.07.47:438][481]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.07.57:447][ 20]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.08.07:447][560]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.08.17:454][100]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.08.27:456][623]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.08.37:462][152]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.08.47:464][685]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.08.57:480][211]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.09.07:494][747]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.09.17:504][275]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.09.27:517][786]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.09.37:542][316]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.09.47:556][843]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.09.57:557][380]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.10.07:564][920]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.10.17:573][456]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.10.27:591][986]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.10.37:598][511]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.10.47:612][ 33]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.10.57:627][569]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.11.07:636][107]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.11.17:653][638]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.11.27:653][162]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.11.37:667][683]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.11.47:677][209]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.11.57:695][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.12.07:700][260]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.12.17:718][784]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.12.27:720][308]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.12.37:724][828]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.12.43:146][109]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 10437.675781
[2025.05.27-10.12.43:449][125]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-10.12.43:449][125]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 10437.959961, Update Interval: 338.607147
[2025.05.27-10.12.47:730][349]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.12.57:751][852]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.13.07:755][350]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.13.17:773][852]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.13.27:787][361]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.13.37:803][883]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.13.47:811][391]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.13.57:818][900]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.14.07:832][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.14.17:841][894]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.14.27:854][372]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.14.37:858][882]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.14.47:867][392]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.14.57:872][909]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.15.07:885][413]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.15.17:896][938]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.15.27:901][465]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.15.37:907][981]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.15.47:925][505]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.15.57:940][ 19]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.16.07:963][542]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.16.17:973][ 56]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.16.27:969][553]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.16.37:971][ 59]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.16.47:986][570]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.16.57:987][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.17.07:994][591]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.17.18:007][109]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.17.28:009][632]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.17.38:015][151]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.17.48:023][666]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.17.58:037][171]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.18.08:039][676]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.18.18:041][172]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.18.28:055][678]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.18.38:067][166]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.18.48:080][660]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.18.58:093][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.19.08:119][655]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.19.16:830][ 87]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 10831.382812
[2025.05.27-10.19.17:133][102]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-10.19.17:133][103]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 10831.665039, Update Interval: 347.938477
[2025.05.27-10.19.18:127][152]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.19.28:135][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.19.38:142][176]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.19.48:149][700]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.19.58:157][203]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.20.08:161][713]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.20.18:166][239]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.20.28:168][767]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.20.37:152][241]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.05.27-10.20.38:178][295]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.20.48:184][820]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.20.58:192][341]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.21.08:209][868]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.21.18:210][381]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.21.28:216][896]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.21.38:223][417]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.21.48:226][917]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.21.58:235][429]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.22.08:249][928]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.22.18:264][426]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.22.28:271][947]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.22.38:280][469]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.22.48:288][988]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.22.58:301][509]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.23.08:312][ 30]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.23.18:322][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.23.28:341][ 35]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.23.38:340][537]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.23.48:341][ 40]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.23.58:344][552]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.24.08:355][ 68]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.24.18:357][556]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.24.28:357][ 39]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.24.38:366][555]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.24.48:372][ 69]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.24.58:390][586]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.25.08:408][103]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.25.18:413][590]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.25.28:424][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.25.38:439][543]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.25.48:446][ 62]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.25.58:462][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.26.04:477][886]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 11239.061523
[2025.05.27-10.26.04:768][901]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-10.26.04:768][901]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 11239.333984, Update Interval: 319.316376
[2025.05.27-10.26.08:464][ 92]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.26.18:469][609]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.26.28:471][128]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.26.38:475][644]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.26.48:493][142]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.26.58:491][653]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.27.08:507][173]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.27.18:518][689]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.27.28:536][211]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.27.38:545][731]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.27.48:562][255]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.27.58:575][779]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.28.08:591][302]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.28.18:599][825]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.28.28:601][333]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.28.38:615][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.28.48:623][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.28.58:633][889]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.29.08:649][398]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.29.18:665][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.29.28:676][427]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.29.38:693][943]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.29.48:707][441]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.29.58:719][949]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.30.08:724][459]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.30.18:740][975]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.30.28:742][495]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.30.38:751][ 17]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.30.48:756][539]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.30.58:768][ 65]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.31.08:773][586]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.31.18:793][107]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.31.28:803][625]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.31.38:806][143]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.31.48:823][665]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.31.58:827][187]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.32.08:847][706]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.32.17:215][139]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 11611.878906
[2025.05.27-10.32.17:536][156]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-10.32.17:536][156]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 11612.181641, Update Interval: 313.270050
[2025.05.27-10.32.18:864][225]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.32.28:875][741]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.32.38:876][258]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.32.48:878][768]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.32.58:885][286]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.33.08:885][801]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.33.18:890][321]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.33.28:910][843]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.33.38:911][362]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.33.48:926][878]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.33.58:930][399]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.34.08:938][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.34.18:957][443]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.34.28:970][965]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.34.38:972][487]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.34.48:989][  3]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.34.58:991][519]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.35.09:010][ 30]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.35.19:023][542]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.35.29:030][ 56]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.35.39:037][571]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.35.49:048][ 90]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.35.59:058][603]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.36.09:076][121]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.36.19:094][633]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.36.29:111][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.36.39:123][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.36.49:131][199]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.36.59:141][712]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.37.09:152][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.37.19:153][752]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.37.29:156][277]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.37.39:170][802]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.37.49:185][325]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.37.59:198][848]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.38.09:206][361]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.38.16:021][718]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 11970.752930
[2025.05.27-10.38.16:307][733]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-10.38.16:307][733]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 11971.020508, Update Interval: 347.021088
[2025.05.27-10.38.19:222][887]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.38.29:237][408]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.38.39:248][923]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.38.49:252][441]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.38.59:255][961]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.39.09:270][477]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.39.19:273][999]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.39.29:274][525]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.39.39:291][ 50]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.39.49:304][577]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.39.59:318][102]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.40.09:332][616]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.40.19:330][126]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.40.29:341][649]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.40.39:342][178]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.40.49:349][701]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.40.59:352][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.41.09:358][747]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.41.19:372][273]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.41.29:382][800]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.41.39:390][327]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.41.49:402][859]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.41.59:404][388]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.42.09:409][917]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.42.19:416][447]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.42.29:433][978]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.42.39:442][508]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.42.49:442][ 32]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.42.59:455][559]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.43.09:459][ 90]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.43.19:471][619]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.43.29:481][147]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.43.39:498][679]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.43.49:509][213]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.43.59:514][743]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.44.09:514][268]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.44.19:523][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.44.29:529][319]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.44.39:538][843]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.44.46:180][188]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 12361.031250
[2025.05.27-10.44.46:486][204]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-10.44.46:486][204]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 12361.317383, Update Interval: 342.954193
[2025.05.27-10.44.49:538][363]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.44.59:542][887]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.45.09:552][413]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.45.19:568][940]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.45.29:587][467]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.45.39:605][992]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.45.49:616][496]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.45.59:626][  9]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.46.09:634][519]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.46.19:640][ 18]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.46.29:651][539]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.46.39:663][ 63]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.46.49:681][589]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.46.59:695][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.47.09:711][641]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.47.19:723][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.47.29:724][687]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.47.39:745][219]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.47.49:748][753]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.47.59:768][280]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.48.09:774][806]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.48.19:774][331]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.48.29:792][837]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.48.39:800][332]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.48.49:816][822]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.48.59:818][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.49.09:835][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.49.19:839][347]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.49.29:842][865]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.49.39:848][387]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.49.49:855][906]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.49.59:872][429]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.50.09:882][952]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.50.19:891][469]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.50.29:902][987]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.50.39:915][503]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.50.49:923][  7]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.50.59:940][502]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.51.05:623][775]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 12740.638672
[2025.05.27-10.51.05:927][790]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-10.51.05:927][790]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 12740.921875, Update Interval: 344.072998
[2025.05.27-10.51.09:944][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.51.19:946][478]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.51.29:961][983]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.51.39:972][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.51.49:990][989]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.51.59:992][505]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.52.09:992][ 22]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.52.19:996][548]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.52.30:002][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.52.40:018][542]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.52.50:027][ 37]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.53.00:032][553]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.53.10:041][ 77]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.53.20:039][602]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.53.30:048][127]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.53.40:055][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.53.50:059][170]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.54.00:065][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.54.10:076][221]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.54.20:081][747]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.54.30:089][275]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.54.40:104][800]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.54.50:112][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.55.00:112][839]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.55.10:120][351]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.55.20:128][855]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.55.30:138][368]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.55.40:145][873]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.55.50:167][380]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.56.00:182][886]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.56.10:195][395]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.56.20:210][903]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.56.30:212][395]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.56.40:221][903]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.56.50:238][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.57.00:257][935]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.57.10:259][448]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.57.20:271][970]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.57.30:285][492]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.57.38:891][942]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 13133.891602
[2025.05.27-10.57.39:203][958]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-10.57.39:204][958]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 13134.183594, Update Interval: 345.975525
[2025.05.27-10.57.40:300][ 15]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.57.50:315][529]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.58.00:333][ 50]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.58.10:344][573]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.58.20:352][ 99]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.58.30:357][607]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.58.40:359][129]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.58.50:375][649]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.59.00:383][167]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.59.10:389][661]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.59.20:395][156]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.59.30:409][642]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.59.40:424][136]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-10.59.50:437][625]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.00.00:455][120]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.00.10:465][611]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.00.20:470][101]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.00.30:482][591]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.00.40:498][ 73]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.00.50:512][558]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.01.00:513][ 47]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.01.10:530][541]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.01.20:533][ 26]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.01.30:548][518]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.01.40:557][ 11]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.01.50:569][507]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.02.00:581][  2]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.02.10:600][491]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.02.20:614][980]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.02.30:615][472]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.02.40:619][966]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.02.50:626][459]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.03.00:634][955]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.03.10:646][449]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.03.20:655][929]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.03.30:662][421]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.03.40:678][916]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.03.50:691][401]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.04.00:713][884]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.04.10:712][376]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.04.20:712][868]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.04.20:832][874]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 13535.831055
[2025.05.27-11.04.21:144][889]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-11.04.21:144][889]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 13536.123047, Update Interval: 341.487488
[2025.05.27-11.04.30:722][362]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.04.40:722][871]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.04.50:723][392]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.05.00:730][912]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.05.10:747][435]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.05.20:762][957]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.05.30:770][482]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.05.40:787][  8]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.05.50:804][532]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.06.00:809][ 58]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.06.10:828][586]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.06.20:829][110]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.06.30:838][633]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.06.40:846][136]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.06.50:850][653]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.07.00:867][172]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.07.10:882][691]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.07.20:891][209]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.07.30:893][728]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.07.40:907][236]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.07.46:817][526]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.05.27-11.07.50:800][526]LogSlate: Window 'Save Content' being destroyed
[2025.05.27-11.07.50:872][526]LogStall: Shutdown...
[2025.05.27-11.07.50:872][526]LogStall: Shutdown complete.
[2025.05.27-11.07.51:055][526]LogWorld: UWorld::CleanupWorld for World_23, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:055][526]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:077][526]LogSlate: Window 'Face_Archetype_Skeleton' being destroyed
[2025.05.27-11.07.51:108][526]LogWorld: UWorld::CleanupWorld for World_25, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:108][526]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:118][526]LogWorld: UWorld::CleanupWorld for World_24, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:118][526]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:168][526]LogSlate: Window 'BlenderLinkProject - Unreal Editor' being destroyed
[2025.05.27-11.07.51:256][526]Cmd: QUIT_EDITOR
[2025.05.27-11.07.51:256][527]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.05.27-11.07.51:262][527]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.05.27-11.07.51:262][527]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.05.27-11.07.51:262][527]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.05.27-11.07.51:271][527]LogWorld: UWorld::CleanupWorld for TestLevel, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:275][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:275][527]LogWorldPartition: UWorldPartition::Uninitialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel
[2025.05.27-11.07.51:288][527]LogStylusInput: Shutting down StylusInput subsystem.
[2025.05.27-11.07.51:289][527]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.05.27-11.07.51:292][527]LogWorld: UWorld::CleanupWorld for World_27, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:292][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:294][527]LogWorld: UWorld::CleanupWorld for World_29, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:294][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:295][527]LogWorld: UWorld::CleanupWorld for World_22, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:295][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:295][527]LogWorld: UWorld::CleanupWorld for World_21, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:295][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:295][527]LogWorld: UWorld::CleanupWorld for World_20, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:295][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:296][527]LogWorld: UWorld::CleanupWorld for World_19, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:296][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:296][527]LogWorld: UWorld::CleanupWorld for World_18, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:296][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:296][527]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:296][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:297][527]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:297][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:298][527]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:298][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:298][527]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:298][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:298][527]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:298][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:298][527]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:298][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:299][527]LogWorld: UWorld::CleanupWorld for World_28, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:299][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:299][527]LogWorld: UWorld::CleanupWorld for World_26, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.07.51:299][527]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.07.51:300][527]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.05.27-11.07.51:306][527]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroying ET Analytics provider
[2025.05.27-11.07.51:306][527]LogAnalytics: Display: [UEEditor.Rocket.Release] Ended ET Analytics provider session
[2025.05.27-11.07.51:306][527]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroyed ET Analytics provider
[2025.05.27-11.07.51:307][527]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.05.27-11.07.51:307][527]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.05.27-11.07.51:307][527]LogAudio: Display: Audio Device unregistered from world 'TestLevel'.
[2025.05.27-11.07.51:307][527]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.05.27-11.07.51:307][527]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.05.27-11.07.51:310][527]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.05.27-11.07.51:316][527]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.05.27-11.07.51:316][527]LogAudio: Display: Audio Device Manager Shutdown
[2025.05.27-11.07.51:318][527]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.05.27-11.07.51:320][527]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.05.27-11.07.51:320][527]LogExit: Preparing to exit.
[2025.05.27-11.07.51:475][527]LogUObjectHash: Compacting FUObjectHashTables data took   1.25ms
[2025.05.27-11.07.52:700][527]LogEditorDataStorage: Deinitializing
[2025.05.27-11.07.53:557][527]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.05.27-11.07.53:566][527]LogExit: Editor shut down
[2025.05.27-11.07.53:572][527]LogExit: Transaction tracking system shut down
[2025.05.27-11.07.53:713][527]LogExit: Object subsystem successfully closed.
[2025.05.27-11.07.53:810][527]LogShaderCompilers: Display: Shaders left to compile 0
[2025.05.27-11.07.53:856][527]LogMemoryProfiler: Shutdown
[2025.05.27-11.07.53:856][527]LogNetworkingProfiler: Shutdown
[2025.05.27-11.07.53:856][527]LoadingProfiler: Shutdown
[2025.05.27-11.07.53:856][527]LogTimingProfiler: Shutdown
[2025.05.27-11.07.53:863][527]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.27-11.07.53:863][527]LogBlenderLink: Closing listener socket
[2025.05.27-11.07.53:863][527]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.27-11.07.54:208][527]LogChaosDD: Chaos Debug Draw Shutdown
[2025.05.27-11.07.54:238][527]LogHttp: Warning: [FHttpManager::Shutdown] Unbinding delegates for 1 outstanding Http Requests:
[2025.05.27-11.07.54:238][527]LogHttp: Warning: 	verb=[POST] url=[https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7B6FE59F02-4C25-1EF6-699C-6087437A59F2%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.5.4-40574608%2B%2B%2BUE5%2BRelease-5.5&UserID=5eff80b14364fb2f37e5468dbd2f7de6%7C0de775007ac941b984ac36d970a4fb1c%7C6ba94555-8ddf-4db5-b84c-ec5c48d27de3&AppEnvironment=datacollector-binary&UploadType=eteventstream] refs=[2] status=Processing
[2025.05.27-11.07.55:303][527]LogEOSSDK: FEOSSDKManager::Shutdown EOS_Shutdown Result=[EOS_Success]
[2025.05.27-11.07.55:309][527]LogStudioTelemetry: Display: Shutdown StudioTelemetry Module
[2025.05.27-11.07.55:309][527]LogNFORDenoise: NFORDenoise function shutting down
[2025.05.27-11.07.55:309][527]RenderDocPlugin: plugin has been unloaded.
[2025.05.27-11.07.55:310][527]LogXGEController: Cleaning working directory: C:/Users/<USER>/AppData/Local/Temp/UnrealXGEWorkingDir/
[2025.05.27-11.07.55:311][527]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
[2025.05.27-11.07.55:311][527]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
[2025.05.27-11.07.55:311][527]LogPakFile: Destroying PakPlatformFile
[2025.05.27-11.07.55:817][527]LogD3D12RHI: ~FD3D12DynamicRHI
[2025.05.27-11.07.55:891][527]LogExit: Exiting.
[2025.05.27-11.07.55:903][527]Log file closed, 05/27/25 16:37:55
