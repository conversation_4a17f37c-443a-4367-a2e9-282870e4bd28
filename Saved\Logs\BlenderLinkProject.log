﻿Log file open, 05/27/25 17:09:29
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=32940)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: BlenderLinkProject
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\Plugins\BlenderLinkProject\BlenderLinkProject.uproject""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.226791
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-C9EE4A7A4CC750B50C551EBF92A4CED9
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/Plugins/BlenderLinkProject/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading Android ini files took 0.05 seconds
LogConfig: Display: Loading IOS ini files took 0.05 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.05 seconds
LogConfig: Display: Loading Mac ini files took 0.05 seconds
LogAssetRegistry: Display: Asset registry cache read as 43.8 MiB from H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_0.bin
LogConfig: Display: Loading VisionOS ini files took 0.05 seconds
LogConfig: Display: Loading Unix ini files took 0.05 seconds
LogConfig: Display: Loading TVOS ini files took 0.06 seconds
LogConfig: Display: Loading Windows ini files took 0.06 seconds
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogConfig: Display: Loading LinuxArm64 ini files took 0.06 seconds
LogConfig: Display: Loading Linux ini files took 0.06 seconds
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin Mutable
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaIOFramework
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SkeletalMerging
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin XRBase
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin CaptureData
LogPluginManager: Mounting Engine plugin LensComponent
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin OpenColorIO
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LiveLinkControlRig
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RigLogicMutable
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin MetaHuman
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin ARUtilities
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Engine plugin AppleARKitFaceSupport
LogPluginManager: Mounting Engine plugin AppleARKit
LogPluginManager: Mounting Project plugin BlenderLink
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum ********** concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: AMD Radeon RX 6900 XT
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: 
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.54ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMerging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XRBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CaptureData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.15ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LiveLinkControlRig' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RigLogicMutable' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MetaHuman' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ARUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKitFaceSupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKit' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlenderLink' had been unloaded. Reloading on-demand took 0.06ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.05.27-11.39.30:263][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.05.27-11.39.30:263][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.05.27-11.39.30:263][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.05.27-11.39.30:263][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.05.27-11.39.30:263][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.05.27-11.39.30:263][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.05.27-11.39.30:263][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.05.27-11.39.30:263][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.05.27-11.39.30:263][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.05.27-11.39.30:263][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.05.27-11.39.30:264][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.05.27-11.39.30:264][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.05.27-11.39.30:264][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.05.27-11.39.30:264][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.05.27-11.39.30:264][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.05.27-11.39.30:264][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.05.27-11.39.30:264][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.05.27-11.39.30:264][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.05.27-11.39.30:264][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.05.27-11.39.30:266][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.05.27-11.39.30:266][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.05.27-11.39.30:266][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.05.27-11.39.30:266][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.05.27-11.39.30:266][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.05.27-11.39.30:267][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.05.27-11.39.30:267][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.05.27-11.39.30:267][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.05.27-11.39.30:267][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.05.27-11.39.30:267][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.05.27-11.39.30:269][  0]LogRHI: Using Default RHI: D3D12
[2025.05.27-11.39.30:269][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.27-11.39.30:269][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.27-11.39.30:272][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.05.27-11.39.30:273][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.27-11.39.30:367][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.05.27-11.39.30:367][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.27-11.39.30:367][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 3 output[s]
[2025.05.27-11.39.30:368][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.27-11.39.30:368][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.05.27-11.39.30:524][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.05.27-11.39.30:524][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.27-11.39.30:524][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.27-11.39.30:524][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.05.27-11.39.30:524][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.05.27-11.39.30:531][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.05.27-11.39.30:531][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.05.27-11.39.30:531][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.27-11.39.30:531][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.05.27-11.39.30:531][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.05.27-11.39.30:531][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.27-11.39.30:531][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.05.27-11.39.30:531][  0]LogHAL: Display: Platform has ~ 64 GB [68631805952 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.05.27-11.39.30:531][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.05.27-11.39.30:531][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.27-11.39.30:531][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.05.27-11.39.30:531][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.05.27-11.39.30:531][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.27-11.39.30:531][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.05.27-11.39.30:531][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.05.27-11.39.30:531][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.05.27-11.39.30:531][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.05.27-11.39.30:532][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.05.27-11.39.30:532][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.27-11.39.30:532][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.05.27-11.39.30:532][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.05.27-11.39.30:532][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/Plugins/BlenderLinkProject/Saved/Config/WindowsEditor/Editor.ini]
[2025.05.27-11.39.30:532][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.05.27-11.39.30:532][  0]LogInit: User: Shashank
[2025.05.27-11.39.30:532][  0]LogInit: CPU Page size=4096, Cores=16
[2025.05.27-11.39.30:532][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.05.27-11.39.30:789][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.05.27-11.39.30:789][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.05.27-11.39.30:789][  0]LogMemory: Process Physical Memory: 626.95 MB used, 644.58 MB peak
[2025.05.27-11.39.30:789][  0]LogMemory: Process Virtual Memory: 755.89 MB used, 755.89 MB peak
[2025.05.27-11.39.30:789][  0]LogMemory: Physical Memory: 24786.61 MB used,  40665.79 MB free, 65452.39 MB total
[2025.05.27-11.39.30:789][  0]LogMemory: Virtual Memory: 40926.07 MB used,  28622.32 MB free, 69548.39 MB total
[2025.05.27-11.39.30:789][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.05.27-11.39.30:793][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.05.27-11.39.30:798][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.05.27-11.39.30:798][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.05.27-11.39.30:802][  0]LogInit: Using OS detected language (en-GB).
[2025.05.27-11.39.30:802][  0]LogInit: Using OS detected locale (en-IN).
[2025.05.27-11.39.30:803][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.05.27-11.39.30:803][  0]LogInit: Setting process to per monitor DPI aware
[2025.05.27-11.39.31:062][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.05.27-11.39.31:062][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.05.27-11.39.31:063][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.05.27-11.39.31:075][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.05.27-11.39.31:075][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.05.27-11.39.31:153][  0]LogRHI: Using Default RHI: D3D12
[2025.05.27-11.39.31:153][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.27-11.39.31:153][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.27-11.39.31:153][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.27-11.39.31:153][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.27-11.39.31:153][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.05.27-11.39.31:154][  0]LogWindows: Attached monitors:
[2025.05.27-11.39.31:154][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY5'
[2025.05.27-11.39.31:154][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY1' [PRIMARY]
[2025.05.27-11.39.31:154][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY6'
[2025.05.27-11.39.31:154][  0]LogWindows: Found 3 attached monitors.
[2025.05.27-11.39.31:154][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.27-11.39.31:154][  0]LogRHI: RHI Adapter Info:
[2025.05.27-11.39.31:154][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.05.27-11.39.31:154][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.27-11.39.31:154][  0]LogRHI:      Driver Date: 4-25-2025
[2025.05.27-11.39.31:154][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.05.27-11.39.31:179][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.05.27-11.39.31:244][  0]LogNvidiaAftermath: Aftermath initialized
[2025.05.27-11.39.31:244][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.05.27-11.39.31:315][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: Bindless resources are supported
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: Raster order views are supported
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.05.27-11.39.31:315][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.05.27-11.39.31:342][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000077D964C5300)
[2025.05.27-11.39.31:342][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000077D964C5580)
[2025.05.27-11.39.31:342][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000077D964C5800)
[2025.05.27-11.39.31:342][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.05.27-11.39.31:342][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.05.27-11.39.31:342][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.05.27-11.39.31:342][  0]LogRHI: Texture pool is 9808 MB (70% of 14012 MB)
[2025.05.27-11.39.31:342][  0]LogD3D12RHI: Async texture creation enabled
[2025.05.27-11.39.31:342][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.05.27-11.39.31:353][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.05.27-11.39.31:358][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.05.27-11.39.31:364][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all'
[2025.05.27-11.39.31:364][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all" ]
[2025.05.27-11.39.31:380][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.05.27-11.39.31:380][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.05.27-11.39.31:380][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.05.27-11.39.31:380][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.05.27-11.39.31:380][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.05.27-11.39.31:380][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.05.27-11.39.31:380][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.05.27-11.39.31:381][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.05.27-11.39.31:381][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.05.27-11.39.31:403][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.05.27-11.39.31:403][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.05.27-11.39.31:403][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.05.27-11.39.31:403][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.05.27-11.39.31:403][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.05.27-11.39.31:403][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.05.27-11.39.31:403][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.05.27-11.39.31:403][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.05.27-11.39.31:403][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.05.27-11.39.31:403][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.05.27-11.39.31:417][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.05.27-11.39.31:417][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.05.27-11.39.31:429][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.05.27-11.39.31:429][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.05.27-11.39.31:429][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.05.27-11.39.31:429][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.05.27-11.39.31:441][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.05.27-11.39.31:441][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.05.27-11.39.31:441][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.05.27-11.39.31:454][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.05.27-11.39.31:454][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.05.27-11.39.31:454][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.05.27-11.39.31:454][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.05.27-11.39.31:465][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.05.27-11.39.31:465][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.05.27-11.39.31:481][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.05.27-11.39.31:481][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.05.27-11.39.31:481][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.05.27-11.39.31:481][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.05.27-11.39.31:481][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.05.27-11.39.31:520][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.05.27-11.39.31:522][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.05.27-11.39.31:522][  0]LogTargetPlatformManager:   SF_METAL
[2025.05.27-11.39.31:522][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.05.27-11.39.31:522][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.05.27-11.39.31:522][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.05.27-11.39.31:522][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.05.27-11.39.31:522][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.05.27-11.39.31:522][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.05.27-11.39.31:522][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager:   VVM_1_0
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.05.27-11.39.31:523][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.05.27-11.39.31:523][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.05.27-11.39.31:523][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.05.27-11.39.31:525][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.05.27-11.39.31:525][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.05.27-11.39.31:525][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.05.27-11.39.31:525][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.27-11.39.31:525][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.05.27-11.39.31:582][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.05.27-11.39.31:582][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.27-11.39.31:582][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.05.27-11.39.31:582][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.05.27-11.39.31:582][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.27-11.39.31:582][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.27-11.39.31:582][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.05.27-11.39.31:582][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 35464 --child-id Zen_35464_Startup'
[2025.05.27-11.39.31:642][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.05.27-11.39.31:642][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.060 seconds
[2025.05.27-11.39.31:643][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.05.27-11.39.31:646][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.00 seconds.
[2025.05.27-11.39.31:646][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.01ms. RandomReadSpeed=2676.90MBs, RandomWriteSpeed=401.58MBs. Assigned SpeedClass 'Local'
[2025.05.27-11.39.31:647][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.05.27-11.39.31:647][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.05.27-11.39.31:647][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.05.27-11.39.31:647][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.05.27-11.39.31:647][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.05.27-11.39.31:647][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.05.27-11.39.31:647][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.05.27-11.39.31:648][  0]LogShaderCompilers: Guid format shader working directory is 19 characters bigger than the processId version (H:/Plugins/BlenderLinkProject/Intermediate/Shaders/WorkingDirectory/35464/).
[2025.05.27-11.39.31:648][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/210D0750482796BDE91418B6172B7105/'.
[2025.05.27-11.39.31:648][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.05.27-11.39.31:648][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.05.27-11.39.31:648][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/Plugins/BlenderLinkProject/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.05.27-11.39.31:649][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.05.27-11.39.32:035][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.05.27-11.39.32:594][  0]LogSlate: Using FreeType 2.10.0
[2025.05.27-11.39.32:594][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.05.27-11.39.32:594][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.27-11.39.32:594][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.27-11.39.32:595][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.27-11.39.32:595][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.27-11.39.32:595][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.27-11.39.32:595][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.27-11.39.32:616][  0]LogAssetRegistry: FAssetRegistry took 0.0019 seconds to start up
[2025.05.27-11.39.32:617][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.05.27-11.39.32:622][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin.
[2025.05.27-11.39.32:622][  0]LogAssetRegistry: Error: Package is unloadable: H:/Plugins/BlenderLinkProject/Content/SM_Rock_ukxmdhuga.uasset. Reason: Invalid value for PACKAGE_FILE_TAG at start of file.
[2025.05.27-11.39.32:791][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-11.39.32:792][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.05.27-11.39.32:792][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.05.27-11.39.32:792][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.05.27-11.39.32:802][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.05.27-11.39.32:802][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.05.27-11.39.32:826][  0]LogDeviceProfileManager: Active device profile: [0000077DB56EC200][0000077DB37F0000 66] WindowsEditor
[2025.05.27-11.39.32:826][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.05.27-11.39.32:826][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.05.27-11.39.32:828][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.05.27-11.39.32:828][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.05.27-11.39.32:855][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:855][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.05.27-11.39.32:855][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-11.39.32:855][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:855][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.05.27-11.39.32:856][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-11.39.32:856][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:856][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.05.27-11.39.32:856][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-11.39.32:856][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:856][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.05.27-11.39.32:856][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-11.39.32:856][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:857][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:857][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.05.27-11.39.32:857][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.27-11.39.32:857][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:857][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.05.27-11.39.32:857][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.27-11.39.32:857][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:857][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:857][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.05.27-11.39.32:857][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-11.39.32:857][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:857][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.05.27-11.39.32:857][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-11.39.32:857][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:858][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-11.39.32:858][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:858][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.05.27-11.39.32:858][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.27-11.39.32:858][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:858][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.05.27-11.39.32:858][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.27-11.39.32:859][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-11.39.32:859][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.05.27-11.39.32:859][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-11.39.32:859][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:859][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.05.27-11.39.32:859][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-11.39.32:859][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.05.27-11.39.32:859][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.27-11.39.32:859][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:859][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.05.27-11.39.32:859][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.27-11.39.32:860][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.05.27-11.39.32:860][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-11.39.32:860][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-11.39.32:860][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.05.27-11.39.32:860][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-11.39.33:004][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.05.27-11.39.33:004][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.05.27-11.39.33:004][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.05.27-11.39.33:004][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.05.27-11.39.33:004][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.05.27-11.39.33:118][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.44ms
[2025.05.27-11.39.33:135][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.45ms
[2025.05.27-11.39.33:145][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.46ms
[2025.05.27-11.39.33:146][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.44ms
[2025.05.27-11.39.33:320][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.05.27-11.39.33:320][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.05.27-11.39.33:325][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.05.27-11.39.33:325][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.05.27-11.39.33:325][  0]LogLiveCoding: Display: First instance in process group "UE_BlenderLinkProject_0xe99fe6f9", spawning console
[2025.05.27-11.39.33:328][  0]LogLiveCoding: Display: Waiting for server
[2025.05.27-11.39.33:340][  0]LogSlate: Border
[2025.05.27-11.39.33:340][  0]LogSlate: BreadcrumbButton
[2025.05.27-11.39.33:340][  0]LogSlate: Brushes.Title
[2025.05.27-11.39.33:340][  0]LogSlate: Default
[2025.05.27-11.39.33:340][  0]LogSlate: Icons.Save
[2025.05.27-11.39.33:340][  0]LogSlate: Icons.Toolbar.Settings
[2025.05.27-11.39.33:340][  0]LogSlate: ListView
[2025.05.27-11.39.33:340][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.05.27-11.39.33:340][  0]LogSlate: SoftwareCursor_Grab
[2025.05.27-11.39.33:340][  0]LogSlate: TableView.DarkRow
[2025.05.27-11.39.33:340][  0]LogSlate: TableView.Row
[2025.05.27-11.39.33:340][  0]LogSlate: TreeView
[2025.05.27-11.39.33:395][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.05.27-11.39.33:398][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.05.27-11.39.33:400][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 1.728 ms
[2025.05.27-11.39.33:408][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.44ms
[2025.05.27-11.39.33:421][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.05.27-11.39.33:421][  0]LogInit: XR: MultiViewport is Disabled
[2025.05.27-11.39.33:421][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.05.27-11.39.33:422][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.05.27-11.39.33:474][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.05.27-11.39.33:478][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.05.27-11.39.33:478][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.05.27-11.39.33:478][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:54621'.
[2025.05.27-11.39.33:480][  0]LogUdpMessaging: Display: Added local interface '192.168.31.37' to multicast group '230.0.0.1:6666'
[2025.05.27-11.39.33:480][  0]LogUdpMessaging: Display: Added local interface '172.21.160.1' to multicast group '230.0.0.1:6666'
[2025.05.27-11.39.33:480][  0]LogUdpMessaging: Display: Added local interface '172.30.208.1' to multicast group '230.0.0.1:6666'
[2025.05.27-11.39.33:581][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.05.27-11.39.33:582][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.05.27-11.39.33:596][  0]LogMetaSound: MetaSound Engine Initialized
[2025.05.27-11.39.33:788][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.27-11.39.33:788][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.27-11.39.33:824][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.46ms
[2025.05.27-11.39.33:912][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: C98B470867344B4A8000000000005B00 | Instance: 4EE2A0F64B0A8306D9448B911BF008D4 (DESKTOP-E41IK6R-35464).
[2025.05.27-11.39.34:058][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.05.27-11.39.34:058][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.05.27-11.39.34:058][  0]LogNNERuntimeORT: 1: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.05.27-11.39.34:058][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.05.27-11.39.34:058][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.05.27-11.39.34:067][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.05.27-11.39.34:248][  0]LogMutable: Creating Mutable Customizable Object System.
[2025.05.27-11.39.34:322][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.05.27-11.39.34:333][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.27-11.39.34:333][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.27-11.39.34:390][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.05.27-11.39.34:390][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.05.27-11.39.34:391][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.05.27-11.39.34:391][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.05.27-11.39.34:391][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.05.27-11.39.34:391][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.05.27-11.39.34:392][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.05.27-11.39.34:392][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.05.27-11.39.34:392][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.05.27-11.39.34:392][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.05.27-11.39.34:392][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.05.27-11.39.34:392][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.05.27-11.39.34:392][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.05.27-11.39.34:393][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.05.27-11.39.34:393][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.05.27-11.39.34:394][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.05.27-11.39.34:394][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.05.27-11.39.34:394][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.05.27-11.39.34:394][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.05.27-11.39.34:394][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.05.27-11.39.34:395][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.05.27-11.39.34:395][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.05.27-11.39.34:395][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.05.27-11.39.34:395][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.05.27-11.39.34:395][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.05.27-11.39.34:395][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.05.27-11.39.34:395][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.05.27-11.39.34:395][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.05.27-11.39.34:396][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.05.27-11.39.34:396][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.05.27-11.39.34:396][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.05.27-11.39.34:397][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.05.27-11.39.34:397][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.05.27-11.39.34:397][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.05.27-11.39.34:397][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.05.27-11.39.34:478][  0]SourceControl: Revision control is disabled
[2025.05.27-11.39.34:492][  0]SourceControl: Revision control is disabled
[2025.05.27-11.39.34:513][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.47ms
[2025.05.27-11.39.34:519][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.42ms
[2025.05.27-11.39.34:718][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.27-11.39.34:718][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.27-11.39.34:767][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.05.27-11.39.35:189][  0]LogSkeletalMesh: Building Skeletal Mesh Face_Archetype...
[2025.05.27-11.39.35:269][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (Face_Archetype) ...
[2025.05.27-11.39.41:288][  0]LogSkeletalMesh: Skeletal mesh [/MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-11.39.41:298][  0]LogSkeletalMesh: Built Skeletal Mesh [6.11s] /MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype
[2025.05.27-11.39.41:317][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.27-11.39.41:317][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.27-11.39.41:317][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.27-11.39.41:317][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.27-11.39.41:317][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.27-11.39.41:317][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.27-11.39.41:324][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.27-11.39.41:324][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.27-11.39.41:362][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.05.27-11.39.41:382][  0]LogCollectionManager: Loaded 0 collections in 0.001046 seconds
[2025.05.27-11.39.41:385][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Saved/Collections/' took 0.00s
[2025.05.27-11.39.41:385][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Developers/Shashank/Collections/' took 0.00s
[2025.05.27-11.39.41:387][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Collections/' took 0.00s
[2025.05.27-11.39.41:427][  0]LogBlenderLink: Initializing BlenderLink socket listener
[2025.05.27-11.39.41:428][  0]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.27-11.39.41:428][  0]LogBlenderLink: Set socket buffer sizes to 524288 bytes
[2025.05.27-11.39.41:428][  0]LogBlenderLink: Binding socket to 127.0.0.1:2907
[2025.05.27-11.39.41:428][  0]LogBlenderLink: Warning: Socket bind success: true
[2025.05.27-11.39.41:428][  0]LogBlenderLink: Warning: Socket listen success: true
[2025.05.27-11.39.41:428][  0]LogBlenderLink: Waiting for client connection...
[2025.05.27-11.39.41:442][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.27-11.39.41:442][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.27-11.39.41:442][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.27-11.39.41:442][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.27-11.39.41:442][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.27-11.39.41:442][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.27-11.39.41:448][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.27-11.39.41:448][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.27-11.39.41:466][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-05-27T11:39:41.466Z using C
[2025.05.27-11.39.41:466][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=BlenderLinkProject, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.05.27-11.39.41:466][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.27-11.39.41:466][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.05.27-11.39.41:472][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.05.27-11.39.41:472][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.05.27-11.39.41:472][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.05.27-11.39.41:472][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000054
[2025.05.27-11.39.41:472][  0]LogFab: Display: Logging in using persist
[2025.05.27-11.39.41:473][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.05.27-11.39.41:503][  0]LogUObjectArray: 52283 objects as part of root set at end of initial load.
[2025.05.27-11.39.41:503][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.05.27-11.39.41:514][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 38255 public script object entries (1030.17 KB)
[2025.05.27-11.39.41:514][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.05.27-11.39.41:619][  0]LogEngine: Initializing Engine...
[2025.05.27-11.39.41:622][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.05.27-11.39.41:622][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.05.27-11.39.41:688][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.05.27-11.39.41:700][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.05.27-11.39.41:708][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.05.27-11.39.41:708][  0]LogInit: Texture streaming: Enabled
[2025.05.27-11.39.41:715][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.05.27-11.39.41:726][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.05.27-11.39.41:730][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.05.27-11.39.41:730][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.05.27-11.39.41:730][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.05.27-11.39.41:730][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.05.27-11.39.41:730][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.05.27-11.39.41:730][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.05.27-11.39.41:730][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.05.27-11.39.41:730][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.05.27-11.39.41:730][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.05.27-11.39.41:730][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.05.27-11.39.41:730][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.05.27-11.39.41:730][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.05.27-11.39.41:730][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.05.27-11.39.41:730][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.05.27-11.39.41:730][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.05.27-11.39.41:734][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.05.27-11.39.41:786][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter Input (VB-Audio Voicemeeter VAIO)
[2025.05.27-11.39.41:786][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.05.27-11.39.41:787][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.05.27-11.39.41:787][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.05.27-11.39.41:788][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.05.27-11.39.41:788][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.05.27-11.39.41:791][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.05.27-11.39.41:791][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.05.27-11.39.41:791][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.05.27-11.39.41:791][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.05.27-11.39.41:791][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.05.27-11.39.41:796][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.05.27-11.39.41:798][  0]LogInit: Undo buffer set to 256 MB
[2025.05.27-11.39.41:798][  0]LogInit: Transaction tracking system initialized
[2025.05.27-11.39.41:808][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded H:/Plugins/BlenderLinkProject/Saved/SourceControl/UncontrolledChangelists.json
[2025.05.27-11.39.41:847][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.46ms
[2025.05.27-11.39.41:849][  0]LocalizationService: Localization service is disabled
[2025.05.27-11.39.41:858][  0]LogTimingProfiler: Initialize
[2025.05.27-11.39.41:858][  0]LogTimingProfiler: OnSessionChanged
[2025.05.27-11.39.41:858][  0]LoadingProfiler: Initialize
[2025.05.27-11.39.41:858][  0]LoadingProfiler: OnSessionChanged
[2025.05.27-11.39.41:858][  0]LogNetworkingProfiler: Initialize
[2025.05.27-11.39.41:858][  0]LogNetworkingProfiler: OnSessionChanged
[2025.05.27-11.39.41:858][  0]LogMemoryProfiler: Initialize
[2025.05.27-11.39.41:858][  0]LogMemoryProfiler: OnSessionChanged
[2025.05.27-11.39.41:974][  0]LogAutoReimportManager: Warning: Unable to watch directory H:/Plugins/BlenderLinkProject/Content/ as it will conflict with another watching H:/Plugins/BlenderLinkProject/Content/.
[2025.05.27-11.39.41:982][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/' took 0.01s
[2025.05.27-11.39.42:009][  0]LogPython: Using Python 3.11.8
[2025.05.27-11.39.42:943][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.05.27-11.39.42:954][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.05.27-11.39.42:981][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.05.27-11.39.43:037][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.05.27-11.39.43:038][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.05.27-11.39.43:057][  0]LogEditorDataStorage: Initializing
[2025.05.27-11.39.43:058][  0]LogEditorDataStorage: Initialized
[2025.05.27-11.39.43:059][  0]LogWindows: Attached monitors:
[2025.05.27-11.39.43:059][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY5'
[2025.05.27-11.39.43:059][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY1' [PRIMARY]
[2025.05.27-11.39.43:059][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY6'
[2025.05.27-11.39.43:059][  0]LogWindows: Found 3 attached monitors.
[2025.05.27-11.39.43:059][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.27-11.39.43:070][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.05.27-11.39.43:072][  0]SourceControl: Revision control is disabled
[2025.05.27-11.39.43:072][  0]LogUnrealEdMisc: Loading editor; pre map load, took 13.514
[2025.05.27-11.39.43:072][  0]Cmd: MAP LOAD FILE="H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/TestLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.05.27-11.39.43:074][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.05.27-11.39.43:074][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.39.43:117][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.05.27-11.39.43:118][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.52ms
[2025.05.27-11.39.43:124][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'TestLevel'.
[2025.05.27-11.39.43:124][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world TestLevel
[2025.05.27-11.39.43:126][  0]LogWorldPartition: ULevel::OnLevelLoaded(TestLevel)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.05.27-11.39.43:126][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.05.27-11.39.43:126][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.05.27-11.39.43:176][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.05.27-11.39.43:885][  0]LogAssetRegistry: Display: Asset registry cache written as 43.8 MiB to H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin
[2025.05.27-11.39.46:039][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.27-11.39.46:044][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.27-11.39.46:046][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.27-11.39.46:047][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.05.27-11.39.46:047][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.05.27-11.39.46:047][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.27-11.39.46:048][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.05.27-11.39.48:011][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.05.27-11.39.48:059][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.05.27-11.39.48:435][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-11.39.48:438][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.05.27-11.39.48:450][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.05.27-11.39.48:451][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.05.27-11.39.48:811][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-11.39.48:814][  0]LogSkeletalMesh: Built Skeletal Mesh [0.36s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.05.27-11.39.49:892][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-11.39.49:894][  0]LogSkeletalMesh: Built Skeletal Mesh [1.44s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.05.27-11.39.49:997][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.05.27-11.39.50:203][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-11.39.50:204][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.05.27-11.39.50:206][  0]LogSkeletalMesh: Built Skeletal Mesh [0.21s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.05.27-11.39.50:514][  0]LogWorldPartition: Display: WorldPartition initialize took 7.38 sec
[2025.05.27-11.39.50:589][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.05.27-11.39.55:296][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-11.39.55:309][  0]LogSkeletalMesh: Built Skeletal Mesh [5.11s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.05.27-11.39.55:923][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.05.27-11.39.56:136][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.07ms
[2025.05.27-11.39.56:137][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.05.27-11.39.56:139][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 1.342ms to complete.
[2025.05.27-11.39.56:146][  0]LogUnrealEdMisc: Total Editor Startup Time, took 26.589
[2025.05.27-11.39.56:303][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.05.27-11.39.56:391][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.27-11.39.56:439][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.27-11.39.56:499][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.27-11.39.56:556][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.27-11.39.56:588][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-11.39.56:588][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.05.27-11.39.56:588][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-11.39.56:588][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.05.27-11.39.56:588][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-11.39.56:589][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.05.27-11.39.56:589][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-11.39.56:589][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.05.27-11.39.56:589][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-11.39.56:589][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.05.27-11.39.56:589][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-11.39.56:589][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.05.27-11.39.56:589][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-11.39.56:589][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.05.27-11.39.56:590][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-11.39.56:590][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.05.27-11.39.56:590][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-11.39.56:590][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.05.27-11.39.56:590][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-11.39.56:590][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.05.27-11.39.56:628][  0]LogSlate: Took 0.000066 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.05.27-11.39.56:786][  0]LogStall: Startup...
[2025.05.27-11.39.56:788][  0]LogStall: Startup complete.
[2025.05.27-11.39.56:793][  0]LogLoad: (Engine Initialization) Total time: 27.24 seconds
[2025.05.27-11.39.56:971][  0]LogAssetRegistry: AssetRegistryGather time 0.0741s: AssetDataDiscovery 0.0129s, AssetDataGather 0.0113s, StoreResults 0.0499s. Wall time 24.3570s.
	NumCachedDirectories 0. NumUncachedDirectories 1854. NumCachedFiles 7963. NumUncachedFiles 1.
	BackgroundTickInterruptions 1.
[2025.05.27-11.39.56:988][  0]LogSourceControl: Uncontrolled asset enumeration started...
[2025.05.27-11.39.56:988][  0]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.05.27-11.39.57:089][  0]LogSourceControl: Uncontrolled asset enumeration finished in 0.100928 seconds (Found 7940 uncontrolled assets)
[2025.05.27-11.39.57:162][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.05.27-11.39.57:162][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.05.27-11.39.57:310][  0]LogSlate: Took 0.000079 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.05.27-11.39.57:361][  0]LogSlate: Took 0.000112 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.05.27-11.39.57:364][  0]LogSlate: Took 0.000058 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.05.27-11.39.57:404][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.05.27-11.39.57:404][  0]LogStreaming: Display: FlushAsyncLoading(501): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-11.39.57:407][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.05.27-11.39.57:407][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.05.27-11.39.57:407][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.27-11.39.57:472][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.27-11.39.57:472][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.05.27-11.39.57:472][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.05.27-11.39.57:472][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.05.27-11.39.57:472][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.27-11.39.57:533][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.27-11.39.57:533][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.05.27-11.39.57:586][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-11.39.57:595][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.05.27-11.39.57:595][  0]LogFab: Display: Logging in using exchange code
[2025.05.27-11.39.57:595][  0]LogFab: Display: Reading exchange code from commandline
[2025.05.27-11.39.57:595][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.05.27-11.39.57:596][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.05.27-11.39.57:644][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.05.27-11.39.57:651][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 55.230 ms
[2025.05.27-11.39.57:655][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BlenderLinkProjectEditor Win64 Development
[2025.05.27-11.39.57:748][  1]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 26.76 ms. Compile time 16.52 ms, link time 9.96 ms.
[2025.05.27-11.39.58:020][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.05.27-11.39.58:708][  9]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 17.154026
[2025.05.27-11.39.58:709][  9]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.05.27-11.39.58:709][  9]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 17.235199
[2025.05.27-11.39.59:186][ 16]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.27-11.39.59:574][ 20]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 17.990477
[2025.05.27-11.39.59:576][ 20]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 607272702
[2025.05.27-11.39.59:577][ 20]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 17.990477, Update Interval: 346.647552
[2025.05.27-11.40.06:869][122]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.40.16:897][316]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.40.26:906][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.40.36:911][695]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.40.46:956][887]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.40.56:987][ 76]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.41.07:001][272]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.41.17:020][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.41.27:057][656]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.41.31:647][745]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.05.27-11.41.37:074][848]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.41.47:119][ 41]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.41.57:134][233]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.42.07:136][427]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.42.17:162][619]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.42.27:198][811]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.42.37:205][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.42.47:237][199]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.42.57:277][395]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.43.07:292][586]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.43.17:315][778]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.43.27:322][972]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.43.37:340][169]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.43.46:678][350]LogSlate: Took 0.000070 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.05.27-11.43.47:386][360]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.43.57:413][565]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.44.07:457][763]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.44.17:486][958]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.44.27:515][149]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.44.37:569][329]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.44.47:568][513]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.44.57:595][701]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.45.07:599][893]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.45.17:604][ 88]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.45.27:653][278]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.45.37:651][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.45.46:200][608]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 364.727631
[2025.05.27-11.45.46:592][615]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-11.45.46:592][615]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 365.065582, Update Interval: 324.952545
[2025.05.27-11.45.47:656][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.45.57:691][820]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.46.07:723][  9]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.46.17:746][211]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.46.27:755][403]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.46.37:771][595]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.46.47:799][789]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.46.57:812][980]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.47.07:844][171]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.47.17:876][360]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.47.27:920][549]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.47.37:965][746]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.47.47:994][943]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.47.57:999][136]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.48.08:013][334]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.48.18:029][533]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.48.28:071][732]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.48.38:075][931]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.48.48:110][132]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.48.58:116][329]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.49.08:128][533]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.49.18:158][735]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.49.28:207][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.49.38:236][131]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.49.48:251][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.49.58:281][517]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.50.08:307][710]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.50.18:344][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.50.28:394][ 99]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.50.38:409][296]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.50.48:427][496]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.50.58:463][695]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.51.08:494][892]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.51.14:449][ 10]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 692.975647
[2025.05.27-11.51.14:812][ 17]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-11.51.14:812][ 17]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 693.288818, Update Interval: 300.146484
[2025.05.27-11.51.18:547][ 89]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.51.28:549][286]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.51.38:549][483]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.51.48:589][681]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.51.58:627][878]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.52.08:640][ 71]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.52.18:672][267]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.52.28:702][466]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.52.38:739][664]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.52.48:753][860]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.52.58:756][ 56]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.53.08:758][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.53.18:784][449]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.53.28:782][641]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.53.38:806][835]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.53.48:817][ 27]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.53.58:821][217]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.54.08:861][413]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.54.18:895][607]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.54.28:920][803]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.54.38:927][995]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.54.48:952][198]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.54.58:974][390]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.55.08:983][585]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.55.19:000][780]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.55.29:047][977]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.55.39:126][172]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.55.49:169][359]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.55.59:203][551]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.56.05:022][651]LogRenderer: Display: Invalidated clipmap due to WPO threshold change. This can occur due to resolution or FOV changes.
[2025.05.27-11.56.09:252][732]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.56.18:972][922]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 997.496399
[2025.05.27-11.56.19:269][928]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.56.19:370][930]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-11.56.19:370][930]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 997.843933, Update Interval: 322.077698
[2025.05.27-11.56.29:314][126]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.56.39:351][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.56.49:381][520]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.56.59:468][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.57.09:498][886]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.57.19:518][ 83]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.57.29:529][282]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.57.39:564][480]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.57.49:613][682]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.57.59:630][879]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.58.09:671][ 78]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.58.19:720][277]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.58.29:765][475]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.58.39:778][672]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.58.49:786][870]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.58.59:793][ 68]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.59.09:815][266]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.59.19:844][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.59.29:885][662]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.59.39:919][862]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.59.49:937][ 60]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-11.59.59:954][256]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.00.09:973][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.00.20:021][652]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.00.30:023][846]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.00.40:077][ 38]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.00.50:059][223]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.01.00:103][425]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.01.10:116][620]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.01.20:116][810]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.01.30:132][  5]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.01.40:179][203]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.01.50:207][399]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.01.53:779][469]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1332.301147
[2025.05.27-12.01.54:132][476]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-12.01.54:132][476]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1332.604858, Update Interval: 306.564545
[2025.05.27-12.02.00:244][595]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.02.10:272][792]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.02.20:320][990]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.02.30:364][186]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.02.40:364][380]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.02.50:376][575]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.03.00:426][771]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.03.10:445][968]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.03.20:447][165]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.03.30:496][363]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.03.40:530][561]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.03.50:546][759]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.03.54:840][800]LogRenderer: Display: Invalidated clipmap due to WPO threshold change. This can occur due to resolution or FOV changes.
[2025.05.27-12.03.55:033][801]LogRenderer: Display: Invalidated clipmap due to WPO threshold change. This can occur due to resolution or FOV changes.
[2025.05.27-12.04.00:559][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.04.10:566][ 76]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.04.20:621][202]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.04.24:101][244]Cmd: Interchange.FeatureFlags.Import.FBX False
[2025.05.27-12.04.24:101][244]Interchange.FeatureFlags.Import.FBX = "false"
[2025.05.27-12.04.30:612][371]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.04.39:037][535]LogStreaming: Display: FlushAsyncLoading(511): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-12.04.39:037][535]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/cartilage_lod0_mesh_PhysicsAsset (0x8306E6F3EEA7F81A) - The package to load does not exist on disk or in the loader
[2025.05.27-12.04.39:037][535]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/cartilage_lod0_mesh_PhysicsAsset'
[2025.05.27-12.04.39:307][535]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/cartilage_lod0_mesh.fbx)
[2025.05.27-12.04.39:401][535]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/cartilage_lod0_mesh.fbx
[2025.05.27-12.04.39:416][535]LogFbx: FBX Scene Loaded Succesfully
[2025.05.27-12.04.39:467][535]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader.MH_Friend_cartilage_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.27-12.04.39:478][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7D2800 with pending SubmitJob call.
[2025.05.27-12.04.39:478][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7D0A00 with pending SubmitJob call.
[2025.05.27-12.04.39:478][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7FB400 with pending SubmitJob call.
[2025.05.27-12.04.39:478][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7F8C00 with pending SubmitJob call.
[2025.05.27-12.04.39:479][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7F8200 with pending SubmitJob call.
[2025.05.27-12.04.39:479][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7FAA00 with pending SubmitJob call.
[2025.05.27-12.04.39:480][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7FD200 with pending SubmitJob call.
[2025.05.27-12.04.39:481][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7F1E00 with pending SubmitJob call.
[2025.05.27-12.04.39:481][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7D1E00 with pending SubmitJob call.
[2025.05.27-12.04.39:481][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7D3C00 with pending SubmitJob call.
[2025.05.27-12.04.39:481][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7F2800 with pending SubmitJob call.
[2025.05.27-12.04.39:482][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7F4600 with pending SubmitJob call.
[2025.05.27-12.04.39:483][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7F6E00 with pending SubmitJob call.
[2025.05.27-12.04.39:484][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7D5A00 with pending SubmitJob call.
[2025.05.27-12.04.39:484][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7FE600 with pending SubmitJob call.
[2025.05.27-12.04.39:484][535]LogFbx: Triangulating skeletal mesh cartilage_lod0_mesh
[2025.05.27-12.04.39:485][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7F5000 with pending SubmitJob call.
[2025.05.27-12.04.39:485][535]LogShaderCompilers: Display: Cancelled job 0x0000077E1CFCA000 with pending SubmitJob call.
[2025.05.27-12.04.39:486][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7D3200 with pending SubmitJob call.
[2025.05.27-12.04.39:487][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7F0A00 with pending SubmitJob call.
[2025.05.27-12.04.39:487][535]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7FDC00 with pending SubmitJob call.
[2025.05.27-12.04.39:510][535]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.27-12.04.39:518][535]LogSkeletalMesh: Section 0: Material=0, 576 triangles
[2025.05.27-12.04.39:520][535]LogSkeletalMesh: Building Skeletal Mesh cartilage_lod0_mesh...
[2025.05.27-12.04.39:526][535]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/cartilage_lod0_mesh.cartilage_lod0_mesh
[2025.05.27-12.04.39:527][535]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.05.27-12.04.39:536][535]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.04.39:536][535]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.04.39:536][535]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.04.39:536][535]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.05.27-12.04.39:550][535]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.04.39:550][535]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.04.39:550][535]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.04.39:634][535]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.05.27-12.04.39:662][535]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.05.27-12.04.39:679][535]LogUObjectHash: Compacting FUObjectHashTables data took   0.95ms
[2025.05.27-12.04.39:693][535]LogUObjectHash: Compacting FUObjectHashTables data took   0.38ms
[2025.05.27-12.04.39:723][535]LogSkeletalMesh: Building Skeletal Mesh cartilage_lod0_mesh...
[2025.05.27-12.04.39:738][535]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/cartilage_lod0_mesh.cartilage_lod0_mesh
[2025.05.27-12.04.39:741][535]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.27-12.04.39:741][535]FBXImport: Warning: The bone size is too small to create Physics Asset 'cartilage_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'cartilage_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.27-12.04.39:884][536]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.27-12.04.39:938][537]LogStreaming: Display: FlushAsyncLoading(522): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-12.04.39:938][537]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/custom_head_lod0_mesh_PhysicsAsset (0x8026C549C06B9669) - The package to load does not exist on disk or in the loader
[2025.05.27-12.04.39:938][537]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/custom_head_lod0_mesh_PhysicsAsset'
[2025.05.27-12.04.40:260][537]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/custom_head_lod0_mesh.fbx)
[2025.05.27-12.04.40:373][537]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/custom_head_lod0_mesh.fbx
[2025.05.27-12.04.43:604][537]LogFbx: FBX Scene Loaded Succesfully
[2025.05.27-12.04.43:652][537]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_head_shader.MH_Friend_head_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.27-12.04.43:706][537]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.27-12.04.43:815][537]LogFbx: Triangulating skeletal mesh custom_head_lod0_mesh
[2025.05.27-12.04.47:395][537]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.27-12.04.47:640][537]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/custom_head_lod0_mesh.custom_head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.27-12.04.48:136][537]LogSkeletalMesh: Section 0: Material=0, 48004 triangles
[2025.05.27-12.04.48:201][537]LogSkeletalMesh: Building Skeletal Mesh custom_head_lod0_mesh...
[2025.05.27-12.04.48:238][537]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (custom_head_lod0_mesh) ...
[2025.05.27-12.04.48:271][537]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/custom_head_lod0_mesh.custom_head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.27-12.04.48:778][537]LogSkeletalMesh: Built Skeletal Mesh [0.58s] /Game/untitled_category/untitled_asset/custom_head_lod0_mesh.custom_head_lod0_mesh
[2025.05.27-12.04.48:786][537]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.05.27-12.04.48:796][537]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_2:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.04.48:796][537]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.04.48:796][537]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.04.48:798][537]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.05.27-12.04.48:808][537]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_3:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.04.48:808][537]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.04.48:808][537]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.04.48:823][537]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.05.27-12.04.48:852][537]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.05.27-12.04.48:866][537]LogUObjectHash: Compacting FUObjectHashTables data took   0.75ms
[2025.05.27-12.04.48:881][537]LogUObjectHash: Compacting FUObjectHashTables data took   0.35ms
[2025.05.27-12.04.52:707][537]LogSkeletalMesh: Building Skeletal Mesh custom_head_lod0_mesh...
[2025.05.27-12.04.52:722][537]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (custom_head_lod0_mesh) ...
[2025.05.27-12.04.53:127][537]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/custom_head_lod0_mesh.custom_head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.27-12.04.56:768][537]LogSkeletalMesh: Built Skeletal Mesh [4.06s] /Game/untitled_category/untitled_asset/custom_head_lod0_mesh.custom_head_lod0_mesh
[2025.05.27-12.04.56:911][537]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.27-12.04.56:911][537]FBXImport: Warning: The bone size is too small to create Physics Asset 'custom_head_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'custom_head_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.27-12.04.57:098][538]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.27-12.04.57:109][538]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.04.57:126][540]LogStreaming: Display: FlushAsyncLoading(530): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-12.04.57:126][540]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_PhysicsAsset (0x66E38450F451F47D) - The package to load does not exist on disk or in the loader
[2025.05.27-12.04.57:126][540]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_PhysicsAsset'
[2025.05.27-12.04.57:608][540]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeEdge_lod0_mesh.fbx)
[2025.05.27-12.04.57:822][540]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeEdge_lod0_mesh.fbx
[2025.05.27-12.04.57:825][540]LogFbx: FBX Scene Loaded Succesfully
[2025.05.27-12.04.57:890][540]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader.MH_Friend_eyeEdge_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.27-12.04.57:900][540]LogShaderCompilers: Display: Cancelled job 0x0000077EC956DC00 with pending SubmitJob call.
[2025.05.27-12.04.57:901][540]LogShaderCompilers: Display: Cancelled job 0x0000077EC9568C00 with pending SubmitJob call.
[2025.05.27-12.04.57:902][540]LogShaderCompilers: Display: Cancelled job 0x0000077EC9568200 with pending SubmitJob call.
[2025.05.27-12.04.57:902][540]LogShaderCompilers: Display: Cancelled job 0x0000077EC9566400 with pending SubmitJob call.
[2025.05.27-12.04.57:903][540]LogShaderCompilers: Display: Cancelled job 0x0000077EC9565A00 with pending SubmitJob call.
[2025.05.27-12.04.57:903][540]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7F0000 with pending SubmitJob call.
[2025.05.27-12.04.57:903][540]LogShaderCompilers: Display: Cancelled job 0x0000077EC9560000 with pending SubmitJob call.
[2025.05.27-12.04.57:903][540]LogShaderCompilers: Display: Cancelled job 0x0000077E94520000 with pending SubmitJob call.
[2025.05.27-12.04.57:904][540]LogShaderCompilers: Display: Cancelled job 0x0000077EC956AA00 with pending SubmitJob call.
[2025.05.27-12.04.57:905][540]LogShaderCompilers: Display: Cancelled job 0x0000077EC956B400 with pending SubmitJob call.
[2025.05.27-12.04.57:905][540]LogShaderCompilers: Display: Cancelled job 0x0000077EC9562800 with pending SubmitJob call.
[2025.05.27-12.04.57:905][540]LogShaderCompilers: Display: Cancelled job 0x0000077E94523200 with pending SubmitJob call.
[2025.05.27-12.04.57:905][540]LogShaderCompilers: Display: Cancelled job 0x0000077EC9561E00 with pending SubmitJob call.
[2025.05.27-12.04.57:905][540]LogShaderCompilers: Display: Cancelled job 0x0000077E94521400 with pending SubmitJob call.
[2025.05.27-12.04.57:905][540]LogShaderCompilers: Display: Cancelled job 0x0000077E94520A00 with pending SubmitJob call.
[2025.05.27-12.04.57:906][540]LogFbx: Triangulating skeletal mesh eyeEdge_lod0_mesh
[2025.05.27-12.04.57:906][540]LogShaderCompilers: Display: Cancelled job 0x0000077EC9564600 with pending SubmitJob call.
[2025.05.27-12.04.57:906][540]LogShaderCompilers: Display: Cancelled job 0x0000077EC956BE00 with pending SubmitJob call.
[2025.05.27-12.04.57:906][540]LogShaderCompilers: Display: Cancelled job 0x0000077E2D187800 with pending SubmitJob call.
[2025.05.27-12.04.57:907][540]LogShaderCompilers: Display: Cancelled job 0x0000077E871EAA00 with pending SubmitJob call.
[2025.05.27-12.04.57:908][540]LogShaderCompilers: Display: Cancelled job 0x0000077EC956F000 with pending SubmitJob call.
[2025.05.27-12.04.57:915][540]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.27-12.04.57:920][540]LogSkeletalMesh: Section 0: Material=0, 386 triangles
[2025.05.27-12.04.57:921][540]LogSkeletalMesh: Building Skeletal Mesh eyeEdge_lod0_mesh...
[2025.05.27-12.04.57:925][540]LogSkeletalMesh: Built Skeletal Mesh [0.00s] /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh.eyeEdge_lod0_mesh
[2025.05.27-12.04.57:926][540]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.05.27-12.04.57:934][540]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_4:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.04.57:934][540]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.04.57:934][540]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.04.57:934][540]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.05.27-12.04.57:940][540]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_5:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.04.57:940][540]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.04.57:940][540]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.04.57:957][540]LogUObjectHash: Compacting FUObjectHashTables data took   0.84ms
[2025.05.27-12.04.58:038][540]LogUObjectHash: Compacting FUObjectHashTables data took   0.66ms
[2025.05.27-12.04.58:053][540]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.27-12.04.58:068][540]LogUObjectHash: Compacting FUObjectHashTables data took   0.36ms
[2025.05.27-12.04.58:070][540]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.27-12.04.58:070][540]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeEdge_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeEdge_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.27-12.04.58:227][541]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.27-12.04.58:318][544]LogStreaming: Display: FlushAsyncLoading(536): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-12.04.58:318][544]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_PhysicsAsset (0x3503C37BEAAD4328) - The package to load does not exist on disk or in the loader
[2025.05.27-12.04.58:318][544]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_PhysicsAsset'
[2025.05.27-12.04.58:653][544]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeLeft_lod0_mesh.fbx)
[2025.05.27-12.04.58:746][544]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeLeft_lod0_mesh.fbx
[2025.05.27-12.04.58:750][544]LogFbx: FBX Scene Loaded Succesfully
[2025.05.27-12.04.58:789][544]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader.MH_Friend_eyeLeft_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.27-12.04.58:842][544]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.27-12.04.58:874][544]LogTexture: Display: Building textures: /Game/untitled_category/untitled_asset/eyes_normal_map.eyes_normal_map (TFO_BC5, 4096x4096 x1x1x1) (Required Memory Estimate: 1234.499988 MB), EncodeSpeed: Fast
[2025.05.27-12.04.58:999][544]LogFbx: Triangulating skeletal mesh eyeLeft_lod0_mesh
[2025.05.27-12.04.59:046][544]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.27-12.04.59:072][544]LogSkeletalMesh: Section 0: Material=0, 1536 triangles
[2025.05.27-12.04.59:073][544]LogSkeletalMesh: Building Skeletal Mesh eyeLeft_lod0_mesh...
[2025.05.27-12.04.59:090][544]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeLeft_lod0_mesh) ...
[2025.05.27-12.04.59:096][544]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh.eyeLeft_lod0_mesh
[2025.05.27-12.04.59:097][544]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.05.27-12.04.59:103][544]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.04.59:103][544]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.04.59:104][544]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.04.59:104][544]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.05.27-12.04.59:110][544]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_7:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.04.59:110][544]LogWorld: UWorld::CleanupWorld for World_7, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.04.59:111][544]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.04.59:127][544]LogUObjectHash: Compacting FUObjectHashTables data took   0.80ms
[2025.05.27-12.04.59:155][544]LogUObjectHash: Compacting FUObjectHashTables data took   0.80ms
[2025.05.27-12.04.59:170][544]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.05.27-12.04.59:184][544]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.27-12.04.59:191][544]LogSkeletalMesh: Building Skeletal Mesh eyeLeft_lod0_mesh...
[2025.05.27-12.04.59:207][544]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeLeft_lod0_mesh) ...
[2025.05.27-12.04.59:223][544]LogSkeletalMesh: Built Skeletal Mesh [0.03s] /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh.eyeLeft_lod0_mesh
[2025.05.27-12.04.59:224][544]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.27-12.04.59:224][544]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeLeft_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeLeft_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.27-12.04.59:345][545]LogUObjectHash: Compacting FUObjectHashTables data took   0.77ms
[2025.05.27-12.04.59:435][548]LogStreaming: Display: FlushAsyncLoading(544): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-12.04.59:435][548]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_PhysicsAsset (0xB68C5B7FAED625F8) - The package to load does not exist on disk or in the loader
[2025.05.27-12.04.59:435][548]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_PhysicsAsset'
[2025.05.27-12.04.59:778][548]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeRight_lod0_mesh.fbx)
[2025.05.27-12.04.59:870][548]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeRight_lod0_mesh.fbx
[2025.05.27-12.04.59:875][548]LogFbx: FBX Scene Loaded Succesfully
[2025.05.27-12.04.59:916][548]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader.MH_Friend_eyeRight_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.27-12.04.59:935][548]LogFbxMaterialImport: Warning: Manual texture reimport and recompression may be needed for eyes_normal_map
[2025.05.27-12.04.59:970][548]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.27-12.05.00:002][548]LogTexture: Display: Building textures: /Game/untitled_category/untitled_asset/eyes_normal_map.eyes_normal_map (TFO_BC5, 4096x4096 x1x1x1) (Required Memory Estimate: 1234.499988 MB), EncodeSpeed: Fast
[2025.05.27-12.05.00:119][548]LogFbx: Triangulating skeletal mesh eyeRight_lod0_mesh
[2025.05.27-12.05.00:160][548]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.27-12.05.00:186][548]LogSkeletalMesh: Section 0: Material=0, 1536 triangles
[2025.05.27-12.05.00:187][548]LogSkeletalMesh: Building Skeletal Mesh eyeRight_lod0_mesh...
[2025.05.27-12.05.00:204][548]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeRight_lod0_mesh) ...
[2025.05.27-12.05.00:209][548]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh.eyeRight_lod0_mesh
[2025.05.27-12.05.00:210][548]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.05.27-12.05.00:217][548]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_8:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.05.00:217][548]LogWorld: UWorld::CleanupWorld for World_8, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.05.00:217][548]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.05.00:218][548]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.05.27-12.05.00:225][548]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_9:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.05.00:225][548]LogWorld: UWorld::CleanupWorld for World_9, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.05.00:225][548]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.05.00:242][548]LogUObjectHash: Compacting FUObjectHashTables data took   1.01ms
[2025.05.27-12.05.00:272][548]LogUObjectHash: Compacting FUObjectHashTables data took   0.66ms
[2025.05.27-12.05.00:286][548]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.05.27-12.05.00:301][548]LogUObjectHash: Compacting FUObjectHashTables data took   0.36ms
[2025.05.27-12.05.00:308][548]LogSkeletalMesh: Building Skeletal Mesh eyeRight_lod0_mesh...
[2025.05.27-12.05.00:324][548]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeRight_lod0_mesh) ...
[2025.05.27-12.05.00:339][548]LogSkeletalMesh: Built Skeletal Mesh [0.03s] /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh.eyeRight_lod0_mesh
[2025.05.27-12.05.00:340][548]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.27-12.05.00:340][548]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeRight_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeRight_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.27-12.05.00:461][549]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.27-12.05.00:559][552]LogStreaming: Display: FlushAsyncLoading(550): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-12.05.00:559][552]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_PhysicsAsset (0xC8AA4E7A36074A88) - The package to load does not exist on disk or in the loader
[2025.05.27-12.05.00:559][552]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_PhysicsAsset'
[2025.05.27-12.05.00:898][552]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyelashes_lod0_mesh.fbx)
[2025.05.27-12.05.00:989][552]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyelashes_lod0_mesh.fbx
[2025.05.27-12.05.00:992][552]LogFbx: FBX Scene Loaded Succesfully
[2025.05.27-12.05.01:032][552]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader.MH_Friend_eyelashes_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.27-12.05.01:047][552]LogShaderCompilers: Display: Cancelled job 0x0000077E8A41F000 with pending SubmitJob call.
[2025.05.27-12.05.01:047][552]LogShaderCompilers: Display: Cancelled job 0x0000077E2E7BF000 with pending SubmitJob call.
[2025.05.27-12.05.01:047][552]LogShaderCompilers: Display: Cancelled job 0x0000077E2E7BBE00 with pending SubmitJob call.
[2025.05.27-12.05.01:048][552]LogShaderCompilers: Display: Cancelled job 0x0000077E2E7B6400 with pending SubmitJob call.
[2025.05.27-12.05.01:048][552]LogShaderCompilers: Display: Cancelled job 0x0000077E94521E00 with pending SubmitJob call.
[2025.05.27-12.05.01:049][552]LogShaderCompilers: Display: Cancelled job 0x0000077E2E7B7800 with pending SubmitJob call.
[2025.05.27-12.05.01:049][552]LogShaderCompilers: Display: Cancelled job 0x0000077E2E7BC800 with pending SubmitJob call.
[2025.05.27-12.05.01:049][552]LogShaderCompilers: Display: Cancelled job 0x0000077E2D182800 with pending SubmitJob call.
[2025.05.27-12.05.01:049][552]LogShaderCompilers: Display: Cancelled job 0x0000077ECC7D3C00 with pending SubmitJob call.
[2025.05.27-12.05.01:049][552]LogShaderCompilers: Display: Cancelled job 0x0000077E2D183200 with pending SubmitJob call.
[2025.05.27-12.05.01:049][552]LogShaderCompilers: Display: Cancelled job 0x0000077E94521400 with pending SubmitJob call.
[2025.05.27-12.05.01:049][552]LogShaderCompilers: Display: Cancelled job 0x0000077E2D180A00 with pending SubmitJob call.
[2025.05.27-12.05.01:049][552]LogShaderCompilers: Display: Cancelled job 0x0000077E887CE600 with pending SubmitJob call.
[2025.05.27-12.05.01:051][552]LogShaderCompilers: Display: Cancelled job 0x0000077E483AD200 with pending SubmitJob call.
[2025.05.27-12.05.01:051][552]LogShaderCompilers: Display: Cancelled job 0x0000077E2E7B5000 with pending SubmitJob call.
[2025.05.27-12.05.01:052][552]LogShaderCompilers: Display: Cancelled job 0x0000077E2E7B8C00 with pending SubmitJob call.
[2025.05.27-12.05.01:052][552]LogFbx: Triangulating skeletal mesh eyelashes_lod0_mesh
[2025.05.27-12.05.01:052][552]LogShaderCompilers: Display: Cancelled job 0x0000077E2E7B1E00 with pending SubmitJob call.
[2025.05.27-12.05.01:053][552]LogShaderCompilers: Display: Cancelled job 0x0000077E2E7BD200 with pending SubmitJob call.
[2025.05.27-12.05.01:053][552]LogShaderCompilers: Display: Cancelled job 0x0000077E2E7B3200 with pending SubmitJob call.
[2025.05.27-12.05.01:053][552]LogShaderCompilers: Display: Cancelled job 0x0000077E871E7800 with pending SubmitJob call.
[2025.05.27-12.05.01:079][552]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.27-12.05.01:094][552]LogSkeletalMesh: Section 0: Material=0, 1722 triangles
[2025.05.27-12.05.01:095][552]LogSkeletalMesh: Building Skeletal Mesh eyelashes_lod0_mesh...
[2025.05.27-12.05.01:106][552]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh.eyelashes_lod0_mesh
[2025.05.27-12.05.01:108][552]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.05.27-12.05.01:122][552]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.05.01:122][552]LogWorld: UWorld::CleanupWorld for World_10, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.05.01:122][552]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.05.01:123][552]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.05.27-12.05.01:132][552]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_11:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.05.01:132][552]LogWorld: UWorld::CleanupWorld for World_11, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.05.01:132][552]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.05.01:147][552]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.27-12.05.01:176][552]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.05.27-12.05.01:191][552]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.05.27-12.05.01:205][552]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.27-12.05.01:209][552]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.27-12.05.01:209][552]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyelashes_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyelashes_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.27-12.05.01:317][553]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.05.27-12.05.01:443][554]LogStreaming: Display: FlushAsyncLoading(556): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-12.05.01:443][554]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_PhysicsAsset (0xA8E5BC927DC4F667) - The package to load does not exist on disk or in the loader
[2025.05.27-12.05.01:443][554]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_PhysicsAsset'
[2025.05.27-12.05.01:759][554]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeshell_lod0_mesh.fbx)
[2025.05.27-12.05.01:981][554]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeshell_lod0_mesh.fbx
[2025.05.27-12.05.01:984][554]LogFbx: FBX Scene Loaded Succesfully
[2025.05.27-12.05.02:046][554]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader.MH_Friend_eyeshell_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.27-12.05.02:058][554]LogShaderCompilers: Display: Cancelled job 0x0000077E9638DC00 with pending SubmitJob call.
[2025.05.27-12.05.02:058][554]LogShaderCompilers: Display: Cancelled job 0x0000077E96388C00 with pending SubmitJob call.
[2025.05.27-12.05.02:058][554]LogShaderCompilers: Display: Cancelled job 0x0000077E96091400 with pending SubmitJob call.
[2025.05.27-12.05.02:059][554]LogShaderCompilers: Display: Cancelled job 0x0000077E9638BE00 with pending SubmitJob call.
[2025.05.27-12.05.02:059][554]LogShaderCompilers: Display: Cancelled job 0x0000077E9638B400 with pending SubmitJob call.
[2025.05.27-12.05.02:059][554]LogShaderCompilers: Display: Cancelled job 0x0000077E96389600 with pending SubmitJob call.
[2025.05.27-12.05.02:061][554]LogShaderCompilers: Display: Cancelled job 0x0000077E96093200 with pending SubmitJob call.
[2025.05.27-12.05.02:061][554]LogShaderCompilers: Display: Cancelled job 0x0000077E96092800 with pending SubmitJob call.
[2025.05.27-12.05.02:061][554]LogShaderCompilers: Display: Cancelled job 0x0000077E96383200 with pending SubmitJob call.
[2025.05.27-12.05.02:061][554]LogShaderCompilers: Display: Cancelled job 0x0000077E96382800 with pending SubmitJob call.
[2025.05.27-12.05.02:062][554]LogShaderCompilers: Display: Cancelled job 0x0000077E9638E600 with pending SubmitJob call.
[2025.05.27-12.05.02:062][554]LogShaderCompilers: Display: Cancelled job 0x0000077E96094600 with pending SubmitJob call.
[2025.05.27-12.05.02:062][554]LogShaderCompilers: Display: Cancelled job 0x0000077E96096400 with pending SubmitJob call.
[2025.05.27-12.05.02:062][554]LogShaderCompilers: Display: Cancelled job 0x0000077E96385000 with pending SubmitJob call.
[2025.05.27-12.05.02:062][554]LogShaderCompilers: Display: Cancelled job 0x0000077E96385A00 with pending SubmitJob call.
[2025.05.27-12.05.02:063][554]LogShaderCompilers: Display: Cancelled job 0x0000077E96387800 with pending SubmitJob call.
[2025.05.27-12.05.02:063][554]LogShaderCompilers: Display: Cancelled job 0x0000077EC93A2800 with pending SubmitJob call.
[2025.05.27-12.05.02:063][554]LogShaderCompilers: Display: Cancelled job 0x0000077E96093C00 with pending SubmitJob call.
[2025.05.27-12.05.02:064][554]LogShaderCompilers: Display: Cancelled job 0x0000077E96381400 with pending SubmitJob call.
[2025.05.27-12.05.02:064][554]LogShaderCompilers: Display: Cancelled job 0x0000077E9638F000 with pending SubmitJob call.
[2025.05.27-12.05.02:064][554]LogFbx: Triangulating skeletal mesh eyeshell_lod0_mesh
[2025.05.27-12.05.02:082][554]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.27-12.05.02:095][554]LogSkeletalMesh: Section 0: Material=0, 980 triangles
[2025.05.27-12.05.02:096][554]LogSkeletalMesh: Building Skeletal Mesh eyeshell_lod0_mesh...
[2025.05.27-12.05.02:107][554]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh.eyeshell_lod0_mesh
[2025.05.27-12.05.02:107][554]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
[2025.05.27-12.05.02:117][554]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_12:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.05.02:117][554]LogWorld: UWorld::CleanupWorld for World_12, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.05.02:117][554]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.05.02:118][554]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
[2025.05.27-12.05.02:132][554]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.05.02:132][554]LogWorld: UWorld::CleanupWorld for World_13, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.05.02:132][554]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.05.02:148][554]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.05.27-12.05.02:200][554]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.27-12.05.02:214][554]LogUObjectHash: Compacting FUObjectHashTables data took   0.66ms
[2025.05.27-12.05.02:228][554]LogUObjectHash: Compacting FUObjectHashTables data took   0.36ms
[2025.05.27-12.05.02:231][554]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.27-12.05.02:231][554]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeshell_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeshell_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.27-12.05.02:398][556]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.27-12.05.02:493][558]LogStreaming: Display: FlushAsyncLoading(562): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-12.05.02:493][558]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/head_lod0_mesh_PhysicsAsset (0xE7DA988AE0F17ACA) - The package to load does not exist on disk or in the loader
[2025.05.27-12.05.02:494][558]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/head_lod0_mesh_PhysicsAsset'
[2025.05.27-12.05.03:014][558]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/head_lod0_mesh.fbx)
[2025.05.27-12.05.03:220][558]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/head_lod0_mesh.fbx
[2025.05.27-12.05.06:897][558]LogFbx: FBX Scene Loaded Succesfully
[2025.05.27-12.05.06:970][558]LogFbx: Triangulating skeletal mesh head_lod0_mesh
[2025.05.27-12.05.11:489][558]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.27-12.05.11:712][558]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.27-12.05.12:212][558]LogSkeletalMesh: Section 0: Material=0, 48004 triangles
[2025.05.27-12.05.12:278][558]LogSkeletalMesh: Building Skeletal Mesh head_lod0_mesh...
[2025.05.27-12.05.12:295][558]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.27-12.05.12:351][558]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.27-12.05.12:854][558]LogSkeletalMesh: Built Skeletal Mesh [0.58s] /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.27-12.05.12:861][558]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
[2025.05.27-12.05.12:871][558]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_14:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.05.12:871][558]LogWorld: UWorld::CleanupWorld for World_14, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.05.12:871][558]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.05.12:873][558]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_15
[2025.05.27-12.05.12:882][558]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_15:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.05.12:882][558]LogWorld: UWorld::CleanupWorld for World_15, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.05.12:882][558]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.05.12:897][558]LogUObjectHash: Compacting FUObjectHashTables data took   0.87ms
[2025.05.27-12.05.12:926][558]LogUObjectHash: Compacting FUObjectHashTables data took   0.72ms
[2025.05.27-12.05.12:941][558]LogUObjectHash: Compacting FUObjectHashTables data took   0.66ms
[2025.05.27-12.05.12:957][558]LogUObjectHash: Compacting FUObjectHashTables data took   0.38ms
[2025.05.27-12.05.16:442][558]LogSkeletalMesh: Building Skeletal Mesh head_lod0_mesh...
[2025.05.27-12.05.16:457][558]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.27-12.05.16:712][558]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.27-12.05.20:188][558]LogSkeletalMesh: Built Skeletal Mesh [3.75s] /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.27-12.05.20:344][558]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.27-12.05.20:345][558]FBXImport: Warning: The bone size is too small to create Physics Asset 'head_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'head_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.27-12.05.20:604][559]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.05.27-12.05.20:614][559]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.05.20:630][561]LogStreaming: Display: FlushAsyncLoading(568): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-12.05.20:630][561]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/saliva_lod0_mesh_PhysicsAsset (0xCEBEF7E7EA79F106) - The package to load does not exist on disk or in the loader
[2025.05.27-12.05.20:630][561]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/saliva_lod0_mesh_PhysicsAsset'
[2025.05.27-12.05.21:069][561]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/saliva_lod0_mesh.fbx)
[2025.05.27-12.05.21:276][561]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/saliva_lod0_mesh.fbx
[2025.05.27-12.05.21:279][561]LogFbx: FBX Scene Loaded Succesfully
[2025.05.27-12.05.21:342][561]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_saliva_shader.MH_Friend_saliva_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.27-12.05.21:353][561]LogShaderCompilers: Display: Cancelled job 0x0000077E434D3200 with pending SubmitJob call.
[2025.05.27-12.05.21:354][561]LogShaderCompilers: Display: Cancelled job 0x0000077E434DAA00 with pending SubmitJob call.
[2025.05.27-12.05.21:354][561]LogShaderCompilers: Display: Cancelled job 0x0000077E434D7800 with pending SubmitJob call.
[2025.05.27-12.05.21:355][561]LogShaderCompilers: Display: Cancelled job 0x0000077E434D5000 with pending SubmitJob call.
[2025.05.27-12.05.21:357][561]LogShaderCompilers: Display: Cancelled job 0x0000077E434D2800 with pending SubmitJob call.
[2025.05.27-12.05.21:357][561]LogShaderCompilers: Display: Cancelled job 0x0000077ECE71D200 with pending SubmitJob call.
[2025.05.27-12.05.21:357][561]LogShaderCompilers: Display: Cancelled job 0x0000077E434D5A00 with pending SubmitJob call.
[2025.05.27-12.05.21:357][561]LogShaderCompilers: Display: Cancelled job 0x0000077E92C68200 with pending SubmitJob call.
[2025.05.27-12.05.21:358][561]LogShaderCompilers: Display: Cancelled job 0x0000077E2D180A00 with pending SubmitJob call.
[2025.05.27-12.05.21:358][561]LogShaderCompilers: Display: Cancelled job 0x0000077E434DC800 with pending SubmitJob call.
[2025.05.27-12.05.21:358][561]LogShaderCompilers: Display: Cancelled job 0x0000077E434DDC00 with pending SubmitJob call.
[2025.05.27-12.05.21:360][561]LogShaderCompilers: Display: Cancelled job 0x0000077E434DF000 with pending SubmitJob call.
[2025.05.27-12.05.21:360][561]LogShaderCompilers: Display: Cancelled job 0x0000077E434DD200 with pending SubmitJob call.
[2025.05.27-12.05.21:360][561]LogShaderCompilers: Display: Cancelled job 0x0000077E4217DC00 with pending SubmitJob call.
[2025.05.27-12.05.21:360][561]LogShaderCompilers: Display: Cancelled job 0x0000077E434D8200 with pending SubmitJob call.
[2025.05.27-12.05.21:361][561]LogShaderCompilers: Display: Cancelled job 0x0000077F3A81F000 with pending SubmitJob call.
[2025.05.27-12.05.21:361][561]LogShaderCompilers: Display: Cancelled job 0x0000077F3A81E600 with pending SubmitJob call.
[2025.05.27-12.05.21:361][561]LogFbx: Triangulating skeletal mesh saliva_lod0_mesh
[2025.05.27-12.05.21:361][561]LogShaderCompilers: Display: Cancelled job 0x0000077E434DBE00 with pending SubmitJob call.
[2025.05.27-12.05.21:361][561]LogShaderCompilers: Display: Cancelled job 0x0000077E434D1400 with pending SubmitJob call.
[2025.05.27-12.05.21:361][561]LogShaderCompilers: Display: Cancelled job 0x0000077E434D8C00 with pending SubmitJob call.
[2025.05.27-12.05.21:377][561]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.27-12.05.21:389][561]LogSkeletalMesh: Section 0: Material=0, 1004 triangles
[2025.05.27-12.05.21:390][561]LogSkeletalMesh: Building Skeletal Mesh saliva_lod0_mesh...
[2025.05.27-12.05.21:400][561]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/saliva_lod0_mesh.saliva_lod0_mesh
[2025.05.27-12.05.21:402][561]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_16
[2025.05.27-12.05.21:410][561]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_16:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.05.21:436][561]LogWorld: UWorld::CleanupWorld for World_16, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.05.21:436][561]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.05.21:437][561]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_17
[2025.05.27-12.05.21:451][561]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_17:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.05.21:451][561]LogWorld: UWorld::CleanupWorld for World_17, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.05.21:451][561]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.05.21:467][561]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.05.27-12.05.21:498][561]LogUObjectHash: Compacting FUObjectHashTables data took   0.76ms
[2025.05.27-12.05.21:514][561]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.05.27-12.05.21:530][561]LogUObjectHash: Compacting FUObjectHashTables data took   0.44ms
[2025.05.27-12.05.21:533][561]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.27-12.05.21:533][561]FBXImport: Warning: The bone size is too small to create Physics Asset 'saliva_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'saliva_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.27-12.05.21:689][562]LogUObjectHash: Compacting FUObjectHashTables data took   0.72ms
[2025.05.27-12.05.21:804][565]LogStreaming: Display: FlushAsyncLoading(574): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-12.05.21:804][565]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/teeth_lod0_mesh_PhysicsAsset (0x466EF1832CA8EE25) - The package to load does not exist on disk or in the loader
[2025.05.27-12.05.21:804][565]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/teeth_lod0_mesh_PhysicsAsset'
[2025.05.27-12.05.22:250][565]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/teeth_lod0_mesh.fbx)
[2025.05.27-12.05.22:461][565]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/teeth_lod0_mesh.fbx
[2025.05.27-12.05.22:505][565]LogFbx: FBX Scene Loaded Succesfully
[2025.05.27-12.05.22:539][565]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_teeth_shader.MH_Friend_teeth_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.27-12.05.22:595][565]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.27-12.05.22:627][565]LogTexture: Display: Building textures: /Game/untitled_category/untitled_asset/teeth_normal_map.teeth_normal_map (TFO_BC5, 4096x4096 x1x1x1) (Required Memory Estimate: 1234.499988 MB), EncodeSpeed: Fast
[2025.05.27-12.05.22:751][565]LogFbx: Triangulating skeletal mesh teeth_lod0_mesh
[2025.05.27-12.05.22:866][565]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.27-12.05.22:991][565]LogSkeletalMesh: Section 0: Material=0, 8350 triangles
[2025.05.27-12.05.22:993][565]LogSkeletalMesh: Building Skeletal Mesh teeth_lod0_mesh...
[2025.05.27-12.05.23:009][565]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (teeth_lod0_mesh) ...
[2025.05.27-12.05.23:099][565]LogSkeletalMesh: Built Skeletal Mesh [0.11s] /Game/untitled_category/untitled_asset/teeth_lod0_mesh.teeth_lod0_mesh
[2025.05.27-12.05.23:101][565]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_18
[2025.05.27-12.05.23:109][565]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_18:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.05.23:109][565]LogWorld: UWorld::CleanupWorld for World_18, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.05.23:109][565]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.05.23:109][565]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_19
[2025.05.27-12.05.23:122][565]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_19:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.27-12.05.23:122][565]LogWorld: UWorld::CleanupWorld for World_19, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.05.23:122][565]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.05.23:138][565]LogUObjectHash: Compacting FUObjectHashTables data took   0.76ms
[2025.05.27-12.05.23:166][565]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.05.27-12.05.23:181][565]LogUObjectHash: Compacting FUObjectHashTables data took   0.75ms
[2025.05.27-12.05.23:194][565]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.27-12.05.23:242][565]LogSkeletalMesh: Building Skeletal Mesh teeth_lod0_mesh...
[2025.05.27-12.05.23:258][565]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (teeth_lod0_mesh) ...
[2025.05.27-12.05.23:421][565]LogSkeletalMesh: Built Skeletal Mesh [0.18s] /Game/untitled_category/untitled_asset/teeth_lod0_mesh.teeth_lod0_mesh
[2025.05.27-12.05.23:423][565]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.27-12.05.23:423][565]FBXImport: Warning: The bone size is too small to create Physics Asset 'teeth_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'teeth_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.27-12.05.23:592][566]LogUObjectHash: Compacting FUObjectHashTables data took   0.94ms
[2025.05.27-12.05.30:717][639]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.05.32:742][657]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.27-12.05.32:744][657]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/MetaHumans/Test/TestLevel' took 0.016
[2025.05.27-12.05.32:746][657]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.27-12.05.32:746][657]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_20
[2025.05.27-12.05.32:833][657]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Skeleton.cartilage_lod0_mesh_Skeleton]
[2025.05.27-12.05.32:833][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Skeleton]
[2025.05.27-12.05.32:835][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Skeleton_Auto1
[2025.05.27-12.05.32:835][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/cartilage_lod0_mesh_Skeleton_AutB6493D0C4429D542DB762AABA4409BE5.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.27-12.05.32:835][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/cartilage_lod0_mesh] ([1] browsable assets)...
[2025.05.27-12.05.32:835][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/cartilage_lod0_mesh]
[2025.05.27-12.05.32:841][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Auto1
[2025.05.27-12.05.32:841][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/cartilage_lod0_mesh_Auto1E4B9A7944E4ECE0D02847EA21A24864A.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Auto1.uasset'
[2025.05.27-12.05.32:842][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader] ([1] browsable assets)...
[2025.05.27-12.05.32:842][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader]
[2025.05.27-12.05.32:842][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader_Auto1
[2025.05.27-12.05.32:842][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_cartilage_shader_Auto1A8B76D744031ED72DE88F7A53F89099F.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader_Auto1.uasset'
[2025.05.27-12.05.32:844][657]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/custom_head_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.27-12.05.32:844][657]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_21
[2025.05.27-12.05.32:857][657]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/custom_head_lod0_mesh_Skeleton.custom_head_lod0_mesh_Skeleton]
[2025.05.27-12.05.32:858][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/custom_head_lod0_mesh_Skeleton]
[2025.05.27-12.05.32:871][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/custom_head_lod0_mesh_Skeleton_Auto1
[2025.05.27-12.05.32:871][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/custom_head_lod0_mesh_Skeleton_ACBA24487407E353234ACF4BD015E5F0C.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/custom_head_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.27-12.05.32:872][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/custom_head_lod0_mesh] ([1] browsable assets)...
[2025.05.27-12.05.32:872][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/custom_head_lod0_mesh]
[2025.05.27-12.05.33:095][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/custom_head_lod0_mesh_Auto1
[2025.05.27-12.05.33:096][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/custom_head_lod0_mesh_Auto1C5EAB7A943A09D77443AA78197D92313.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/custom_head_lod0_mesh_Auto1.uasset'
[2025.05.27-12.05.33:096][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_head_shader] ([1] browsable assets)...
[2025.05.27-12.05.33:096][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_head_shader]
[2025.05.27-12.05.33:097][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_head_shader_Auto1
[2025.05.27-12.05.33:097][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_head_shader_Auto17B6D39564998E277282E409282A39C4A.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_head_shader_Auto1.uasset'
[2025.05.27-12.05.33:097][657]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/head_roughness_map] ([1] browsable assets)...
[2025.05.27-12.05.33:155][657]OBJ SavePackage:     Rendered thumbnail for [Texture2D /Game/untitled_category/untitled_asset/head_roughness_map.head_roughness_map]
[2025.05.27-12.05.33:155][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/head_roughness_map]
[2025.05.27-12.05.33:309][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/head_roughness_map_Auto1
[2025.05.27-12.05.33:309][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/head_roughness_map_Auto183417AFA473F56B703C51EB5A3F507AC.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/head_roughness_map_Auto1.uasset'
[2025.05.27-12.05.33:311][657]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.27-12.05.33:311][657]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_22
[2025.05.27-12.05.33:384][657]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Skeleton.eyeEdge_lod0_mesh_Skeleton]
[2025.05.27-12.05.33:384][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Skeleton]
[2025.05.27-12.05.33:385][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Skeleton_Auto1
[2025.05.27-12.05.33:385][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeEdge_lod0_mesh_Skeleton_Auto1CCCB68FC4C5663CAA5D63AA12AA4F2A5.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.27-12.05.33:385][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh] ([1] browsable assets)...
[2025.05.27-12.05.33:385][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh]
[2025.05.27-12.05.33:387][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Auto1
[2025.05.27-12.05.33:387][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeEdge_lod0_mesh_Auto139B98A8E43799FA81A6BDEB15727078F.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Auto1.uasset'
[2025.05.27-12.05.33:387][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader] ([1] browsable assets)...
[2025.05.27-12.05.33:387][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader]
[2025.05.27-12.05.33:388][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader_Auto1
[2025.05.27-12.05.33:388][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_eyeEdge_shader_Auto1191562FB48CCD4C66580B39771767892.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader_Auto1.uasset'
[2025.05.27-12.05.33:390][657]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.27-12.05.33:391][657]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_23
[2025.05.27-12.05.33:403][657]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Skeleton.eyeLeft_lod0_mesh_Skeleton]
[2025.05.27-12.05.33:403][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Skeleton]
[2025.05.27-12.05.33:406][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Skeleton_Auto1
[2025.05.27-12.05.33:406][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeLeft_lod0_mesh_Skeleton_Auto181E77D1B42E1A28B7E529DADCC20880F.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.27-12.05.33:406][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh] ([1] browsable assets)...
[2025.05.27-12.05.33:406][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh]
[2025.05.27-12.05.33:410][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Auto1
[2025.05.27-12.05.33:410][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeLeft_lod0_mesh_Auto1D74F44A647C657B27E17718D821E453D.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Auto1.uasset'
[2025.05.27-12.05.33:410][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader] ([1] browsable assets)...
[2025.05.27-12.05.33:410][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader]
[2025.05.27-12.05.33:411][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader_Auto1
[2025.05.27-12.05.33:411][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_eyeLeft_shader_Auto1EB997DB7400AE906D1503EA4055CB412.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader_Auto1.uasset'
[2025.05.27-12.05.33:412][657]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyes_normal_map] ([1] browsable assets)...
[2025.05.27-12.05.33:414][657]OBJ SavePackage:     Rendered thumbnail for [Texture2D /Game/untitled_category/untitled_asset/eyes_normal_map.eyes_normal_map]
[2025.05.27-12.05.33:414][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyes_normal_map]
[2025.05.27-12.05.33:596][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyes_normal_map_Auto1
[2025.05.27-12.05.33:596][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyes_normal_map_Auto13E7882B842EFACD71073E6BAA1BC4C3D.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyes_normal_map_Auto1.uasset'
[2025.05.27-12.05.33:598][657]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.27-12.05.33:599][657]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_24
[2025.05.27-12.05.33:671][657]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Skeleton.eyeRight_lod0_mesh_Skeleton]
[2025.05.27-12.05.33:671][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Skeleton]
[2025.05.27-12.05.33:672][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Skeleton_Auto1
[2025.05.27-12.05.33:672][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeRight_lod0_mesh_Skeleton_AutoA8D2E6D740DC9CE562E4E4B10385687C.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.27-12.05.33:673][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh] ([1] browsable assets)...
[2025.05.27-12.05.33:673][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh]
[2025.05.27-12.05.33:676][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Auto1
[2025.05.27-12.05.33:676][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeRight_lod0_mesh_Auto192E1750E4187334C364613A0981EF54F.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Auto1.uasset'
[2025.05.27-12.05.33:677][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader] ([1] browsable assets)...
[2025.05.27-12.05.33:677][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader]
[2025.05.27-12.05.33:678][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader_Auto1
[2025.05.27-12.05.33:678][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_eyeRight_shader_Auto100B33C7B425AE1D3206019A3A3807069.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader_Auto1.uasset'
[2025.05.27-12.05.33:680][657]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.27-12.05.33:680][657]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_25
[2025.05.27-12.05.33:695][657]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Skeleton.eyelashes_lod0_mesh_Skeleton]
[2025.05.27-12.05.33:696][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Skeleton]
[2025.05.27-12.05.33:697][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Skeleton_Auto1
[2025.05.27-12.05.33:697][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyelashes_lod0_mesh_Skeleton_Aut5F15E2164A537F5195B5E382925746CA.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.27-12.05.33:698][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh] ([1] browsable assets)...
[2025.05.27-12.05.33:698][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh]
[2025.05.27-12.05.33:702][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Auto1
[2025.05.27-12.05.33:702][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyelashes_lod0_mesh_Auto1C47174CF4E13D4DE835E9EABFC224261.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Auto1.uasset'
[2025.05.27-12.05.33:702][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader] ([1] browsable assets)...
[2025.05.27-12.05.33:702][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader]
[2025.05.27-12.05.33:704][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader_Auto1
[2025.05.27-12.05.33:704][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_eyelashes_shader_Auto1E9EA66A241D828EF0E5E9B8F8A422979.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader_Auto1.uasset'
[2025.05.27-12.05.33:707][657]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.27-12.05.33:707][657]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_26
[2025.05.27-12.05.33:721][657]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Skeleton.eyeshell_lod0_mesh_Skeleton]
[2025.05.27-12.05.33:721][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Skeleton]
[2025.05.27-12.05.33:723][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Skeleton_Auto1
[2025.05.27-12.05.33:723][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeshell_lod0_mesh_Skeleton_AutoD056F4D747707393F6C7D09203A81AF1.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.27-12.05.33:724][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh] ([1] browsable assets)...
[2025.05.27-12.05.33:724][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh]
[2025.05.27-12.05.33:726][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Auto1
[2025.05.27-12.05.33:726][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeshell_lod0_mesh_Auto1202626824FE3CDF42FB5ACBB0B47D41B.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Auto1.uasset'
[2025.05.27-12.05.33:727][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader] ([1] browsable assets)...
[2025.05.27-12.05.33:727][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader]
[2025.05.27-12.05.33:727][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader_Auto1
[2025.05.27-12.05.33:727][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_eyeshell_shader_Auto1599B85C14DC6F873942A29B606378150.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader_Auto1.uasset'
[2025.05.27-12.05.33:729][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/head_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.27-12.05.33:729][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/head_lod0_mesh_Skeleton]
[2025.05.27-12.05.33:740][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/head_lod0_mesh_Skeleton_Auto1
[2025.05.27-12.05.33:740][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/head_lod0_mesh_Skeleton_Auto17AE935AC4D9560788B498F92B37FA165.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/head_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.27-12.05.33:740][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/head_lod0_mesh] ([1] browsable assets)...
[2025.05.27-12.05.33:741][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/head_lod0_mesh]
[2025.05.27-12.05.33:971][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/head_lod0_mesh_Auto1
[2025.05.27-12.05.33:971][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/head_lod0_mesh_Auto1BED6144F426BF4976093E68D6C5D9411.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/head_lod0_mesh_Auto1.uasset'
[2025.05.27-12.05.33:973][657]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.27-12.05.33:973][657]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_27
[2025.05.27-12.05.33:990][657]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/saliva_lod0_mesh_Skeleton.saliva_lod0_mesh_Skeleton]
[2025.05.27-12.05.33:990][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Skeleton]
[2025.05.27-12.05.33:991][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Skeleton_Auto1
[2025.05.27-12.05.33:991][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/saliva_lod0_mesh_Skeleton_Auto12A77EA8B4A117E9F60FE64B579604D52.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.27-12.05.33:993][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/saliva_lod0_mesh] ([1] browsable assets)...
[2025.05.27-12.05.33:993][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/saliva_lod0_mesh]
[2025.05.27-12.05.33:996][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Auto1
[2025.05.27-12.05.33:996][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/saliva_lod0_mesh_Auto11C22A6E24F3534416DD8F2A73B3530C7.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Auto1.uasset'
[2025.05.27-12.05.33:997][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_saliva_shader] ([1] browsable assets)...
[2025.05.27-12.05.33:997][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_saliva_shader]
[2025.05.27-12.05.33:998][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_saliva_shader_Auto1
[2025.05.27-12.05.33:998][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_saliva_shader_Auto18EFA46D8427D226ECDF17FBBAC3AB6A4.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_saliva_shader_Auto1.uasset'
[2025.05.27-12.05.34:000][657]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.27-12.05.34:000][657]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_28
[2025.05.27-12.05.34:075][657]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/teeth_lod0_mesh_Skeleton.teeth_lod0_mesh_Skeleton]
[2025.05.27-12.05.34:075][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Skeleton]
[2025.05.27-12.05.34:077][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Skeleton_Auto1
[2025.05.27-12.05.34:078][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/teeth_lod0_mesh_Skeleton_Auto152E01188461392E6415F3B9F7772C300.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.27-12.05.34:078][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/teeth_lod0_mesh] ([1] browsable assets)...
[2025.05.27-12.05.34:078][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/teeth_lod0_mesh]
[2025.05.27-12.05.34:091][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Auto1
[2025.05.27-12.05.34:091][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/teeth_lod0_mesh_Auto19DF9CE7A4DC48DE3BCF5B5AB46DA2CD1.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Auto1.uasset'
[2025.05.27-12.05.34:092][657]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_teeth_shader] ([1] browsable assets)...
[2025.05.27-12.05.34:092][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_teeth_shader]
[2025.05.27-12.05.34:092][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_teeth_shader_Auto1
[2025.05.27-12.05.34:092][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_teeth_shader_Auto1E52643814D33C045BFAC0B9323942E9A.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_teeth_shader_Auto1.uasset'
[2025.05.27-12.05.34:093][657]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/teeth_normal_map] ([1] browsable assets)...
[2025.05.27-12.05.34:095][657]OBJ SavePackage:     Rendered thumbnail for [Texture2D /Game/untitled_category/untitled_asset/teeth_normal_map.teeth_normal_map]
[2025.05.27-12.05.34:095][657]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/teeth_normal_map]
[2025.05.27-12.05.34:239][657]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/teeth_normal_map_Auto1
[2025.05.27-12.05.34:239][657]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/teeth_normal_map_Auto1C56F03C44C57A5A1DA37048EEFFA5E2C.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/teeth_normal_map_Auto1.uasset'
[2025.05.27-12.05.34:240][657]LogFileHelpers: Auto-saving content packages took 1.496
[2025.05.27-12.05.40:733][733]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.05.46:774][813]LogSlate: Window 'Message Log' being destroyed
[2025.05.27-12.05.46:946][813]LogSlate: Window 'Message Log' being destroyed
[2025.05.27-12.05.50:727][882]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.05.50:911][886]LogSlate: Warning: Unable to rasterize '../../../Engine/Content/Editor/Slate/Starship/AssetIcons/SkeletalMesh_64.svg'. File could not be found
[2025.05.27-12.05.51:090][888]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_29
[2025.05.27-12.05.56:599][979]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_30
[2025.05.27-12.05.56:680][980]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_31
[2025.05.27-12.05.56:923][984]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_32
[2025.05.27-12.05.57:078][985]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_33
[2025.05.27-12.05.57:199][987]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_34
[2025.05.27-12.05.57:381][990]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_35
[2025.05.27-12.05.57:510][992]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_36
[2025.05.27-12.05.57:629][994]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_37
[2025.05.27-12.05.57:744][996]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_38
[2025.05.27-12.05.57:859][998]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_39
[2025.05.27-12.05.57:978][  0]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_40
[2025.05.27-12.06.00:702][ 62]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.06.10:769][331]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.06.20:745][646]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.06.30:751][526]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.06.40:758][450]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.06.50:758][368]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: ================================================
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: === FShaderJobCache stats ===
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Total job queries 462, among them cache hits 114 (24.68%), DDC hits 325 (70.35%), Duplicates 16 (3.46%)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Tracking 332 distinct input hashes that result in 269 distinct outputs (81.02%)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: RAM used: 164.68 KiB of 3.20 GiB budget. Usage: 0.00%
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: === Shader Compilation stats ===
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Shaders Compiled: 7
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Jobs assigned 7, completed 7 (100%)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Average time worker was idle: 8.74 s
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Time job spent in pending queue: average 0.06 s, longest 0.08 s
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Job execution time: average 1.00 s, max 1.62 s
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Job life time (pending + execution): average 1.05 s, max 1.69
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Shader code size: average 8.273 KiB, min 5.031 KiB, max 11.516 KiB
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Time at least one job was in flight (either pending or executed): 3.34 s
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Mutex wait stall in FShaderJobCache::SubmitJobs:  0.90%
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Jobs were issued in 7 batches (only local compilation was used), average 1.00 jobs/batch
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Average processing rate: 2.09 jobs/sec
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Total thread time: 2.758 s
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Total thread preprocess time: 7.667 s
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Percentage time preprocessing: 278.04%
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Effective parallelization: 0.83 (times faster than compiling all shaders on one thread). Compare with number of workers: 16
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Top 5 most expensive shader types by average time:
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display:                                TMaterialCHSFNoLightMapPolicy (compiled    2 times, average 0.66 sec, max 0.67 sec, min 0.65 sec)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy (compiled    2 times, average 0.37 sec, max 0.38 sec, min 0.36 sec)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display:                                                 FLumenCardPS (compiled    1 times, average 0.26 sec, max 0.26 sec, min 0.26 sec)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display:                                                 FLumenCardVS (compiled    1 times, average 0.22 sec, max 0.22 sec, min 0.22 sec)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display:                                 TBasePassVSFNoLightMapPolicy (compiled    1 times, average 0.21 sec, max 0.21 sec, min 0.21 sec)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Top 5 shader types by total compile time:
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display:                                TMaterialCHSFNoLightMapPolicy - 48.03% of total time (compiled    2 times, average 0.66 sec, max 0.67 sec, min 0.65 sec)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy - 27.01% of total time (compiled    2 times, average 0.37 sec, max 0.38 sec, min 0.36 sec)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display:                                                 FLumenCardPS - 9.31% of total time (compiled    1 times, average 0.26 sec, max 0.26 sec, min 0.26 sec)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display:                                                 FLumenCardVS - 7.86% of total time (compiled    1 times, average 0.22 sec, max 0.22 sec, min 0.22 sec)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display:                                 TBasePassVSFNoLightMapPolicy - 7.79% of total time (compiled    1 times, average 0.21 sec, max 0.21 sec, min 0.21 sec)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: === Material stats ===
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Materials Cooked:        0
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Materials Translated:    178
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Material Total Translate Time: 0.08 s
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Material Translation Only: 0.04 s (52%)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Material DDC Serialization Only: 0.00 s (4%)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: Material Cache Hits: 51 (29%)
[2025.05.27-12.06.58:256][ 61]LogShaderCompilers: Display: ================================================
[2025.05.27-12.07.00:765][292]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.07.10:776][219]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.07.20:782][146]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.07.30:333][ 29]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1668.856934
[2025.05.27-12.07.30:594][ 53]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-12.07.30:594][ 53]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1669.108643, Update Interval: 355.949585
[2025.05.27-12.07.30:788][ 71]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.07.40:789][  1]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.07.50:799][940]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.08.00:803][828]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.08.10:809][718]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.08.20:820][601]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.08.30:826][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.08.40:828][363]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.08.50:836][281]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.09.00:840][199]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.09.10:850][123]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.09.20:852][ 45]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.09.30:853][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.09.40:858][898]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.09.50:864][830]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.10.00:865][753]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.10.10:871][681]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.10.20:880][601]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.10.30:883][521]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.10.40:886][413]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.10.50:896][333]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.11.00:904][226]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.11.10:911][109]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.11.20:916][ 28]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.11.30:923][937]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.11.40:934][856]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.11.50:937][769]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.12.00:948][687]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.12.10:955][612]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.12.20:955][537]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.12.30:957][448]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.12.40:966][365]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.12.50:976][278]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.13.00:985][178]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.13.10:991][ 82]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.13.20:999][995]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.13.31:000][902]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.13.41:008][821]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.13.51:018][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.14.01:026][661]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.14.11:034][581]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.14.21:039][509]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.14.21:039][509]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2079.559814
[2025.05.27-12.14.21:580][559]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-12.14.21:580][559]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2080.087402, Update Interval: 325.624573
[2025.05.27-12.14.31:047][436]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.14.41:056][360]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.14.51:059][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.15.01:060][209]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.15.11:063][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.15.21:066][ 22]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.15.31:071][933]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.15.41:076][840]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.15.51:079][752]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.16.01:084][667]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.16.11:086][578]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.16.21:096][497]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.16.31:103][416]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.16.39:623][966]LogAssetEditorSubsystem: Opening Asset editor for Texture2D /Game/untitled_category/untitled_asset/teeth_normal_map.teeth_normal_map
[2025.05.27-12.16.41:195][988]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-12.16.45:146][ 42]LogSlate: Window 'teeth_normal_map' being destroyed
[2025.05.27-12.16.46:742][127]LogUObjectHash: Compacting FUObjectHashTables data took   0.80ms
[2025.05.27-12.16.48:267][127]LogSlate: Window 'Save Content' being destroyed
[2025.05.27-12.16.48:340][127]LogStall: Shutdown...
[2025.05.27-12.16.48:340][127]LogStall: Shutdown complete.
[2025.05.27-12.16.48:390][127]LogSlate: Window 'BlenderLinkProject - Unreal Editor' being destroyed
[2025.05.27-12.16.48:450][127]Cmd: QUIT_EDITOR
[2025.05.27-12.16.48:451][128]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.05.27-12.16.48:457][128]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.05.27-12.16.48:458][128]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.05.27-12.16.48:458][128]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.05.27-12.16.48:466][128]LogWorld: UWorld::CleanupWorld for TestLevel, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:467][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:467][128]LogWorldPartition: UWorldPartition::Uninitialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel
[2025.05.27-12.16.48:478][128]LogStylusInput: Shutting down StylusInput subsystem.
[2025.05.27-12.16.48:479][128]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.05.27-12.16.48:483][128]LogWorld: UWorld::CleanupWorld for World_38, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:483][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:483][128]LogWorld: UWorld::CleanupWorld for World_37, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:483][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:483][128]LogWorld: UWorld::CleanupWorld for World_36, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:483][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:484][128]LogWorld: UWorld::CleanupWorld for World_35, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:484][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:484][128]LogWorld: UWorld::CleanupWorld for World_34, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:484][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:484][128]LogWorld: UWorld::CleanupWorld for World_33, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:484][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:484][128]LogWorld: UWorld::CleanupWorld for World_32, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:484][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:484][128]LogWorld: UWorld::CleanupWorld for World_31, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:484][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:484][128]LogWorld: UWorld::CleanupWorld for World_30, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:484][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:485][128]LogWorld: UWorld::CleanupWorld for World_29, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:485][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:485][128]LogWorld: UWorld::CleanupWorld for World_28, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:485][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:485][128]LogWorld: UWorld::CleanupWorld for World_27, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:485][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:486][128]LogWorld: UWorld::CleanupWorld for World_26, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:486][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:486][128]LogWorld: UWorld::CleanupWorld for World_25, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:486][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:486][128]LogWorld: UWorld::CleanupWorld for World_24, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:486][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:486][128]LogWorld: UWorld::CleanupWorld for World_23, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:486][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:486][128]LogWorld: UWorld::CleanupWorld for World_22, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:486][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:486][128]LogWorld: UWorld::CleanupWorld for World_21, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:486][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:486][128]LogWorld: UWorld::CleanupWorld for World_20, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:486][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:486][128]LogWorld: UWorld::CleanupWorld for World_39, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:487][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:487][128]LogWorld: UWorld::CleanupWorld for World_40, bSessionEnded=true, bCleanupResources=true
[2025.05.27-12.16.48:487][128]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-12.16.48:487][128]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.05.27-12.16.48:493][128]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroying ET Analytics provider
[2025.05.27-12.16.48:493][128]LogAnalytics: Display: [UEEditor.Rocket.Release] Ended ET Analytics provider session
[2025.05.27-12.16.48:493][128]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroyed ET Analytics provider
[2025.05.27-12.16.48:496][128]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.05.27-12.16.48:496][128]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.05.27-12.16.48:496][128]LogAudio: Display: Audio Device unregistered from world 'TestLevel'.
[2025.05.27-12.16.48:496][128]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.05.27-12.16.48:496][128]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.05.27-12.16.48:499][128]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.05.27-12.16.48:506][128]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.05.27-12.16.48:506][128]LogAudio: Display: Audio Device Manager Shutdown
[2025.05.27-12.16.48:508][128]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.05.27-12.16.48:510][128]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.05.27-12.16.48:510][128]LogExit: Preparing to exit.
[2025.05.27-12.16.48:619][128]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.27-12.16.49:442][128]LogEditorDataStorage: Deinitializing
[2025.05.27-12.16.50:024][128]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.05.27-12.16.50:033][128]LogExit: Editor shut down
[2025.05.27-12.16.50:036][128]LogExit: Transaction tracking system shut down
[2025.05.27-12.16.50:145][128]LogExit: Object subsystem successfully closed.
[2025.05.27-12.16.50:165][128]LogShaderCompilers: Display: Shaders left to compile 0
[2025.05.27-12.16.50:828][128]LogMemoryProfiler: Shutdown
[2025.05.27-12.16.50:828][128]LogNetworkingProfiler: Shutdown
[2025.05.27-12.16.50:828][128]LoadingProfiler: Shutdown
[2025.05.27-12.16.50:829][128]LogTimingProfiler: Shutdown
[2025.05.27-12.16.50:835][128]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.27-12.16.50:835][128]LogBlenderLink: Closing listener socket
[2025.05.27-12.16.50:835][128]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.27-12.16.51:186][128]LogChaosDD: Chaos Debug Draw Shutdown
[2025.05.27-12.16.51:186][128]LogStudioTelemetry: Display: Shutdown StudioTelemetry Module
[2025.05.27-12.16.51:186][128]LogNFORDenoise: NFORDenoise function shutting down
[2025.05.27-12.16.51:186][128]RenderDocPlugin: plugin has been unloaded.
[2025.05.27-12.16.51:214][128]LogHttp: Warning: [FHttpManager::Shutdown] Unbinding delegates for 1 outstanding Http Requests:
[2025.05.27-12.16.51:214][128]LogHttp: Warning: 	verb=[POST] url=[https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7BFBBF1E49-4D68-56F4-7B85-5D9699840F25%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.5.4-40574608%2B%2B%2BUE5%2BRelease-5.5&UserID=5eff80b14364fb2f37e5468dbd2f7de6%7C0de775007ac941b984ac36d970a4fb1c%7C6ba94555-8ddf-4db5-b84c-ec5c48d27de3&AppEnvironment=datacollector-binary&UploadType=eteventstream] refs=[2] status=Processing
[2025.05.27-12.16.52:775][128]LogEOSSDK: FEOSSDKManager::Shutdown EOS_Shutdown Result=[EOS_Success]
[2025.05.27-12.16.52:780][128]LogXGEController: Cleaning working directory: C:/Users/<USER>/AppData/Local/Temp/UnrealXGEWorkingDir/
[2025.05.27-12.16.52:784][128]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
[2025.05.27-12.16.52:784][128]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
[2025.05.27-12.16.52:784][128]LogPakFile: Destroying PakPlatformFile
[2025.05.27-12.16.53:010][128]LogD3D12RHI: ~FD3D12DynamicRHI
[2025.05.27-12.16.53:074][128]LogExit: Exiting.
[2025.05.27-12.16.53:095][128]Log file closed, 05/27/25 17:46:53
