﻿Log file open, 05/27/25 11:42:39
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=37868)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: BlenderLinkProject
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\Plugins\BlenderLinkProject\BlenderLinkProject.uproject""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.237968
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-98A9E9054780016CD1082A94C553DF32
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/Plugins/BlenderLinkProject/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogConfig: Display: Loading VulkanPC ini files took 0.04 seconds
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading Mac ini files took 0.05 seconds
LogConfig: Display: Loading Android ini files took 0.05 seconds
LogConfig: Display: Loading IOS ini files took 0.05 seconds
LogConfig: Display: Loading Unix ini files took 0.05 seconds
LogAssetRegistry: Display: Asset registry cache read as 43.8 MiB from H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_0.bin
LogConfig: Display: Loading TVOS ini files took 0.05 seconds
LogConfig: Display: Loading Windows ini files took 0.06 seconds
LogConfig: Display: Loading VisionOS ini files took 0.06 seconds
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogConfig: Display: Loading Linux ini files took 0.06 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.06 seconds
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin Mutable
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin CaptureData
LogPluginManager: Mounting Engine plugin LensComponent
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OpenColorIO
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LiveLinkControlRig
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RigLogicMutable
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin MetaHuman
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaIOFramework
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SkeletalMerging
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin XRBase
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin ARUtilities
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Engine plugin AppleARKitFaceSupport
LogPluginManager: Mounting Engine plugin AppleARKit
LogPluginManager: Mounting Project plugin BlenderLink
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 2147483647 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: AMD Radeon RX 6900 XT
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: 
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.52ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CaptureData' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.11ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LiveLinkControlRig' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RigLogicMutable' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MetaHuman' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalMerging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XRBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ARUtilities' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKitFaceSupport' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleARKit' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlenderLink' had been unloaded. Reloading on-demand took 0.06ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.05.27-06.12.39:472][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.05.27-06.12.39:472][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.05.27-06.12.39:472][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.05.27-06.12.39:472][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.05.27-06.12.39:472][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.05.27-06.12.39:472][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.05.27-06.12.39:472][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.05.27-06.12.39:472][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.05.27-06.12.39:473][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.05.27-06.12.39:473][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.05.27-06.12.39:473][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.05.27-06.12.39:475][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.05.27-06.12.39:475][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.05.27-06.12.39:475][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.05.27-06.12.39:475][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.05.27-06.12.39:475][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.05.27-06.12.39:475][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.05.27-06.12.39:475][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.05.27-06.12.39:478][  0]LogRHI: Using Default RHI: D3D12
[2025.05.27-06.12.39:478][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.27-06.12.39:478][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.27-06.12.39:481][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.05.27-06.12.39:481][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.27-06.12.39:573][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.05.27-06.12.39:573][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.27-06.12.39:573][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 3 output[s]
[2025.05.27-06.12.39:573][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.27-06.12.39:573][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.05.27-06.12.39:719][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.05.27-06.12.39:719][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.27-06.12.39:719][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.27-06.12.39:719][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.05.27-06.12.39:719][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.05.27-06.12.39:728][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.05.27-06.12.39:728][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.05.27-06.12.39:728][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.27-06.12.39:728][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.05.27-06.12.39:728][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.05.27-06.12.39:728][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.27-06.12.39:728][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.05.27-06.12.39:728][  0]LogHAL: Display: Platform has ~ 64 GB [68631805952 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.05.27-06.12.39:729][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.05.27-06.12.39:729][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.27-06.12.39:729][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.05.27-06.12.39:729][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.05.27-06.12.39:729][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.27-06.12.39:729][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.05.27-06.12.39:729][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.05.27-06.12.39:729][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.05.27-06.12.39:729][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.05.27-06.12.39:729][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.05.27-06.12.39:729][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.27-06.12.39:729][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.05.27-06.12.39:729][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.05.27-06.12.39:729][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/Plugins/BlenderLinkProject/Saved/Config/WindowsEditor/Editor.ini]
[2025.05.27-06.12.39:729][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.05.27-06.12.39:729][  0]LogInit: User: Shashank
[2025.05.27-06.12.39:729][  0]LogInit: CPU Page size=4096, Cores=16
[2025.05.27-06.12.39:729][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.05.27-06.12.40:007][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.05.27-06.12.40:007][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.05.27-06.12.40:007][  0]LogMemory: Process Physical Memory: 627.43 MB used, 643.30 MB peak
[2025.05.27-06.12.40:007][  0]LogMemory: Process Virtual Memory: 760.02 MB used, 760.02 MB peak
[2025.05.27-06.12.40:007][  0]LogMemory: Physical Memory: 23577.31 MB used,  41875.08 MB free, 65452.39 MB total
[2025.05.27-06.12.40:007][  0]LogMemory: Virtual Memory: 39086.49 MB used,  30461.90 MB free, 69548.39 MB total
[2025.05.27-06.12.40:007][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.05.27-06.12.40:011][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.05.27-06.12.40:017][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.05.27-06.12.40:017][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.05.27-06.12.40:018][  0]LogInit: Using OS detected language (en-GB).
[2025.05.27-06.12.40:018][  0]LogInit: Using OS detected locale (en-IN).
[2025.05.27-06.12.40:018][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.05.27-06.12.40:019][  0]LogInit: Setting process to per monitor DPI aware
[2025.05.27-06.12.40:277][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.05.27-06.12.40:277][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.05.27-06.12.40:278][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.05.27-06.12.40:290][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.05.27-06.12.40:290][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.05.27-06.12.40:370][  0]LogRHI: Using Default RHI: D3D12
[2025.05.27-06.12.40:370][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.27-06.12.40:370][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.27-06.12.40:370][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.27-06.12.40:370][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.27-06.12.40:370][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.05.27-06.12.40:370][  0]LogWindows: Attached monitors:
[2025.05.27-06.12.40:370][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY5'
[2025.05.27-06.12.40:370][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY1' [PRIMARY]
[2025.05.27-06.12.40:370][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY6'
[2025.05.27-06.12.40:370][  0]LogWindows: Found 3 attached monitors.
[2025.05.27-06.12.40:370][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.27-06.12.40:370][  0]LogRHI: RHI Adapter Info:
[2025.05.27-06.12.40:370][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.05.27-06.12.40:370][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.27-06.12.40:370][  0]LogRHI:      Driver Date: 4-25-2025
[2025.05.27-06.12.40:370][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.05.27-06.12.40:403][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.05.27-06.12.40:471][  0]LogNvidiaAftermath: Aftermath initialized
[2025.05.27-06.12.40:471][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.05.27-06.12.40:544][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: Bindless resources are supported
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: Raster order views are supported
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.05.27-06.12.40:544][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.05.27-06.12.40:570][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000005FE0DD75300)
[2025.05.27-06.12.40:570][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000005FE0DD75580)
[2025.05.27-06.12.40:570][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000005FE0DD75800)
[2025.05.27-06.12.40:570][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.05.27-06.12.40:570][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.05.27-06.12.40:570][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.05.27-06.12.40:570][  0]LogRHI: Texture pool is 9808 MB (70% of 14012 MB)
[2025.05.27-06.12.40:570][  0]LogD3D12RHI: Async texture creation enabled
[2025.05.27-06.12.40:570][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.05.27-06.12.40:582][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.05.27-06.12.40:586][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.05.27-06.12.40:592][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all'
[2025.05.27-06.12.40:592][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all" ]
[2025.05.27-06.12.40:609][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.05.27-06.12.40:609][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.05.27-06.12.40:609][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.05.27-06.12.40:609][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.05.27-06.12.40:609][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.05.27-06.12.40:609][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.05.27-06.12.40:609][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.05.27-06.12.40:609][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.05.27-06.12.40:609][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.05.27-06.12.40:631][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.05.27-06.12.40:631][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.05.27-06.12.40:631][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.05.27-06.12.40:631][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.05.27-06.12.40:631][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.05.27-06.12.40:631][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.05.27-06.12.40:631][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.05.27-06.12.40:631][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.05.27-06.12.40:631][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.05.27-06.12.40:631][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.05.27-06.12.40:644][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.05.27-06.12.40:644][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.05.27-06.12.40:657][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.05.27-06.12.40:657][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.05.27-06.12.40:657][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.05.27-06.12.40:657][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.05.27-06.12.40:669][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.05.27-06.12.40:669][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.05.27-06.12.40:669][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.05.27-06.12.40:682][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.05.27-06.12.40:682][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.05.27-06.12.40:682][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.05.27-06.12.40:682][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.05.27-06.12.40:694][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.05.27-06.12.40:694][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.05.27-06.12.40:708][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.05.27-06.12.40:708][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.05.27-06.12.40:708][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.05.27-06.12.40:708][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.05.27-06.12.40:708][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.05.27-06.12.40:744][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   SF_METAL
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   VVM_1_0
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.05.27-06.12.40:747][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.05.27-06.12.40:747][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.05.27-06.12.40:747][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.05.27-06.12.40:750][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.05.27-06.12.40:750][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.05.27-06.12.40:750][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.05.27-06.12.40:750][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.27-06.12.40:750][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.05.27-06.12.40:807][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.05.27-06.12.40:807][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.27-06.12.40:807][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.05.27-06.12.40:807][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.05.27-06.12.40:808][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.27-06.12.40:808][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.27-06.12.40:808][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.05.27-06.12.40:808][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 33284 --child-id Zen_33284_Startup'
[2025.05.27-06.12.40:873][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.05.27-06.12.40:873][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.066 seconds
[2025.05.27-06.12.40:875][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.05.27-06.12.40:878][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.00 seconds.
[2025.05.27-06.12.40:878][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.01ms. RandomReadSpeed=3072.33MBs, RandomWriteSpeed=404.83MBs. Assigned SpeedClass 'Local'
[2025.05.27-06.12.40:878][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.05.27-06.12.40:878][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.05.27-06.12.40:878][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.05.27-06.12.40:878][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.05.27-06.12.40:878][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.05.27-06.12.40:878][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.05.27-06.12.40:878][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.05.27-06.12.40:879][  0]LogShaderCompilers: Guid format shader working directory is 19 characters bigger than the processId version (H:/Plugins/BlenderLinkProject/Intermediate/Shaders/WorkingDirectory/33284/).
[2025.05.27-06.12.40:879][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/9863D2544ACF1271A0AF18954F8D8306/'.
[2025.05.27-06.12.40:879][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.05.27-06.12.40:879][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.05.27-06.12.40:880][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/Plugins/BlenderLinkProject/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.05.27-06.12.40:880][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.05.27-06.12.41:284][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.05.27-06.12.41:794][  0]LogSlate: Using FreeType 2.10.0
[2025.05.27-06.12.41:794][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.05.27-06.12.41:795][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.27-06.12.41:795][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.27-06.12.41:795][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.27-06.12.41:795][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.27-06.12.41:795][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.27-06.12.41:795][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.27-06.12.41:816][  0]LogAssetRegistry: FAssetRegistry took 0.0020 seconds to start up
[2025.05.27-06.12.41:817][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.05.27-06.12.41:822][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin.
[2025.05.27-06.12.41:822][  0]LogAssetRegistry: Error: Package is unloadable: H:/Plugins/BlenderLinkProject/Content/SM_Rock_ukxmdhuga.uasset. Reason: Invalid value for PACKAGE_FILE_TAG at start of file.
[2025.05.27-06.12.41:997][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-06.12.41:998][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.05.27-06.12.41:998][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.05.27-06.12.41:998][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.05.27-06.12.42:009][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.05.27-06.12.42:009][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.05.27-06.12.42:031][  0]LogDeviceProfileManager: Active device profile: [000005FE2ADCD600][000005FE28F50000 66] WindowsEditor
[2025.05.27-06.12.42:031][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.05.27-06.12.42:032][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.05.27-06.12.42:034][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.05.27-06.12.42:034][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.05.27-06.12.42:062][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:062][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.05.27-06.12.42:062][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-06.12.42:062][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:062][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.05.27-06.12.42:062][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-06.12.42:062][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:063][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.05.27-06.12.42:063][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-06.12.42:063][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:063][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.05.27-06.12.42:063][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-06.12.42:063][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:064][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:064][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.05.27-06.12.42:064][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.27-06.12.42:064][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:064][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.05.27-06.12.42:064][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.27-06.12.42:064][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:064][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.05.27-06.12.42:065][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-06.12.42:066][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:066][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.05.27-06.12.42:066][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.27-06.12.42:066][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.05.27-06.12.42:066][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.27-06.12.42:066][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:066][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.05.27-06.12.42:066][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.27-06.12.42:066][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.05.27-06.12.42:066][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-06.12.42:066][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.27-06.12.42:066][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.05.27-06.12.42:067][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.27-06.12.42:210][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.05.27-06.12.42:210][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.05.27-06.12.42:210][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.05.27-06.12.42:210][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.05.27-06.12.42:210][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.05.27-06.12.42:324][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.43ms
[2025.05.27-06.12.42:341][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.44ms
[2025.05.27-06.12.42:351][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.43ms
[2025.05.27-06.12.42:352][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.43ms
[2025.05.27-06.12.42:532][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.05.27-06.12.42:532][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.05.27-06.12.42:537][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.05.27-06.12.42:537][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.05.27-06.12.42:538][  0]LogLiveCoding: Display: First instance in process group "UE_BlenderLinkProject_0xe99fe6f9", spawning console
[2025.05.27-06.12.42:541][  0]LogLiveCoding: Display: Waiting for server
[2025.05.27-06.12.42:552][  0]LogSlate: Border
[2025.05.27-06.12.42:552][  0]LogSlate: BreadcrumbButton
[2025.05.27-06.12.42:552][  0]LogSlate: Brushes.Title
[2025.05.27-06.12.42:552][  0]LogSlate: Default
[2025.05.27-06.12.42:552][  0]LogSlate: Icons.Save
[2025.05.27-06.12.42:552][  0]LogSlate: Icons.Toolbar.Settings
[2025.05.27-06.12.42:552][  0]LogSlate: ListView
[2025.05.27-06.12.42:552][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.05.27-06.12.42:552][  0]LogSlate: SoftwareCursor_Grab
[2025.05.27-06.12.42:552][  0]LogSlate: TableView.DarkRow
[2025.05.27-06.12.42:552][  0]LogSlate: TableView.Row
[2025.05.27-06.12.42:552][  0]LogSlate: TreeView
[2025.05.27-06.12.42:585][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.05.27-06.12.42:609][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.05.27-06.12.42:611][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 1.622 ms
[2025.05.27-06.12.42:618][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.41ms
[2025.05.27-06.12.42:630][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.05.27-06.12.42:630][  0]LogInit: XR: MultiViewport is Disabled
[2025.05.27-06.12.42:630][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.05.27-06.12.42:630][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.05.27-06.12.42:803][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.05.27-06.12.42:803][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.05.27-06.12.42:803][  0]LogNNERuntimeORT: 1: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.05.27-06.12.42:803][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.05.27-06.12.42:803][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.05.27-06.12.42:937][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.27-06.12.42:937][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.27-06.12.42:967][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.44ms
[2025.05.27-06.12.43:061][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 2E3F4708426745E08000000000009500 | Instance: 0A1C37F949390512EAD9B597C14B2F38 (DESKTOP-E41IK6R-33284).
[2025.05.27-06.12.43:237][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.05.27-06.12.43:237][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.05.27-06.12.43:237][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:54747'.
[2025.05.27-06.12.43:239][  0]LogUdpMessaging: Display: Added local interface '192.168.31.37' to multicast group '230.0.0.1:6666'
[2025.05.27-06.12.43:239][  0]LogUdpMessaging: Display: Added local interface '172.21.160.1' to multicast group '230.0.0.1:6666'
[2025.05.27-06.12.43:239][  0]LogUdpMessaging: Display: Added local interface '172.30.208.1' to multicast group '230.0.0.1:6666'
[2025.05.27-06.12.43:245][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.05.27-06.12.43:289][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.05.27-06.12.43:338][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.05.27-06.12.43:338][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.05.27-06.12.43:351][  0]LogMetaSound: MetaSound Engine Initialized
[2025.05.27-06.12.43:469][  0]LogMutable: Creating Mutable Customizable Object System.
[2025.05.27-06.12.43:504][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.05.27-06.12.43:572][  0]SourceControl: Revision control is disabled
[2025.05.27-06.12.43:582][  0]SourceControl: Revision control is disabled
[2025.05.27-06.12.43:598][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.43ms
[2025.05.27-06.12.43:604][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.41ms
[2025.05.27-06.12.43:849][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.27-06.12.43:849][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.27-06.12.43:898][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.05.27-06.12.44:319][  0]LogSkeletalMesh: Building Skeletal Mesh Face_Archetype...
[2025.05.27-06.12.44:391][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (Face_Archetype) ...
[2025.05.27-06.12.50:402][  0]LogSkeletalMesh: Skeletal mesh [/MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-06.12.50:413][  0]LogSkeletalMesh: Built Skeletal Mesh [6.10s] /MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype
[2025.05.27-06.12.50:433][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.27-06.12.50:433][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.27-06.12.50:433][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.27-06.12.50:433][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.27-06.12.50:433][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.27-06.12.50:433][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.27-06.12.50:440][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.27-06.12.50:440][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.27-06.12.50:492][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.05.27-06.12.50:503][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.27-06.12.50:503][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.27-06.12.50:546][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.05.27-06.12.50:546][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.05.27-06.12.50:546][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.05.27-06.12.50:546][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.05.27-06.12.50:547][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.05.27-06.12.50:547][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.05.27-06.12.50:548][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.05.27-06.12.50:548][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.05.27-06.12.50:548][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.05.27-06.12.50:549][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.05.27-06.12.50:549][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.05.27-06.12.50:549][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.05.27-06.12.50:549][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.05.27-06.12.50:549][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.05.27-06.12.50:550][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.05.27-06.12.50:550][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.05.27-06.12.50:550][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.05.27-06.12.50:550][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.05.27-06.12.50:550][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.05.27-06.12.50:550][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.05.27-06.12.50:551][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.05.27-06.12.50:551][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.05.27-06.12.50:551][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.05.27-06.12.50:552][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.05.27-06.12.50:552][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.05.27-06.12.50:552][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.05.27-06.12.50:552][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.05.27-06.12.50:552][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.05.27-06.12.50:553][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.05.27-06.12.50:553][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.05.27-06.12.50:553][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.05.27-06.12.50:553][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.05.27-06.12.50:553][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.05.27-06.12.50:553][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.05.27-06.12.50:553][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.05.27-06.12.50:579][  0]LogCollectionManager: Loaded 0 collections in 0.000708 seconds
[2025.05.27-06.12.50:580][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Saved/Collections/' took 0.00s
[2025.05.27-06.12.50:580][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Developers/Shashank/Collections/' took 0.00s
[2025.05.27-06.12.50:580][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Collections/' took 0.00s
[2025.05.27-06.12.50:632][  0]LogBlenderLink: Initializing BlenderLink socket listener
[2025.05.27-06.12.50:632][  0]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.27-06.12.50:632][  0]LogBlenderLink: Set socket buffer sizes to 524288 bytes
[2025.05.27-06.12.50:632][  0]LogBlenderLink: Binding socket to 127.0.0.1:2907
[2025.05.27-06.12.50:632][  0]LogBlenderLink: Warning: Socket bind success: true
[2025.05.27-06.12.50:632][  0]LogBlenderLink: Warning: Socket listen success: true
[2025.05.27-06.12.50:632][  0]LogBlenderLink: Waiting for client connection...
[2025.05.27-06.12.50:647][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.27-06.12.50:648][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.27-06.12.50:648][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.27-06.12.50:648][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.27-06.12.50:648][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.27-06.12.50:648][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.27-06.12.50:654][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.27-06.12.50:654][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.27-06.12.50:670][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-05-27T06:12:50.670Z using C
[2025.05.27-06.12.50:670][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=BlenderLinkProject, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.05.27-06.12.50:670][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.27-06.12.50:670][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.05.27-06.12.50:676][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.05.27-06.12.50:677][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.05.27-06.12.50:677][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.05.27-06.12.50:677][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000048
[2025.05.27-06.12.50:677][  0]LogFab: Display: Logging in using persist
[2025.05.27-06.12.50:677][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.05.27-06.12.50:706][  0]LogUObjectArray: 52283 objects as part of root set at end of initial load.
[2025.05.27-06.12.50:706][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.05.27-06.12.50:719][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 38255 public script object entries (1030.17 KB)
[2025.05.27-06.12.50:719][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.05.27-06.12.50:821][  0]LogEngine: Initializing Engine...
[2025.05.27-06.12.50:823][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.05.27-06.12.50:824][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.05.27-06.12.50:899][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.05.27-06.12.50:911][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.05.27-06.12.50:919][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.05.27-06.12.50:919][  0]LogInit: Texture streaming: Enabled
[2025.05.27-06.12.50:927][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.05.27-06.12.50:937][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.05.27-06.12.50:941][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.05.27-06.12.50:941][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.05.27-06.12.50:941][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.05.27-06.12.50:941][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.05.27-06.12.50:941][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.05.27-06.12.50:941][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.05.27-06.12.50:942][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.05.27-06.12.50:942][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.05.27-06.12.50:942][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.05.27-06.12.50:942][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.05.27-06.12.50:942][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.05.27-06.12.50:942][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.05.27-06.12.50:942][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.05.27-06.12.50:942][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.05.27-06.12.50:942][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.05.27-06.12.50:946][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.05.27-06.12.50:999][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter Input (VB-Audio Voicemeeter VAIO)
[2025.05.27-06.12.51:000][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.05.27-06.12.51:000][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.05.27-06.12.51:000][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.05.27-06.12.51:001][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.05.27-06.12.51:001][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.05.27-06.12.51:003][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.05.27-06.12.51:003][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.05.27-06.12.51:003][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.05.27-06.12.51:004][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.05.27-06.12.51:004][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.05.27-06.12.51:009][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.05.27-06.12.51:011][  0]LogInit: Undo buffer set to 256 MB
[2025.05.27-06.12.51:011][  0]LogInit: Transaction tracking system initialized
[2025.05.27-06.12.51:021][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded H:/Plugins/BlenderLinkProject/Saved/SourceControl/UncontrolledChangelists.json
[2025.05.27-06.12.51:061][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.51ms
[2025.05.27-06.12.51:062][  0]LocalizationService: Localization service is disabled
[2025.05.27-06.12.51:072][  0]LogTimingProfiler: Initialize
[2025.05.27-06.12.51:072][  0]LogTimingProfiler: OnSessionChanged
[2025.05.27-06.12.51:072][  0]LoadingProfiler: Initialize
[2025.05.27-06.12.51:072][  0]LoadingProfiler: OnSessionChanged
[2025.05.27-06.12.51:072][  0]LogNetworkingProfiler: Initialize
[2025.05.27-06.12.51:072][  0]LogNetworkingProfiler: OnSessionChanged
[2025.05.27-06.12.51:073][  0]LogMemoryProfiler: Initialize
[2025.05.27-06.12.51:073][  0]LogMemoryProfiler: OnSessionChanged
[2025.05.27-06.12.51:182][  0]LogAutoReimportManager: Warning: Unable to watch directory H:/Plugins/BlenderLinkProject/Content/ as it will conflict with another watching H:/Plugins/BlenderLinkProject/Content/.
[2025.05.27-06.12.51:192][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/' took 0.01s
[2025.05.27-06.12.51:220][  0]LogPython: Using Python 3.11.8
[2025.05.27-06.12.52:189][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.05.27-06.12.52:200][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.05.27-06.12.52:205][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.05.27-06.12.52:250][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.05.27-06.12.52:250][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.05.27-06.12.52:307][  0]LogEditorDataStorage: Initializing
[2025.05.27-06.12.52:307][  0]LogEditorDataStorage: Initialized
[2025.05.27-06.12.52:308][  0]LogWindows: Attached monitors:
[2025.05.27-06.12.52:308][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY5'
[2025.05.27-06.12.52:308][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY1' [PRIMARY]
[2025.05.27-06.12.52:308][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY6'
[2025.05.27-06.12.52:308][  0]LogWindows: Found 3 attached monitors.
[2025.05.27-06.12.52:308][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.27-06.12.52:320][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.05.27-06.12.52:321][  0]SourceControl: Revision control is disabled
[2025.05.27-06.12.52:321][  0]LogUnrealEdMisc: Loading editor; pre map load, took 13.598
[2025.05.27-06.12.52:322][  0]Cmd: MAP LOAD FILE="H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/TestLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.05.27-06.12.52:324][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.05.27-06.12.52:324][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-06.12.52:334][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.05.27-06.12.52:366][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.05.27-06.12.52:369][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.54ms
[2025.05.27-06.12.52:374][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'TestLevel'.
[2025.05.27-06.12.52:374][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world TestLevel
[2025.05.27-06.12.52:375][  0]LogWorldPartition: ULevel::OnLevelLoaded(TestLevel)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.05.27-06.12.52:376][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.05.27-06.12.52:376][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.05.27-06.12.53:109][  0]LogAssetRegistry: Display: Asset registry cache written as 43.8 MiB to H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin
[2025.05.27-06.12.55:341][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.27-06.12.55:345][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.27-06.12.55:346][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.27-06.12.55:347][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.05.27-06.12.55:347][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.05.27-06.12.55:347][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.27-06.12.55:349][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.05.27-06.12.57:314][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.05.27-06.12.57:362][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.05.27-06.12.57:757][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-06.12.57:761][  0]LogSkeletalMesh: Built Skeletal Mesh [0.40s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.05.27-06.12.57:772][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.05.27-06.12.57:773][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.05.27-06.12.58:173][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-06.12.58:175][  0]LogSkeletalMesh: Built Skeletal Mesh [0.40s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.05.27-06.12.59:236][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-06.12.59:239][  0]LogSkeletalMesh: Built Skeletal Mesh [1.47s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.05.27-06.12.59:384][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.05.27-06.12.59:601][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.05.27-06.12.59:612][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-06.12.59:618][  0]LogSkeletalMesh: Built Skeletal Mesh [0.23s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.05.27-06.12.59:998][  0]LogWorldPartition: Display: WorldPartition initialize took 7.62 sec
[2025.05.27-06.13.00:074][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.05.27-06.13.04:690][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.27-06.13.04:704][  0]LogSkeletalMesh: Built Skeletal Mesh [5.10s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.05.27-06.13.05:319][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.05.27-06.13.05:547][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.17ms
[2025.05.27-06.13.05:548][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.05.27-06.13.05:550][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 1.999ms to complete.
[2025.05.27-06.13.05:559][  0]LogUnrealEdMisc: Total Editor Startup Time, took 26.835
[2025.05.27-06.13.05:707][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.05.27-06.13.05:791][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.27-06.13.05:843][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.27-06.13.05:890][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.27-06.13.05:944][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.27-06.13.05:979][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-06.13.05:979][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.05.27-06.13.05:980][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-06.13.05:980][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.05.27-06.13.05:980][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-06.13.05:980][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.05.27-06.13.05:980][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-06.13.05:980][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.05.27-06.13.05:980][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-06.13.05:980][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.05.27-06.13.05:980][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-06.13.05:980][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.05.27-06.13.05:980][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-06.13.05:980][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.05.27-06.13.05:981][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-06.13.05:981][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.05.27-06.13.05:981][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-06.13.05:981][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.05.27-06.13.05:981][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.27-06.13.05:982][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.05.27-06.13.06:022][  0]LogSlate: Took 0.000088 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.05.27-06.13.06:658][  0]LogSlate: External Image Picker: DecompressImage failed
[2025.05.27-06.13.06:844][  0]LogStall: Startup...
[2025.05.27-06.13.06:846][  0]LogStall: Startup complete.
[2025.05.27-06.13.06:852][  0]LogLoad: (Engine Initialization) Total time: 28.13 seconds
[2025.05.27-06.13.07:023][  0]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.05.27-06.13.07:039][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.05.27-06.13.07:041][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.05.27-06.13.07:042][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.05.27-06.13.07:042][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.27-06.13.07:119][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.27-06.13.07:119][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.05.27-06.13.07:120][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.05.27-06.13.07:120][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.05.27-06.13.07:120][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.27-06.13.07:182][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.27-06.13.07:182][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.05.27-06.13.07:451][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.05.27-06.13.07:451][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.05.27-06.13.07:608][  0]LogSlate: Took 0.000082 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.05.27-06.13.07:653][  0]LogSlate: Took 0.000118 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.05.27-06.13.07:655][  0]LogSlate: Took 0.000049 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.05.27-06.13.07:694][  0]LogStreaming: Display: FlushAsyncLoading(511): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-06.13.07:773][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-06.13.07:780][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.05.27-06.13.07:780][  0]LogFab: Display: Logging in using exchange code
[2025.05.27-06.13.07:780][  0]LogFab: Display: Reading exchange code from commandline
[2025.05.27-06.13.07:780][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.05.27-06.13.07:782][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.05.27-06.13.07:837][  0]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 20.31 ms. Compile time 16.76 ms, link time 3.23 ms.
[2025.05.27-06.13.07:863][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.05.27-06.13.07:867][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 85.386 ms
[2025.05.27-06.13.07:868][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BlenderLinkProjectEditor Win64 Development
[2025.05.27-06.13.08:080][  1]LogAssetRegistry: AssetRegistryGather time 0.0781s: AssetDataDiscovery 0.0138s, AssetDataGather 0.0114s, StoreResults 0.0529s. Wall time 26.2660s.
	NumCachedDirectories 0. NumUncachedDirectories 1854. NumCachedFiles 7965. NumUncachedFiles 0.
	BackgroundTickInterruptions 0.
[2025.05.27-06.13.08:125][  1]LogSourceControl: Uncontrolled asset enumeration started...
[2025.05.27-06.13.08:125][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.05.27-06.13.08:222][  1]LogSourceControl: Uncontrolled asset enumeration finished in 0.097215 seconds (Found 7941 uncontrolled assets)
[2025.05.27-06.13.08:268][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.05.27-06.13.08:744][  2]LogSlate: External Image Picker: DecompressImage failed
[2025.05.27-06.13.08:945][  3]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 18.192308
[2025.05.27-06.13.08:946][  3]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.05.27-06.13.08:946][  3]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 18.268295
[2025.05.27-06.13.09:887][  7]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.27-06.13.10:952][ 12]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 19.990482
[2025.05.27-06.13.10:954][ 12]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 607272702
[2025.05.27-06.13.10:954][ 12]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 19.990482, Update Interval: 319.796143
[2025.05.27-06.13.16:942][108]LogRenderer: Display: Invalidated clipmap due to WPO threshold change. This can occur due to resolution or FOV changes.
[2025.05.27-06.13.17:020][108]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.13.26:936][ 70]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.13.36:995][347]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.13.45:046][500]LogSlate: Took 0.000092 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.05.27-06.13.45:055][500]LogSlate: Warning: Unable to rasterize '../../../Engine/Content/Editor/Slate/Starship/AssetIcons/SkeletalMesh_64.svg'. File could not be found
[2025.05.27-06.13.45:076][501]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.05.27-06.13.45:160][502]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.05.27-06.13.46:965][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.13.49:368][737]LogAssetEditorSubsystem: Opening Asset editor for LevelSequence /Game/MetaHumans/Test/Test.Test
[2025.05.27-06.13.50:722][737]LogStreaming: Display: FlushAsyncLoading(512): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-06.13.56:972][163]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.14.02:058][445]LogStreaming: Display: FlushAsyncLoading(513): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-06.14.02:414][445]LogAssetEditorSubsystem: Opening Asset editor for AnimSequence /Game/MetaHumans/Test/Face_Archetype_Skeleton_Sequence.Face_Archetype_Skeleton_Sequence
[2025.05.27-06.14.02:647][445]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.05.27-06.14.02:950][445]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_3:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.27-06.14.04:047][445]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.05.27-06.14.04:263][445]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_3:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.27-06.14.04:485][445]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.05.27-06.14.04:662][445]LogSlate: Took 0.008850 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/FontAwesome.ttf' (139K)
[2025.05.27-06.14.05:079][447]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.05.27-06.14.07:044][467]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.14.17:115][613]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.14.27:233][706]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.14.36:895][806]LogSlate: Window 'Live Link' being destroyed
[2025.05.27-06.14.36:990][806]LogSlate: Window 'Live Link' being destroyed
[2025.05.27-06.14.37:170][810]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.14.40:878][858]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.05.27-06.14.47:275][938]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.14.47:956][944]LogSlate: Window 'Face_Archetype_Skeleton_Sequence' being destroyed
[2025.05.27-06.14.47:961][944]LogSlate: Window 'Face_Archetype_Skeleton_Sequence' being destroyed
[2025.05.27-06.14.48:028][944]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.05.27-06.14.48:028][944]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-06.14.48:092][944]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.05.27-06.14.48:093][944]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-06.14.48:328][944]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.05.27-06.14.57:262][189]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.15.07:351][297]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.15.17:353][327]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.15.27:353][357]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.15.37:353][387]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.15.47:353][417]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.15.57:353][447]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.16.07:355][477]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.16.17:354][507]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.16.27:354][537]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.16.37:353][567]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.16.47:354][597]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.16.57:354][627]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.17.07:355][657]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.17.17:357][687]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.17.27:356][717]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.17.37:358][747]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.17.47:358][777]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.17.57:360][807]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.18.07:360][837]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.18.17:363][867]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.18.27:361][897]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.18.31:028][908]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 340.351929
[2025.05.27-06.18.32:028][911]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-06.18.32:028][911]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 341.018188, Update Interval: 340.729401
[2025.05.27-06.18.37:362][927]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.18.47:362][957]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.18.57:363][987]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.19.07:365][ 17]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.19.17:366][ 47]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.19.27:368][ 77]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.19.37:367][107]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.19.47:368][137]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.19.57:369][167]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.20.07:370][197]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.20.17:370][227]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.20.27:370][257]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.20.37:372][287]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.20.47:372][317]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.20.57:372][347]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.21.07:373][377]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.21.17:373][407]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.21.27:373][437]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.21.37:374][467]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.21.47:375][497]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.21.57:377][527]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.22.07:377][557]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.22.17:378][587]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.22.27:378][617]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.22.37:379][647]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.22.47:380][677]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.22.57:382][707]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.23.07:383][737]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.23.17:382][767]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.23.27:384][797]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.23.37:384][827]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.23.47:385][857]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.23.57:386][887]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.24.07:386][917]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.24.16:053][943]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 685.376099
[2025.05.27-06.24.17:053][946]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-06.24.17:053][946]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 686.042969, Update Interval: 312.171387
[2025.05.27-06.24.17:386][947]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.24.27:387][977]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.24.37:388][  7]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.24.47:389][ 37]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.24.57:391][ 67]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.25.07:391][ 97]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.25.17:393][127]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.25.27:393][157]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.25.37:393][187]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.25.47:396][217]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.25.57:397][247]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.26.07:397][277]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.26.17:398][307]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.26.27:398][337]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.26.37:401][367]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.26.47:401][397]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.26.57:402][427]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.27.07:404][457]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.27.17:407][487]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.27.27:406][517]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.27.37:408][547]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.27.47:409][577]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.27.57:409][607]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.28.07:410][637]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.28.17:409][667]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.28.27:411][697]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.28.37:412][727]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.28.47:417][757]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.28.57:416][787]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.29.07:417][817]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.29.16:547][946]LogRenderer: Display: Invalidated clipmap due to WPO threshold change. This can occur due to resolution or FOV changes.
[2025.05.27-06.29.17:429][ 12]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.29.27:434][397]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.29.36:103][423]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1005.427551
[2025.05.27-06.29.37:104][426]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-06.29.37:104][426]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1006.095398, Update Interval: 338.738983
[2025.05.27-06.29.37:437][427]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.29.42:235][674]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.05.27-06.29.44:294][753]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/MetaHumans/MH_Friend/BP_MH_Friend.BP_MH_Friend
[2025.05.27-06.29.44:301][753]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.05.27-06.29.44:572][753]LogStreaming: Display: FlushAsyncLoading(519): 1 QueuedPackages, 0 AsyncPackages
[2025.05.27-06.29.46:329][753]LogSlate: Took 0.015455 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-BoldCondensed.ttf' (158K)
[2025.05.27-06.29.47:504][769]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.29.48:679][785]LogPSOHitching: Encountered 50 PSO creation hitches so far (31 graphics, 19 compute). 0 of them were precached.
[2025.05.27-06.29.52:194][837]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.05.27-06.29.52:601][844]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.05.27-06.29.53:009][851]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.05.27-06.29.57:651][895]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.30.05:761][978]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.05.27-06.30.07:705][ 12]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.30.08:108][ 20]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.05.27-06.30.10:087][ 55]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.05.27-06.30.15:935][160]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.05.27-06.30.17:759][191]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.30.22:324][271]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.05.27-06.30.23:969][300]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.05.27-06.30.25:959][335]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.05.27-06.30.27:767][365]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.30.31:105][422]LogSlate: Window 'BP_MH_Friend' being destroyed
[2025.05.27-06.30.31:233][422]LogSlate: Window 'BP_MH_Friend' being destroyed
[2025.05.27-06.30.31:260][422]LogWorld: UWorld::CleanupWorld for World_8, bSessionEnded=true, bCleanupResources=true
[2025.05.27-06.30.31:261][422]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.27-06.30.31:460][422]LogUObjectHash: Compacting FUObjectHashTables data took   1.20ms
[2025.05.27-06.30.37:938][613]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.30.47:939][643]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.30.57:940][673]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.31.07:940][703]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.31.17:941][733]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.31.27:942][763]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.31.37:944][793]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.31.47:944][823]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.31.57:944][853]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.32.07:946][883]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.32.17:947][913]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.32.27:949][943]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.32.37:948][973]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.32.47:951][  3]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.32.57:952][ 33]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.33.07:953][ 63]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.33.17:955][ 93]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.33.27:954][123]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.33.37:957][153]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.33.47:959][183]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.33.57:958][213]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.34.07:962][243]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.34.17:963][273]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.34.27:963][303]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.34.37:964][333]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.34.47:964][363]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.34.57:965][393]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.35.07:967][423]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.35.17:967][453]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.35.27:301][481]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1356.625366
[2025.05.27-06.35.27:967][483]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.35.28:301][484]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-06.35.28:301][484]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1357.291626, Update Interval: 335.554688
[2025.05.27-06.35.37:968][513]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.35.47:968][543]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.35.57:968][573]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.36.07:970][603]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.36.17:970][633]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.36.27:971][663]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.36.37:971][693]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.36.47:973][723]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.36.57:973][753]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.37.07:973][783]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.37.17:974][813]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.37.27:974][843]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.37.37:974][873]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.37.47:975][903]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.37.57:976][933]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.38.07:976][963]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.38.17:977][993]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.38.27:978][ 23]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.38.37:979][ 53]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.38.47:979][ 83]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.38.57:979][113]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.39.07:980][143]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.39.17:980][173]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.39.27:981][203]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.39.37:981][233]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.39.47:982][263]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.39.57:983][293]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.40.07:982][323]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.40.17:984][353]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.40.27:985][383]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.40.37:985][413]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.40.47:985][443]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.40.57:985][473]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.41.07:986][503]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.41.17:985][533]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.41.27:987][563]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.41.29:321][567]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1718.643799
[2025.05.27-06.41.30:321][570]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-06.41.30:321][570]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1719.310547, Update Interval: 336.367676
[2025.05.27-06.41.37:987][593]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.41.47:988][623]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.41.57:988][653]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.42.07:990][683]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.42.17:989][713]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.42.27:990][743]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.42.37:991][773]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.42.47:992][803]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.42.57:992][833]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.43.07:993][863]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.43.17:993][893]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.43.27:994][923]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.43.37:994][953]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.43.47:993][983]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.43.57:995][ 13]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.44.07:995][ 43]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.44.17:996][ 73]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.44.27:997][103]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.44.37:998][133]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.44.47:999][163]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.44.57:999][193]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.45.08:000][223]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.45.17:999][253]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.45.28:000][283]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.45.38:000][313]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.45.48:001][343]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.45.58:002][373]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.46.08:003][403]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.46.18:002][433]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.46.28:004][463]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.46.38:005][493]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.46.48:006][523]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.46.58:008][553]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.47.08:008][583]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.47.18:008][613]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.47.28:010][643]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.47.38:011][673]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.47.48:011][703]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.47.58:012][733]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.48.05:681][756]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2115.005127
[2025.05.27-06.48.06:680][759]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-06.48.06:680][759]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2115.671631, Update Interval: 338.158508
[2025.05.27-06.48.08:014][763]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.48.18:015][793]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.48.28:015][823]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.48.38:016][853]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.48.48:017][883]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.48.58:019][913]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.49.08:019][943]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.49.18:019][973]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.49.28:019][  3]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.49.38:020][ 33]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.49.48:020][ 63]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.49.58:020][ 93]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.50.08:023][123]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.50.18:025][153]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.50.28:026][183]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.50.38:025][213]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.50.48:025][243]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.50.58:029][273]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.51.08:029][303]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.51.18:030][333]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.51.28:031][363]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.51.38:029][393]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.51.48:031][423]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.51.58:032][453]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.52.08:032][483]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.52.18:033][513]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.52.28:035][543]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.52.38:035][573]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.52.48:036][603]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.52.58:037][633]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.53.08:037][663]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.53.18:039][693]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.53.28:039][723]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.53.38:039][753]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.53.48:039][783]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.53.58:041][813]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.54.08:041][843]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.54.18:042][873]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.54.28:043][903]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.54.29:709][908]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2499.031982
[2025.05.27-06.54.30:709][911]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-06.54.30:709][911]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2499.699219, Update Interval: 328.171631
[2025.05.27-06.54.38:044][933]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.54.48:045][963]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.54.58:047][993]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.55.08:047][ 23]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.55.18:047][ 53]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.55.28:049][ 83]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.55.38:050][113]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.55.48:050][143]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.55.58:052][173]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.56.08:054][203]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.56.18:055][233]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.56.28:055][263]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.56.38:057][293]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.56.48:058][323]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.56.58:059][353]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.57.08:062][383]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.57.18:062][413]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.57.28:063][443]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.57.38:063][473]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.57.48:063][503]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.57.58:064][533]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.58.08:065][563]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.58.18:066][593]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.58.28:069][623]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.58.38:070][653]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.58.48:072][683]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.58.58:072][713]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.59.08:072][743]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.59.18:073][773]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.59.28:073][803]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.59.38:074][833]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.59.48:073][863]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-06.59.58:075][893]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.00.08:076][923]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.00.18:077][953]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.00.28:078][983]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.00.38:080][ 13]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.00.40:080][ 19]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2869.401123
[2025.05.27-07.00.41:079][ 22]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.27-07.00.41:079][ 22]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2870.067871, Update Interval: 336.395142
[2025.05.27-07.00.48:080][ 43]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.00.58:081][ 73]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.01.08:081][103]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.01.18:083][133]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.01.28:082][163]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.01.38:083][193]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.01.48:082][223]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.01.58:082][253]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.02.08:085][283]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.02.18:086][313]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.02.28:087][343]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.02.38:086][373]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.02.48:088][403]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.02.58:089][433]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.03.08:089][463]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.03.18:091][493]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.03.28:094][523]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.03.38:094][553]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.03.48:096][583]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.03.58:095][613]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.04.08:095][643]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.04.18:096][673]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.04.28:097][703]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.04.38:096][733]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.04.48:096][763]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.04.58:096][793]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.05.08:098][823]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.05.18:100][853]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.05.28:101][883]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.05.38:101][913]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.05.48:102][943]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.05.58:102][973]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.06.08:104][  3]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.06.18:104][ 33]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.06.28:106][ 63]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.27-07.06.38:106][ 93]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
