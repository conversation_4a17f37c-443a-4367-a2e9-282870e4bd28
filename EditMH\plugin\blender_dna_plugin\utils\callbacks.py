"""
Callback functions for the Blender MetaHuman DNA Plugin.

This module provides callback functions for UI elements and property updates.
"""

import os
import bpy
import json
from pathlib import Path

# Import constants
from ..constants import POSES_FOLDER

# Import logging functions
try:
    # Try the direct import first (for development)
    from .logging_utils import log_info, log_error, log_warning, log_debug
except ImportError:
    # Fall back to the full package path (for installed addon)
    from blender_metahuman_dna.blender_dna_plugin.utils.logging_utils import log_info, log_error, log_warning, log_debug

# Global variable to store preview collections
preview_collections = {}

def get_active_rig_logic():
    """
    Gets the active rig logic instance.

    Returns:
        The active DNA tools properties or None if not available
    """
    try:
        dna_tools = bpy.context.scene.dna_tools
        if dna_tools.is_dna_loaded:
            return dna_tools
    except AttributeError:
        pass
    return None

def deselect_all():
    """
    Deselects all objects in the scene.
    """
    for scene_object in bpy.data.objects:
        scene_object.select_set(False)

def get_lod_index(name: str) -> int:
    """
    Gets the LOD index from the given name.

    Args:
        name (str): A name of an object.

    Returns:
        int: The LOD index. Or -1 if no LOD index was found.
    """
    import re
    # LOD regex pattern to match LOD naming conventions
    LOD_REGEX = r'.*[_\.]lod(\d+).*'
    result = re.search(LOD_REGEX, name, re.IGNORECASE)
    if result:
        lod = result.groups()[-1]
        return int(lod)
    return -1

def get_face_pose_previews_items(self, context):
    """Get the available pose thumbnails as enum items.

    This function is used as a callback for the face_pose_previews EnumProperty.
    It loads thumbnails from the poses folder and returns them as enum items.

    Args:
        self: The property owner
        context: Blender context

    Returns:
        List of enum items (identifier, name, description, icon_id, index)
    """
    enum_items = []

    if context is None:
        return enum_items

    # Get the poses folder path
    directory = POSES_FOLDER / 'face'

    # Get the preview collection
    preview_collection = preview_collections.get("face_poses")
    if not preview_collection:
        log_warning("No preview collection available")
        # Create the preview collection if it doesn't exist
        import bpy.utils.previews
        preview_collection = bpy.utils.previews.new()
        preview_collections["face_poses"] = preview_collection
        log_info("Created new preview collection for face poses")

    # If the enum items have already been cached, return them
    if hasattr(preview_collection, 'face_pose_previews') and preview_collection.face_pose_previews:
        return preview_collection.face_pose_previews

    if directory.exists():
        image_paths = []

        # Find all thumbnail-preview.png files that have a corresponding pose.json
        for folder_path, _, file_names in os.walk(directory):
            for file_name in file_names:
                if file_name == 'thumbnail-preview.png':
                    thumbnail_file_path = Path(folder_path, file_name)
                    pose_file_path = Path(folder_path, 'pose.json')
                    if pose_file_path.exists() and thumbnail_file_path.exists():
                        image_paths.append(Path(folder_path, file_name))

        # Create enum items for each thumbnail
        for i, file_path in enumerate(image_paths):
            name = file_path.parent.name
            # Generate a thumbnail preview for the file
            icon = preview_collection.get(name)
            if not icon:
                try:
                    thumb = preview_collection.load(name, str(file_path), 'IMAGE')
                    log_info(f"Loaded thumbnail for {name}: {file_path}")
                except Exception as e:
                    log_error(f"Error loading thumbnail for {name}: {e}")
                    # Create a default icon if loading fails
                    try:
                        # Use a default icon from Blender
                        enum_items.append((str(file_path), name, "", 0, i))
                        log_info(f"Using default icon for {name}")
                        continue
                    except Exception as e2:
                        log_error(f"Error creating default icon for {name}: {e2}")
                        continue
            else:
                thumb = preview_collection[name]

            # Add the enum item with the thumbnail
            enum_items.append((str(file_path), name, "", thumb.icon_id, i))
            log_info(f"Added enum item for {name} with icon ID {thumb.icon_id}")

        # Sort items alphabetically by name
        enum_items.sort(key=lambda x: x[1])

        # Log the number of enum items
        log_info(f"Created {len(enum_items)} enum items for face pose previews")

    # Cache the enum items
    preview_collection.face_pose_previews = enum_items

    return enum_items

def update_face_pose(self, context):
    """Update the UI when a pose is selected.

    This function is called when the face_pose_previews property is changed.
    It only updates the UI to show the selected pose, but does not apply the pose
    or send any packets. The pose will be applied when the user clicks the "Apply Selected Pose" button.

    Args:
        self: The property owner
        context: Blender context
    """
    # Get the selected pose file path
    thumbnail_file = Path(self.face_pose_previews)
    json_file_path = thumbnail_file.parent / 'pose.json'

    if not json_file_path.exists():
        log_warning(f"Pose file not found: {json_file_path}")
        return

    # Just log that a pose was selected, but don't apply it yet
    pose_name = thumbnail_file.parent.name
    log_info(f"Selected pose: {pose_name} (will be applied when 'Apply Selected Pose' is clicked)")

    # Store the selected pose path for later use
    # This is just a UI update, no packets are sent
    context.window_manager.dna_tools.selected_pose_path = str(json_file_path)

# Register and unregister functions
def register():
    """Register the callbacks module"""
    global preview_collections

    # Create the preview collection for face poses
    import bpy.utils.previews
    face_pose_previews_collection = bpy.utils.previews.new()
    face_pose_previews_collection.face_pose_previews_root_folder = ""
    face_pose_previews_collection.face_pose_previews = []
    preview_collections["face_poses"] = face_pose_previews_collection

    # Load thumbnails for all poses
    try:
        # Get the poses folder path
        directory = POSES_FOLDER / 'face'
        if directory.exists():
            log_info(f"Loading thumbnails from {directory}")

            # Find all thumbnail-preview.png files
            image_paths = []
            for folder_path, _, file_names in os.walk(directory):
                for file_name in file_names:
                    if file_name == 'thumbnail-preview.png':
                        thumbnail_file_path = Path(folder_path, file_name)
                        pose_file_path = Path(folder_path, 'pose.json')
                        if pose_file_path.exists() and thumbnail_file_path.exists():
                            image_paths.append(Path(folder_path, file_name))

            log_info(f"Found {len(image_paths)} thumbnails")

            # Load thumbnails into the preview collection
            for file_path in image_paths:
                name = file_path.parent.name
                try:
                    face_pose_previews_collection.load(name, str(file_path), 'IMAGE')
                    log_info(f"Loaded thumbnail for {name}: {file_path}")
                except Exception as e:
                    log_error(f"Error loading thumbnail for {name}: {e}")
    except Exception as e:
        log_error(f"Error loading thumbnails: {e}")

    log_info("Registered callbacks module")

def unregister():
    """Unregister the callbacks module"""
    global preview_collections

    # Remove preview collections
    for pcoll in preview_collections.values():
        bpy.utils.previews.remove(pcoll)
    preview_collections.clear()

    log_info("Unregistered callbacks module")
