﻿Log file open, 05/26/25 15:42:11
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=29024)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: BlenderLinkProject
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\Plugins\BlenderLinkProject\BlenderLinkProject.uproject""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 14.291506
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-B13C58784CA0671717BE87920FA7A11C
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/Plugins/BlenderLinkProject/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogConfig: Display: Loading VulkanPC ini files took 0.03 seconds
LogConfig: Display: Loading Unix ini files took 0.03 seconds
LogConfig: Display: Loading Windows ini files took 0.03 seconds
LogConfig: Display: Loading Android ini files took 0.04 seconds
LogConfig: Display: Loading Linux ini files took 0.05 seconds
LogConfig: Display: Loading IOS ini files took 0.05 seconds
LogConfig: Display: Loading VisionOS ini files took 0.05 seconds
LogConfig: Display: Loading TVOS ini files took 0.05 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.07 seconds
LogConfig: Display: Loading Mac ini files took 0.14 seconds
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogAssetRegistry: Display: Asset registry cache read as 43.8 MiB from H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin Mutable
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaIOFramework
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin SkeletalMerging
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin XRBase
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin CaptureData
LogPluginManager: Mounting Engine plugin LensComponent
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OpenColorIO
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LiveLinkControlRig
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RigLogicMutable
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin MetaHuman
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin ARUtilities
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin AppleARKit
LogPluginManager: Mounting Engine plugin AppleARKitFaceSupport
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Project plugin BlenderLink
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum ********** concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: AMD Radeon RX 6900 XT
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: 
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.55ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.11ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalMerging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XRBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CaptureData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.11ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LiveLinkControlRig' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RigLogicMutable' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MetaHuman' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ARUtilities' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleARKit' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleARKitFaceSupport' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlenderLink' had been unloaded. Reloading on-demand took 0.06ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.05.26-10.12.21:270][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.05.26-10.12.21:270][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.05.26-10.12.21:275][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.05.26-10.12.21:275][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.05.26-10.12.21:275][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.05.26-10.12.21:275][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.05.26-10.12.21:275][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.05.26-10.12.21:275][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.05.26-10.12.21:275][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.05.26-10.12.21:275][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.05.26-10.12.21:275][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.05.26-10.12.21:275][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.05.26-10.12.21:275][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.05.26-10.12.21:296][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.05.26-10.12.21:296][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.05.26-10.12.21:296][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.05.26-10.12.21:296][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.05.26-10.12.21:297][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.05.26-10.12.21:297][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.05.26-10.12.21:297][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.05.26-10.12.21:373][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.05.26-10.12.21:373][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.05.26-10.12.21:373][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.05.26-10.12.21:373][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.05.26-10.12.21:375][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.05.26-10.12.21:375][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.05.26-10.12.21:375][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.05.26-10.12.21:375][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.05.26-10.12.21:376][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.05.26-10.12.21:430][  0]LogRHI: Using Default RHI: D3D12
[2025.05.26-10.12.21:430][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.26-10.12.21:430][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.26-10.12.21:487][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.05.26-10.12.21:487][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.26-10.12.21:736][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.05.26-10.12.21:736][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.26-10.12.21:736][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 3 output[s]
[2025.05.26-10.12.21:757][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.26-10.12.21:757][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.05.26-10.12.21:776][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.05.26-10.12.21:776][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.05.26-10.12.21:776][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.26-10.12.21:777][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.05.26-10.12.21:777][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.05.26-10.12.21:777][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.26-10.12.21:777][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.05.26-10.12.21:792][  0]LogHAL: Display: Platform has ~ 64 GB [68631805952 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.05.26-10.12.21:801][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.05.26-10.12.21:812][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.26-10.12.21:819][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.05.26-10.12.21:819][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.05.26-10.12.21:819][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.26-10.12.21:841][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.05.26-10.12.21:841][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.05.26-10.12.21:841][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.05.26-10.12.21:841][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.05.26-10.12.21:841][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.05.26-10.12.21:841][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.26-10.12.21:841][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.05.26-10.12.21:841][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.05.26-10.12.21:841][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/Plugins/BlenderLinkProject/Saved/Config/WindowsEditor/Editor.ini]
[2025.05.26-10.12.21:863][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.05.26-10.12.21:863][  0]LogInit: User: Shashank
[2025.05.26-10.12.21:863][  0]LogInit: CPU Page size=4096, Cores=16
[2025.05.26-10.12.21:863][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.05.26-10.12.22:149][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.05.26-10.12.22:149][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.05.26-10.12.22:149][  0]LogMemory: Process Physical Memory: 620.34 MB used, 641.67 MB peak
[2025.05.26-10.12.22:149][  0]LogMemory: Process Virtual Memory: 701.68 MB used, 701.68 MB peak
[2025.05.26-10.12.22:149][  0]LogMemory: Physical Memory: 22875.05 MB used,  42577.34 MB free, 65452.39 MB total
[2025.05.26-10.12.22:149][  0]LogMemory: Virtual Memory: 30880.11 MB used,  38668.29 MB free, 69548.39 MB total
[2025.05.26-10.12.22:149][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.05.26-10.12.22:153][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.05.26-10.12.22:307][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.05.26-10.12.22:338][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.05.26-10.12.22:362][  0]LogInit: Using OS detected language (en-GB).
[2025.05.26-10.12.22:362][  0]LogInit: Using OS detected locale (en-IN).
[2025.05.26-10.12.22:622][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.05.26-10.12.22:622][  0]LogInit: Setting process to per monitor DPI aware
[2025.05.26-10.12.23:357][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.05.26-10.12.23:357][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.05.26-10.12.23:357][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.05.26-10.12.23:717][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.05.26-10.12.23:717][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.05.26-10.12.25:345][  0]LogRHI: Using Default RHI: D3D12
[2025.05.26-10.12.25:345][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.26-10.12.25:345][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.26-10.12.25:345][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.26-10.12.25:345][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.26-10.12.25:345][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.05.26-10.12.25:358][  0]LogWindows: Attached monitors:
[2025.05.26-10.12.25:358][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.05.26-10.12.25:358][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY2' [PRIMARY]
[2025.05.26-10.12.25:358][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY3'
[2025.05.26-10.12.25:358][  0]LogWindows: Found 3 attached monitors.
[2025.05.26-10.12.25:358][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.26-10.12.25:359][  0]LogRHI: RHI Adapter Info:
[2025.05.26-10.12.25:359][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.05.26-10.12.25:359][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.26-10.12.25:359][  0]LogRHI:      Driver Date: 4-25-2025
[2025.05.26-10.12.25:359][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.05.26-10.12.25:406][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.05.26-10.12.25:564][  0]LogNvidiaAftermath: Aftermath crash dumping failed to initialize (bad0000c).
[2025.05.26-10.12.25:564][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.05.26-10.12.25:835][  0]LogNvidiaAftermath: Aftermath is not loaded.
[2025.05.26-10.12.25:835][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.05.26-10.12.25:835][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.05.26-10.12.25:835][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.05.26-10.12.25:835][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.05.26-10.12.25:835][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.05.26-10.12.25:835][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.05.26-10.12.25:835][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.05.26-10.12.25:836][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.05.26-10.12.25:836][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.05.26-10.12.25:836][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.05.26-10.12.25:836][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.05.26-10.12.25:836][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.05.26-10.12.25:836][  0]LogD3D12RHI: Bindless resources are supported
[2025.05.26-10.12.25:836][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.05.26-10.12.25:836][  0]LogD3D12RHI: Raster order views are supported
[2025.05.26-10.12.25:836][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.05.26-10.12.25:836][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.05.26-10.12.25:836][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.05.26-10.12.25:836][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.05.26-10.12.25:836][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.05.26-10.12.25:836][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.05.26-10.12.25:836][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.05.26-10.12.25:958][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000914B0D25300)
[2025.05.26-10.12.25:959][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000914B0D25580)
[2025.05.26-10.12.25:959][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000914B0D25800)
[2025.05.26-10.12.25:959][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.05.26-10.12.25:959][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.05.26-10.12.25:959][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.05.26-10.12.25:959][  0]LogRHI: Texture pool is 9808 MB (70% of 14012 MB)
[2025.05.26-10.12.25:959][  0]LogD3D12RHI: Async texture creation enabled
[2025.05.26-10.12.25:959][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.05.26-10.12.26:348][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.05.26-10.12.26:679][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.05.26-10.12.26:755][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all'
[2025.05.26-10.12.26:755][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all" ]
[2025.05.26-10.12.27:008][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.05.26-10.12.27:008][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.05.26-10.12.27:008][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.05.26-10.12.27:008][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.05.26-10.12.27:008][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.05.26-10.12.27:008][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.05.26-10.12.27:008][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.05.26-10.12.27:043][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.05.26-10.12.27:052][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.05.26-10.12.28:354][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.05.26-10.12.28:354][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.05.26-10.12.28:354][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.05.26-10.12.28:355][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.05.26-10.12.28:355][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.05.26-10.12.28:355][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.05.26-10.12.28:355][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.05.26-10.12.28:355][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.05.26-10.12.28:355][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.05.26-10.12.28:355][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.05.26-10.12.28:520][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.05.26-10.12.28:520][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.05.26-10.12.28:684][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.05.26-10.12.28:684][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.05.26-10.12.28:684][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.05.26-10.12.28:684][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.05.26-10.12.28:783][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.05.26-10.12.28:783][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.05.26-10.12.28:783][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.05.26-10.12.28:949][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.05.26-10.12.28:949][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.05.26-10.12.28:949][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.05.26-10.12.28:949][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.05.26-10.12.29:118][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.05.26-10.12.29:118][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.05.26-10.12.29:348][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.05.26-10.12.29:348][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.05.26-10.12.29:348][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.05.26-10.12.29:348][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.05.26-10.12.29:348][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.05.26-10.12.31:395][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   SF_METAL
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   VVM_1_0
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.05.26-10.12.31:428][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.05.26-10.12.31:464][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.05.26-10.12.31:464][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.05.26-10.12.31:567][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.05.26-10.12.31:567][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.05.26-10.12.31:567][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.05.26-10.12.31:567][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.26-10.12.31:567][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.05.26-10.12.31:770][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.05.26-10.12.31:770][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.26-10.12.31:770][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.05.26-10.12.31:835][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.05.26-10.12.31:850][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.26-10.12.31:851][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.26-10.12.31:894][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.05.26-10.12.31:894][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 28880 --child-id Zen_28880_Startup'
[2025.05.26-10.12.32:122][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.05.26-10.12.32:122][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.287 seconds
[2025.05.26-10.12.32:240][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.05.26-10.12.32:262][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.05.26-10.12.32:288][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.09ms. RandomReadSpeed=229.37MBs, RandomWriteSpeed=430.77MBs. Assigned SpeedClass 'Local'
[2025.05.26-10.12.32:288][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.05.26-10.12.32:288][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.05.26-10.12.32:288][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.05.26-10.12.32:288][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.05.26-10.12.32:288][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.05.26-10.12.32:288][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.05.26-10.12.32:288][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.05.26-10.12.32:377][  0]LogShaderCompilers: Guid format shader working directory is 19 characters bigger than the processId version (H:/Plugins/BlenderLinkProject/Intermediate/Shaders/WorkingDirectory/28880/).
[2025.05.26-10.12.32:377][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/CBDAD8824C2DED92B213E9B6124140C0/'.
[2025.05.26-10.12.32:387][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.05.26-10.12.32:387][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.05.26-10.12.32:481][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/Plugins/BlenderLinkProject/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.05.26-10.12.32:481][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.05.26-10.12.45:765][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.05.26-10.12.48:964][  0]LogSlate: Using FreeType 2.10.0
[2025.05.26-10.12.49:072][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.05.26-10.12.49:094][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.26-10.12.49:094][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.26-10.12.49:435][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.26-10.12.49:435][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.26-10.12.49:435][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.26-10.12.49:435][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.26-10.12.49:921][  0]LogAssetRegistry: FAssetRegistry took 0.0059 seconds to start up
[2025.05.26-10.12.50:179][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin.
[2025.05.26-10.12.50:223][  0]LogAssetRegistry: Error: Package is unloadable: H:/Plugins/BlenderLinkProject/Content/SM_Rock_ukxmdhuga.uasset. Reason: Invalid value for PACKAGE_FILE_TAG at start of file.
[2025.05.26-10.12.50:864][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.05.26-10.12.56:057][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.05.26-10.12.56:401][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.05.26-10.12.56:401][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.05.26-10.12.56:401][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.05.26-10.12.56:411][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.05.26-10.12.56:411][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.05.26-10.12.56:453][  0]LogDeviceProfileManager: Active device profile: [00000914CE0D8000][00000914CBFC0000 66] WindowsEditor
[2025.05.26-10.12.56:453][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.05.26-10.12.56:638][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.05.26-10.12.56:665][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.05.26-10.12.56:665][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.05.26-10.12.57:655][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.05.26-10.12.58:686][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:742][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.05.26-10.12.58:742][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.26-10.12.58:742][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:742][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.05.26-10.12.58:742][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.26-10.12.58:742][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:743][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.05.26-10.12.58:743][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.26-10.12.58:743][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:743][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.05.26-10.12.58:743][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.26-10.12.58:749][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:773][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:773][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.05.26-10.12.58:773][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.26-10.12.58:773][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:773][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.05.26-10.12.58:773][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.26-10.12.58:774][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:790][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:790][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.05.26-10.12.58:790][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.26-10.12.58:790][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:790][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.05.26-10.12.58:790][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.26-10.12.58:790][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:816][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.26-10.12.58:816][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:816][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.05.26-10.12.58:816][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.26-10.12.58:816][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:816][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.05.26-10.12.58:816][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.26-10.12.58:816][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.26-10.12.58:817][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.05.26-10.12.58:817][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.26-10.12.58:817][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:817][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.05.26-10.12.58:817][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.26-10.12.58:817][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.05.26-10.12.58:817][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.26-10.12.58:817][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:817][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.05.26-10.12.58:817][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.26-10.12.58:818][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.05.26-10.12.58:818][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.26-10.12.58:818][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.26-10.12.58:818][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.05.26-10.12.58:818][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.26-10.13.00:157][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.05.26-10.13.00:157][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.05.26-10.13.00:157][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.05.26-10.13.00:157][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.05.26-10.13.00:157][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.05.26-10.13.04:861][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.58ms
[2025.05.26-10.13.05:522][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.42ms
[2025.05.26-10.13.06:195][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.42ms
[2025.05.26-10.13.06:195][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.41ms
[2025.05.26-10.13.11:163][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.05.26-10.13.11:163][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.05.26-10.13.11:261][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.05.26-10.13.11:261][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.05.26-10.13.11:262][  0]LogLiveCoding: Display: First instance in process group "UE_BlenderLinkProject_0xe99fe6f9", spawning console
[2025.05.26-10.13.11:324][  0]LogLiveCoding: Display: Waiting for server
[2025.05.26-10.13.11:961][  0]LogSlate: Border
[2025.05.26-10.13.11:961][  0]LogSlate: BreadcrumbButton
[2025.05.26-10.13.11:961][  0]LogSlate: Brushes.Title
[2025.05.26-10.13.11:961][  0]LogSlate: Default
[2025.05.26-10.13.11:961][  0]LogSlate: Icons.Save
[2025.05.26-10.13.11:961][  0]LogSlate: Icons.Toolbar.Settings
[2025.05.26-10.13.11:961][  0]LogSlate: ListView
[2025.05.26-10.13.11:961][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.05.26-10.13.11:961][  0]LogSlate: SoftwareCursor_Grab
[2025.05.26-10.13.11:961][  0]LogSlate: TableView.DarkRow
[2025.05.26-10.13.11:961][  0]LogSlate: TableView.Row
[2025.05.26-10.13.11:961][  0]LogSlate: TreeView
[2025.05.26-10.13.18:430][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.05.26-10.13.18:728][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 296.968 ms
[2025.05.26-10.13.19:296][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.41ms
[2025.05.26-10.13.19:964][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.05.26-10.13.19:964][  0]LogInit: XR: MultiViewport is Disabled
[2025.05.26-10.13.19:964][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.05.26-10.13.19:965][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.05.26-10.13.21:778][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.05.26-10.13.22:125][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.05.26-10.13.22:156][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.05.26-10.13.22:157][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.05.26-10.13.22:169][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:61815'.
[2025.05.26-10.13.22:172][  0]LogUdpMessaging: Display: Added local interface '172.25.48.1' to multicast group '230.0.0.1:6666'
[2025.05.26-10.13.22:172][  0]LogUdpMessaging: Display: Added local interface '172.18.32.1' to multicast group '230.0.0.1:6666'
[2025.05.26-10.13.22:172][  0]LogUdpMessaging: Display: Added local interface '192.168.31.37' to multicast group '230.0.0.1:6666'
[2025.05.26-10.13.22:704][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.05.26-10.13.22:704][  0]LogNNERuntimeORT: 0: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.05.26-10.13.22:704][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.05.26-10.13.22:704][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.05.26-10.13.23:663][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.05.26-10.13.23:678][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.05.26-10.13.23:764][  0]LogMetaSound: MetaSound Engine Initialized
[2025.05.26-10.13.26:825][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.26-10.13.26:825][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.26-10.13.27:514][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.48ms
[2025.05.26-10.13.29:084][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: BD254608719E49728000000000002900 | Instance: 9A8F4B534307066B3DE02E8D99CA8F09 (DESKTOP-E41IK6R-28880).
[2025.05.26-10.13.36:186][  0]LogMutable: Creating Mutable Customizable Object System.
[2025.05.26-10.13.38:051][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.05.26-10.13.38:160][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.05.26-10.13.38:228][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.26-10.13.38:228][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.26-10.13.39:214][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.05.26-10.13.39:214][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.05.26-10.13.39:226][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.05.26-10.13.39:232][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.05.26-10.13.39:237][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.05.26-10.13.39:249][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.05.26-10.13.39:261][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.05.26-10.13.39:261][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.05.26-10.13.39:261][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.05.26-10.13.39:261][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.05.26-10.13.39:261][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.05.26-10.13.39:275][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.05.26-10.13.39:287][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.05.26-10.13.39:299][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.05.26-10.13.39:299][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.05.26-10.13.39:304][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.05.26-10.13.39:311][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.05.26-10.13.39:320][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.05.26-10.13.39:329][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.05.26-10.13.39:342][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.05.26-10.13.39:347][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.05.26-10.13.39:347][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.05.26-10.13.39:348][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.05.26-10.13.39:358][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.05.26-10.13.39:359][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.05.26-10.13.39:369][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.05.26-10.13.39:370][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.05.26-10.13.39:379][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.05.26-10.13.39:389][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.05.26-10.13.39:404][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.05.26-10.13.39:410][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.05.26-10.13.39:410][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.05.26-10.13.39:420][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.05.26-10.13.39:428][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.05.26-10.13.39:429][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.05.26-10.13.41:021][  0]SourceControl: Revision control is disabled
[2025.05.26-10.13.41:223][  0]SourceControl: Revision control is disabled
[2025.05.26-10.13.41:480][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.45ms
[2025.05.26-10.13.41:603][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.41ms
[2025.05.26-10.13.45:783][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.26-10.13.45:783][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.26-10.13.46:741][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.05.26-10.13.50:321][  0]LogSkeletalMesh: Building Skeletal Mesh Face_Archetype...
[2025.05.26-10.13.50:778][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (Face_Archetype) ...
[2025.05.26-10.13.57:688][  0]LogSkeletalMesh: Skeletal mesh [/MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.26-10.13.57:701][  0]LogSkeletalMesh: Built Skeletal Mesh [7.40s] /MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype
[2025.05.26-10.13.58:007][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.26-10.13.58:007][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.26-10.13.58:007][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.26-10.13.58:008][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.26-10.13.58:008][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.26-10.13.58:008][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.26-10.13.58:013][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.26-10.13.58:013][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.26-10.13.58:440][  0]LogCollectionManager: Loaded 0 collections in 0.001125 seconds
[2025.05.26-10.13.58:444][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Saved/Collections/' took 0.00s
[2025.05.26-10.13.58:465][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Developers/Shashank/Collections/' took 0.02s
[2025.05.26-10.13.58:468][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Collections/' took 0.00s
[2025.05.26-10.13.58:914][  0]LogBlenderLink: Initializing BlenderLink socket listener
[2025.05.26-10.13.58:915][  0]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.26-10.13.58:915][  0]LogBlenderLink: Set socket buffer sizes to 524288 bytes
[2025.05.26-10.13.58:915][  0]LogBlenderLink: Binding socket to 127.0.0.1:2907
[2025.05.26-10.13.58:915][  0]LogBlenderLink: Warning: Socket bind success: true
[2025.05.26-10.13.58:915][  0]LogBlenderLink: Warning: Socket listen success: true
[2025.05.26-10.13.58:915][  0]LogBlenderLink: Waiting for client connection...
[2025.05.26-10.13.59:024][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.26-10.13.59:024][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.26-10.13.59:024][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.26-10.13.59:024][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.26-10.13.59:024][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.26-10.13.59:024][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.26-10.13.59:030][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.26-10.13.59:030][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.26-10.13.59:138][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-05-26T10:13:59.138Z using C
[2025.05.26-10.13.59:149][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=BlenderLinkProject, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.05.26-10.13.59:192][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.26-10.13.59:196][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.05.26-10.13.59:273][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.05.26-10.13.59:317][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.05.26-10.13.59:317][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.05.26-10.13.59:325][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.008823
[2025.05.26-10.13.59:335][  0]LogFab: Display: Logging in using persist
[2025.05.26-10.13.59:336][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.05.26-10.13.59:648][  0]LogUObjectArray: 52283 objects as part of root set at end of initial load.
[2025.05.26-10.13.59:648][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.05.26-10.13.59:659][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 38255 public script object entries (1030.17 KB)
[2025.05.26-10.13.59:659][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.05.26-10.14.00:303][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/OnlineSubsystemUtils.OnlineProxyStoreOffer. Time(ms): 11.8
[2025.05.26-10.14.00:311][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/JsonUtilities.JsonObjectWrapper. Time(ms): 8.0
[2025.05.26-10.14.00:321][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AudioExtensions.AudioParameter. Time(ms): 9.5
[2025.05.26-10.14.00:331][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/IrisCore.InstancedStructNetSerializerConfig. Time(ms): 9.8
[2025.05.26-10.14.00:341][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ToolWidgets.SidebarDrawerState. Time(ms): 9.8
[2025.05.26-10.14.00:345][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ToolWidgets.ActionButtonStyle. Time(ms): 4.0
[2025.05.26-10.14.00:365][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MaterialEditor.SortedParamData. Time(ms): 19.5
[2025.05.26-10.14.00:384][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/GameplayTags.GameplayTagTableRow. Time(ms): 19.2
[2025.05.26-10.14.00:404][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AIModule.EnvQueryInstanceCache. Time(ms): 19.9
[2025.05.26-10.14.00:417][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AdvancedWidgets.ColorGradingSpinBoxStyle. Time(ms): 13.0
[2025.05.26-10.14.00:432][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AddContentDialog.FeatureAdditionalFiles. Time(ms): 14.4
[2025.05.26-10.14.00:439][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/GameProjectGeneration.TemplateReplacement. Time(ms): 7.2
[2025.05.26-10.14.00:459][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/SceneOutliner.SceneOutlinerConfig. Time(ms): 19.3
[2025.05.26-10.14.00:477][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/PropertyEditor.DetailsViewConfig. Time(ms): 19.0
[2025.05.26-10.14.00:487][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Kismet.BPGraphClipboardData. Time(ms): 8.3
[2025.05.26-10.14.00:495][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/EditorConfig.EditorConfigTestSimpleMap. Time(ms): 8.8
[2025.05.26-10.14.00:506][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/EditorWidgets.FilterBarSettings. Time(ms): 11.5
[2025.05.26-10.14.00:514][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AnimGraphRuntime.AnimNode_SkeletalControlBase. Time(ms): 7.4
[2025.05.26-10.14.00:520][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AnimGraphRuntime.AnimNode_BlendSpacePlayerBase. Time(ms): 5.9
[2025.05.26-10.14.00:525][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AnimGraphRuntime.AnimNode_RigidBody. Time(ms): 4.6
[2025.05.26-10.14.00:537][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MovieSceneTracks.MovieSceneCVarOverrides. Time(ms): 11.9
[2025.05.26-10.14.00:545][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MovieScene.MovieSceneSubSequenceData. Time(ms): 7.3
[2025.05.26-10.14.00:556][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MediaUtils.MediaPlayerOptions. Time(ms): 11.4
[2025.05.26-10.14.00:579][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.ISMComponentDescriptorBase. Time(ms): 22.0
[2025.05.26-10.14.00:588][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.GPUSpriteEmitterInfo. Time(ms): 9.5
[2025.05.26-10.14.00:591][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.CompositeSection. Time(ms): 2.1
[2025.05.26-10.14.00:597][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.PropertyAccessLibrary. Time(ms): 6.4
[2025.05.26-10.14.00:600][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.RootMotionSource. Time(ms): 2.6
[2025.05.26-10.14.00:608][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.PlayerMuteList. Time(ms): 8.4
[2025.05.26-10.14.00:616][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.AutoCompleteNode. Time(ms): 7.7
[2025.05.26-10.14.00:623][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.LevelCollection. Time(ms): 6.6
[2025.05.26-10.14.00:629][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.WorldPartitionDestructibleHLODState. Time(ms): 5.9
[2025.05.26-10.14.00:633][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.WorldPartitionUpdateStreamingCurrentState. Time(ms): 4.5
[2025.05.26-10.14.00:654][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AudioEditor.SoundCueGraphSchemaAction_NewNode. Time(ms): 20.0
[2025.05.26-10.14.00:665][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ChaosCaching.PerParticleCacheData. Time(ms): 11.3
[2025.05.26-10.14.00:676][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Niagara.NiagaraSystemUpdateContext. Time(ms): 10.2
[2025.05.26-10.14.00:684][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Niagara.NiagaraParameterStore. Time(ms): 8.8
[2025.05.26-10.14.00:694][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Niagara.NiagaraComponentPropertyBinding. Time(ms): 8.8
[2025.05.26-10.14.00:702][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Niagara.NiagaraOutlinerWorldData. Time(ms): 7.4
[2025.05.26-10.14.00:708][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Niagara.NiagaraUserRedirectionParameterStore. Time(ms): 6.4
[2025.05.26-10.14.00:715][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/TargetDeviceServices.TargetDeviceServicePong. Time(ms): 6.6
[2025.05.26-10.14.00:728][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Persona.PhysicsAssetRenderSettings. Time(ms): 13.0
[2025.05.26-10.14.00:742][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AutomationController.AutomationArtifact. Time(ms): 13.8
[2025.05.26-10.14.00:755][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/CustomizableObject.CustomizableObjectInstanceBakeOutput. Time(ms): 13.2
[2025.05.26-10.14.00:771][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/CustomizableObjectEditor.CustomizableObjectSchemaAction_NewNode. Time(ms): 15.0
[2025.05.26-10.14.00:786][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Serialization.StructSerializerObjectTestStruct. Time(ms): 14.7
[2025.05.26-10.14.00:787][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Serialization.StructSerializerLWCTypesTest. Time(ms): 1.1
[2025.05.26-10.14.00:799][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MetasoundFrontend.MetaSoundFrontendDocumentBuilder. Time(ms): 12.0
[2025.05.26-10.14.00:809][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AudioWidgets.AudioMeterDefaultColorStyle. Time(ms): 9.9
[2025.05.26-10.14.00:824][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MetasoundEditor.MetasoundEditorMemberPageDefaultStringArray. Time(ms): 13.8
[2025.05.26-10.14.00:833][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MetasoundEditor.MetasoundEditorGraphMemberNodeBreadcrumb. Time(ms): 9.4
[2025.05.26-10.14.00:843][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/RigVMDeveloper.RigVMInjectNodeIntoPinAction. Time(ms): 8.9
[2025.05.26-10.14.00:855][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/StateTreeModule.GameplayTagQueryCondition. Time(ms): 12.0
[2025.05.26-10.14.00:863][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/StateTreeEditorModule.StateTreeCompilerLog. Time(ms): 7.9
[2025.05.26-10.14.00:869][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/StateTreeTestSuite.StateTreeTest_PropertyStructA. Time(ms): 7.0
[2025.05.26-10.14.00:880][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Synthesis.Synth2DSliderStyle. Time(ms): 11.0
[2025.05.26-10.14.00:895][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ControlRig.ControlRigSettingsPerPinBool. Time(ms): 13.6
[2025.05.26-10.14.00:907][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/TemplateSequence.TemplateSectionPropertyScale. Time(ms): 11.6
[2025.05.26-10.14.00:917][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/TranslationEditor.TranslationContextInfo. Time(ms): 9.3
[2025.05.26-10.14.00:922][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/PythonScriptPlugin.PyTestStruct. Time(ms): 4.6
[2025.05.26-10.14.00:931][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/NiagaraEditor.NiagaraSchemaAction_NewNode. Time(ms): 9.6
[2025.05.26-10.14.00:938][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/NiagaraEditor.NiagaraClipboardCurveCollection. Time(ms): 6.6
[2025.05.26-10.14.00:944][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/NiagaraEditor.NiagaraGraphScriptUsageInfo. Time(ms): 6.6
[2025.05.26-10.14.00:954][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/NiagaraEditor.NiagaraPropagatedVariable. Time(ms): 9.4
[2025.05.26-10.14.00:963][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/NiagaraEditor.NiagaraScriptVariableData. Time(ms): 9.7
[2025.05.26-10.14.00:971][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/InterchangeCommonParser.InterchangeStepCurve. Time(ms): 7.4
[2025.05.26-10.14.00:984][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/VariantManager.CapturableProperty. Time(ms): 12.8
[2025.05.26-10.14.00:996][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ConcertTransport.ConcertMessageData. Time(ms): 12.1
[2025.05.26-10.14.01:009][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Concert.ConcertAdmin_GetSessionActivitiesResponse. Time(ms): 13.4
[2025.05.26-10.14.01:020][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ConcertClient.ConcertClientSettings. Time(ms): 10.7
[2025.05.26-10.14.01:029][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ConcertSyncCore.ConcertReplication_PutState_Request. Time(ms): 7.9
[2025.05.26-10.14.01:040][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/InterchangeTests.InterchangeTestFunctionResult. Time(ms): 11.4
[2025.05.26-10.14.01:049][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/BuildPatchServices.FileManifestData. Time(ms): 9.5
[2025.05.26-10.14.01:060][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/LiveLinkMessageBusFramework.LiveLinkSubjectFrameMessage. Time(ms): 10.0
[2025.05.26-10.14.01:069][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/LiveLinkMovieScene.MovieSceneLiveLinkSectionTemplate. Time(ms): 9.3
[2025.05.26-10.14.01:090][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/LiveLinkEditor.LiveLinkTestFrameDataInternal. Time(ms): 20.6
[2025.05.26-10.14.01:105][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/LevelSequenceEditor.MovieSceneBindingPropertyInfo. Time(ms): 15.6
[2025.05.26-10.14.01:107][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/LevelSequenceEditor.LevelSequenceTrackSettings. Time(ms): 1.3
[2025.05.26-10.14.01:119][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ObjectMixerEditor.ObjectMixerCollectionObjectSet. Time(ms): 12.4
[2025.05.26-10.14.01:124][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MeshModelingToolsExp.PhysicsBoxData. Time(ms): 4.5
[2025.05.26-10.14.01:130][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ControlRigEditor.AnimLayerSelectionSet. Time(ms): 6.1
[2025.05.26-10.14.01:136][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ControlRigEditor.AnimLayerItem. Time(ms): 6.2
[2025.05.26-10.14.01:139][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ControlRigEditor.AnimLayerState. Time(ms): 3.3
[2025.05.26-10.14.01:147][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/GeometryCollectionNodes.UniformVectorFieldDataflowNode. Time(ms): 8.1
[2025.05.26-10.14.01:157][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/GeometryCollectionNodes.AutoClusterDataflowNode. Time(ms): 9.2
[2025.05.26-10.14.01:179][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MetaHumanCaptureSource.MetaHumanTakeInfo. Time(ms): 20.5
[2025.05.26-10.14.01:522][  0]LogEngine: Initializing Engine...
[2025.05.26-10.14.01:847][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.05.26-10.14.01:892][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.009 s
[2025.05.26-10.14.02:611][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.05.26-10.14.03:057][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.05.26-10.14.03:898][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.05.26-10.14.03:898][  0]LogInit: Texture streaming: Enabled
[2025.05.26-10.14.03:997][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.05.26-10.14.04:069][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.05.26-10.14.04:144][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.05.26-10.14.04:150][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.05.26-10.14.04:150][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.05.26-10.14.04:169][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.05.26-10.14.04:170][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.05.26-10.14.04:170][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.05.26-10.14.04:170][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.05.26-10.14.04:191][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.05.26-10.14.04:191][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.05.26-10.14.04:191][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.05.26-10.14.04:191][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.05.26-10.14.04:191][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.05.26-10.14.04:207][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.05.26-10.14.04:207][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.05.26-10.14.04:207][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.05.26-10.14.04:246][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.05.26-10.14.04:293][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter Input (VB-Audio Voicemeeter VAIO)
[2025.05.26-10.14.04:307][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.05.26-10.14.04:383][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.05.26-10.14.04:383][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.05.26-10.14.04:418][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.05.26-10.14.04:418][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.05.26-10.14.04:423][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.05.26-10.14.04:423][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.05.26-10.14.04:453][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.05.26-10.14.04:453][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.05.26-10.14.04:458][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.05.26-10.14.04:582][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.05.26-10.14.04:652][  0]LogInit: Undo buffer set to 256 MB
[2025.05.26-10.14.04:661][  0]LogInit: Transaction tracking system initialized
[2025.05.26-10.14.04:812][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded H:/Plugins/BlenderLinkProject/Saved/SourceControl/UncontrolledChangelists.json
[2025.05.26-10.14.05:841][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.57ms
[2025.05.26-10.14.05:843][  0]LocalizationService: Localization service is disabled
[2025.05.26-10.14.06:047][  0]LogTimingProfiler: Initialize
[2025.05.26-10.14.06:070][  0]LogTimingProfiler: OnSessionChanged
[2025.05.26-10.14.06:070][  0]LoadingProfiler: Initialize
[2025.05.26-10.14.06:070][  0]LoadingProfiler: OnSessionChanged
[2025.05.26-10.14.06:074][  0]LogNetworkingProfiler: Initialize
[2025.05.26-10.14.06:077][  0]LogNetworkingProfiler: OnSessionChanged
[2025.05.26-10.14.06:077][  0]LogMemoryProfiler: Initialize
[2025.05.26-10.14.06:077][  0]LogMemoryProfiler: OnSessionChanged
[2025.05.26-10.14.09:235][  0]LogAutoReimportManager: Warning: Unable to watch directory H:/Plugins/BlenderLinkProject/Content/ as it will conflict with another watching H:/Plugins/BlenderLinkProject/Content/.
[2025.05.26-10.14.09:252][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/' took 0.02s
[2025.05.26-10.14.12:664][  0]LogPython: Using Python 3.11.8
[2025.05.26-10.14.15:678][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.05.26-10.14.16:062][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.05.26-10.14.16:755][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.05.26-10.14.18:299][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.05.26-10.14.18:299][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.05.26-10.14.18:843][  0]LogEditorDataStorage: Initializing
[2025.05.26-10.14.18:881][  0]LogEditorDataStorage: Initialized
[2025.05.26-10.14.18:882][  0]LogWindows: Attached monitors:
[2025.05.26-10.14.18:882][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.05.26-10.14.18:882][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY2' [PRIMARY]
[2025.05.26-10.14.18:882][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY3'
[2025.05.26-10.14.18:882][  0]LogWindows: Found 3 attached monitors.
[2025.05.26-10.14.18:882][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.26-10.14.18:926][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.05.26-10.14.19:054][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.05.26-10.14.19:185][  0]SourceControl: Revision control is disabled
[2025.05.26-10.14.19:210][  0]LogUnrealEdMisc: Loading editor; pre map load, took 149.705
[2025.05.26-10.14.19:382][  0]Cmd: MAP LOAD FILE="H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/TestLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.05.26-10.14.19:498][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.05.26-10.14.19:534][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.26-10.14.19:879][  0]LogAssetRegistry: Display: Asset registry cache written as 43.8 MiB to H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin
[2025.05.26-10.14.20:227][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.05.26-10.14.20:543][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.65ms
[2025.05.26-10.14.20:645][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'TestLevel'.
[2025.05.26-10.14.20:645][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world TestLevel
[2025.05.26-10.14.20:731][  0]LogWorldPartition: ULevel::OnLevelLoaded(TestLevel)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.05.26-10.14.20:731][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.05.26-10.14.20:731][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.05.26-10.14.24:283][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.26-10.14.24:304][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.26-10.14.24:306][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.26-10.14.24:307][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.05.26-10.14.24:307][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.05.26-10.14.24:308][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.26-10.14.24:310][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.05.26-10.14.29:437][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.05.26-10.14.29:587][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.05.26-10.14.30:033][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.26-10.14.30:037][  0]LogSkeletalMesh: Built Skeletal Mesh [0.45s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.05.26-10.14.30:058][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.05.26-10.14.30:059][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.05.26-10.14.30:454][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.26-10.14.30:456][  0]LogSkeletalMesh: Built Skeletal Mesh [0.40s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.05.26-10.14.31:569][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.26-10.14.31:572][  0]LogSkeletalMesh: Built Skeletal Mesh [1.52s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.05.26-10.14.31:636][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.05.26-10.14.31:843][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.26-10.14.31:846][  0]LogSkeletalMesh: Built Skeletal Mesh [0.21s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.05.26-10.14.31:858][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.05.26-10.14.32:289][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.05.26-10.14.32:606][  0]LogWorldPartition: Display: WorldPartition initialize took 11.8 sec
[2025.05.26-10.14.34:338][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.05.26-10.14.37:908][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.26-10.14.37:924][  0]LogSkeletalMesh: Built Skeletal Mesh [6.07s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.05.26-10.14.39:532][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.05.26-10.14.39:984][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.84ms
[2025.05.26-10.14.40:015][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.05.26-10.14.40:032][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 18.111ms to complete.
[2025.05.26-10.14.40:691][  0]LogUnrealEdMisc: Total Editor Startup Time, took 171.187
[2025.05.26-10.14.40:979][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.05.26-10.14.41:866][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.26-10.14.41:924][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.26-10.14.41:965][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.26-10.14.42:005][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.26-10.14.42:674][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.26-10.14.42:709][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.05.26-10.14.42:731][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.26-10.14.42:744][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.05.26-10.14.42:746][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.26-10.14.42:749][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.05.26-10.14.42:757][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.26-10.14.42:771][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.05.26-10.14.42:787][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.26-10.14.42:793][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.05.26-10.14.42:802][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.26-10.14.42:810][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.05.26-10.14.42:811][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.26-10.14.42:816][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.05.26-10.14.42:824][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.26-10.14.42:828][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.05.26-10.14.42:836][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.26-10.14.42:842][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.05.26-10.14.42:843][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.26-10.14.42:851][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.05.26-10.14.45:162][  0]LogSlate: Took 0.007885 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.05.26-10.14.48:574][  0]LogSlate: External Image Picker: DecompressImage failed
[2025.05.26-10.14.49:001][  0]LogStall: Startup...
[2025.05.26-10.14.49:003][  0]LogStall: Startup complete.
[2025.05.26-10.14.49:060][  0]LogLoad: (Engine Initialization) Total time: 179.56 seconds
[2025.05.26-10.14.49:951][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/0/CX/RO749IAXISCV8LT9J5BYI5) ...
[2025.05.26-10.14.50:122][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/0/KO/FUVXQ5B7IQ0H9BHE6O7NFT) ...
[2025.05.26-10.14.50:252][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/1/2F/MHEXK6BJ1KVAC7HBYSCFW0) ...
[2025.05.26-10.14.50:271][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/4/4S/RSJMJFOJ5Q10YF2VAZNYQE) ...
[2025.05.26-10.14.50:326][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/7/P8/98L7UHJMMJP3104IWTJD5O) ...
[2025.05.26-10.14.50:361][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/8/2P/7D2L64CH3U27EU4ATOC2OJ) ...
[2025.05.26-10.14.50:400][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/8/HU/3LAMRMN598G9RNI3NLQSW0) ...
[2025.05.26-10.14.50:492][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/8/KN/A8O4S8R7MMUVCDRNUYVZIP) ...
[2025.05.26-10.14.50:555][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/8/XZ/EJ2HFWY2Z0YYGP0CG2AL46) ...
[2025.05.26-10.14.50:622][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/B/OG/O63E6S0UGSLAKAAT7UN1QX) ...
[2025.05.26-10.14.50:738][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/C/1Y/43ZX09OBZSVBR74I50XSHI) ...
[2025.05.26-10.14.50:890][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.05.26-10.14.50:890][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.05.26-10.14.51:307][  0]LogSlate: Took 0.011790 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.05.26-10.14.51:435][  0]LogSlate: Took 0.020229 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.05.26-10.14.51:475][  0]LogSlate: Took 0.009287 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.05.26-10.14.51:619][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.05.26-10.14.51:619][  0]LogStreaming: Display: FlushAsyncLoading(502): 1 QueuedPackages, 0 AsyncPackages
[2025.05.26-10.14.51:691][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.05.26-10.14.51:691][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.05.26-10.14.51:691][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.26-10.14.52:085][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.26-10.14.52:085][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.05.26-10.14.52:106][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.05.26-10.14.52:107][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.05.26-10.14.52:107][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.26-10.14.52:166][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.26-10.14.52:166][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.05.26-10.14.52:983][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.26-10.14.53:068][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.05.26-10.14.53:068][  0]LogFab: Display: Logging in using exchange code
[2025.05.26-10.14.53:068][  0]LogFab: Display: Reading exchange code from commandline
[2025.05.26-10.14.53:068][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.05.26-10.14.53:115][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.05.26-10.14.54:146][  0]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 69.22 ms. Compile time 62.28 ms, link time 6.83 ms.
[2025.05.26-10.14.54:873][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.05.26-10.14.54:883][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 1.76 sec
[2025.05.26-10.14.55:119][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BlenderLinkProjectEditor Win64 Development
[2025.05.26-10.14.55:347][  1]LogAssetRegistry: AssetRegistryGather time 4.5376s: AssetDataDiscovery 4.0489s, AssetDataGather 0.0633s, StoreResults 0.4255s. Wall time 125.4300s.
	NumCachedDirectories 0. NumUncachedDirectories 1850. NumCachedFiles 7965. NumUncachedFiles 0.
	BackgroundTickInterruptions 0.
[2025.05.26-10.14.55:425][  1]LogSourceControl: Uncontrolled asset enumeration started...
[2025.05.26-10.14.55:425][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.05.26-10.14.55:733][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.05.26-10.14.56:509][  2]LogSlate: External Image Picker: DecompressImage failed
[2025.05.26-10.14.56:731][  2]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 56.417068
[2025.05.26-10.14.56:732][  2]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.05.26-10.14.56:762][  2]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 57.399467
[2025.05.26-10.14.57:730][  6]LogSourceControl: Uncontrolled asset enumeration finished in 2.304753 seconds (Found 7941 uncontrolled assets)
[2025.05.26-10.14.57:797][  6]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.26-10.14.58:784][  9]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 59.185947
[2025.05.26-10.14.58:786][  9]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 607272702
[2025.05.26-10.14.58:786][  9]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 59.185947, Update Interval: 352.510742
[2025.05.26-10.14.59:108][ 10]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.05.26-10.14.59:120][ 10]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.15.09:434][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.15.19:435][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.15.29:436][104]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.15.39:436][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.15.49:437][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.15.59:437][194]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.16.09:440][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.16.19:438][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.16.29:437][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.16.39:438][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.16.49:439][344]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.16.59:441][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.17.09:440][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.17.19:441][434]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.17.29:441][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.17.39:441][494]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.17.49:441][524]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.17.59:442][554]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.18.09:443][584]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.18.19:443][614]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.18.29:443][644]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.18.39:443][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.18.49:444][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.18.59:445][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.19.09:446][764]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.19.19:447][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.19.29:448][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.19.39:449][854]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.19.49:449][884]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.19.59:449][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.20.09:450][944]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.20.19:450][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.20.29:451][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.20.39:451][ 34]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.20.49:451][ 64]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.20.51:452][ 70]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 412.135590
[2025.05.26-10.20.52:452][ 73]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.26-10.20.52:452][ 73]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 412.802216, Update Interval: 349.901428
[2025.05.26-10.20.59:452][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.21.09:452][124]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.21.19:453][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.21.29:455][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.21.39:456][214]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.21.49:456][244]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.21.59:456][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.22.09:456][304]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.22.19:457][334]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.22.29:457][364]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.22.39:457][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.22.49:458][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.22.59:460][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.23.09:460][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.23.19:462][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.23.29:462][544]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.23.39:462][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.23.49:462][604]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.23.59:463][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.24.09:465][664]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.24.19:465][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.24.29:465][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.24.39:466][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.24.49:468][784]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.24.59:466][814]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.25.09:467][844]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.25.19:468][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.25.29:468][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.25.39:468][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.25.49:470][964]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.25.59:470][994]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.26.09:471][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.26.19:470][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.26.29:472][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.26.39:473][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.26.45:473][132]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 766.156799
[2025.05.26-10.26.46:473][135]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.26-10.26.46:473][135]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 766.823303, Update Interval: 351.174042
[2025.05.26-10.26.49:472][144]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.26.59:472][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.27.09:473][204]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.27.19:473][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.27.29:475][264]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.27.39:476][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.27.49:476][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.27.59:477][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.28.09:478][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.28.19:478][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.28.29:479][444]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.28.39:479][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.28.49:478][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.28.59:480][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.29.09:480][564]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.29.19:481][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.29.29:481][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.29.39:481][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.29.49:483][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.29.59:483][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.30.09:483][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.30.19:484][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.30.29:485][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.30.39:485][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.30.49:486][864]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.30.59:487][894]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.31.09:487][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.31.19:487][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.31.29:488][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.31.39:487][ 14]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.31.49:488][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.31.59:488][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.32.09:489][104]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.32.19:489][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.32.29:489][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.32.39:490][194]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.32.43:823][207]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1124.506714
[2025.05.26-10.32.44:823][210]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.26-10.32.44:823][210]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1125.173218, Update Interval: 331.628784
[2025.05.26-10.32.49:490][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.32.59:489][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.33.09:491][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.33.19:492][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.33.29:494][344]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.33.39:493][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.33.49:494][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.33.59:495][434]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.34.09:495][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.34.19:496][494]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.34.29:496][524]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.34.39:496][554]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.34.49:497][584]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.34.59:497][614]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.35.09:498][644]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.35.19:499][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.35.29:499][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.35.39:500][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.35.49:502][764]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.35.59:501][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.36.09:501][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.36.19:501][854]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.36.29:502][884]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.36.39:502][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.36.49:502][944]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.36.59:503][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.37.09:503][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.37.19:504][ 34]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.37.29:505][ 64]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.37.39:505][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.37.49:505][124]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.37.59:506][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.38.09:506][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.38.19:508][214]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.38.25:508][232]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1466.193726
[2025.05.26-10.38.26:842][236]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.26-10.38.26:842][236]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1467.193848, Update Interval: 352.270874
[2025.05.26-10.38.29:508][244]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.38.39:510][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.38.49:512][304]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.38.59:511][334]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.39.09:512][364]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.39.19:512][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.39.29:513][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.39.39:513][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.39.49:515][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.39.59:516][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.40.09:517][544]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.40.19:517][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.40.29:517][604]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.40.39:517][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.40.49:517][664]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.40.59:518][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.41.09:519][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.41.19:519][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.41.29:519][784]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.41.39:520][814]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.41.49:521][844]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.41.59:521][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.42.09:521][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.42.19:521][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.42.29:522][964]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.42.39:522][994]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.42.49:523][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.42.59:523][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.43.09:524][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.43.19:524][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.43.29:524][144]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.43.39:526][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.43.49:526][204]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.43.59:527][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.44.09:526][264]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.44.19:527][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.44.29:528][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.44.39:528][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.44.41:528][360]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1842.214478
[2025.05.26-10.44.42:528][363]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.26-10.44.42:528][363]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1842.880981, Update Interval: 346.927704
[2025.05.26-10.44.49:529][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.44.59:530][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.45.09:530][444]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.45.19:530][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.45.29:530][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.45.39:531][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.45.49:532][564]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.45.59:533][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.46.09:534][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.46.19:534][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.46.29:534][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.46.39:535][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.46.49:535][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.46.59:536][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.47.09:537][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.47.19:537][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.47.29:538][864]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.47.39:539][894]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.47.49:539][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.47.59:539][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.48.09:539][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.48.19:539][ 14]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.48.29:540][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.48.39:540][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.48.49:540][104]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.48.59:541][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.49.09:542][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.49.19:542][194]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.49.29:543][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.49.39:544][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.49.49:544][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.49.59:544][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.50.09:544][344]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.50.19:545][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.50.29:545][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.50.39:545][434]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.50.49:547][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.50.59:547][494]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.51.00:547][497]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2221.235107
[2025.05.26-10.51.01:547][500]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.26-10.51.01:547][500]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2221.901855, Update Interval: 317.981506
[2025.05.26-10.51.09:547][524]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.51.19:548][554]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.51.29:548][584]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.51.39:549][614]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.51.49:549][644]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.51.59:550][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.52.09:550][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.52.19:551][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.52.29:551][764]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.52.39:550][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.52.49:552][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.52.59:552][854]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.53.09:552][884]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.53.19:554][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.53.29:554][944]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.53.39:554][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.53.49:556][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.53.59:557][ 34]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.54.09:557][ 64]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.54.19:558][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.54.29:559][124]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.54.39:560][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.54.49:560][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.54.59:560][214]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.55.09:561][244]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.55.19:561][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.55.29:563][304]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.55.39:563][334]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.55.49:564][364]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.55.59:564][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.56.09:568][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.56.19:568][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.56.29:568][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.56.39:569][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.56.49:570][544]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.56.59:237][573]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2579.922119
[2025.05.26-10.56.59:570][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.57.00:237][576]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.26-10.57.00:237][576]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2580.588867, Update Interval: 343.520020
[2025.05.26-10.57.09:571][604]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.57.19:572][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.57.29:572][664]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.57.39:574][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.57.49:572][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.57.59:574][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.58.09:574][784]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.58.19:574][814]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.58.29:575][844]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.58.39:577][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.58.49:577][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.58.59:577][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.59.09:580][964]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.59.19:579][994]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.59.29:580][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.59.39:580][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.59.49:582][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-10.59.59:582][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.00.09:582][144]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.00.19:584][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.00.29:584][204]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.00.39:586][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.00.49:586][264]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.00.59:586][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.01.09:587][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.01.19:588][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.01.29:588][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.01.39:590][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.01.49:591][444]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.01.59:592][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.02.09:592][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.02.19:593][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.02.29:595][564]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.02.39:595][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.02.49:596][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.02.59:596][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.03.09:597][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.03.15:598][702]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2956.284668
[2025.05.26-11.03.16:931][706]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.26-11.03.16:931][706]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2957.284912, Update Interval: 330.771820
[2025.05.26-11.03.19:598][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.26-11.03.29:598][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
