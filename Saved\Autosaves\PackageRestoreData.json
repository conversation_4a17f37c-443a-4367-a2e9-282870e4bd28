{ 
 
 	 " R e s t o r e E n a b l e d " :   t r u e , 
 
 	 " P a c k a g e s " :   [ 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ c a r t i l a g e _ s h a d e r " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M H _ F r i e n d _ c a r t i l a g e _ s h a d e r " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ c a r t i l a g e _ s h a d e r _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / c a r t i l a g e _ l o d 0 _ m e s h " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " c a r t i l a g e _ l o d 0 _ m e s h " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / c a r t i l a g e _ l o d 0 _ m e s h _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / c a r t i l a g e _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " c a r t i l a g e _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / c a r t i l a g e _ l o d 0 _ m e s h _ S k e l e t o n _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ h e a d _ s h a d e r " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M H _ F r i e n d _ h e a d _ s h a d e r " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ h e a d _ s h a d e r _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / h e a d _ r o u g h n e s s _ m a p " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " h e a d _ r o u g h n e s s _ m a p " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / h e a d _ r o u g h n e s s _ m a p _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / c u s t o m _ h e a d _ l o d 0 _ m e s h " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " c u s t o m _ h e a d _ l o d 0 _ m e s h " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / c u s t o m _ h e a d _ l o d 0 _ m e s h _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / c u s t o m _ h e a d _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " c u s t o m _ h e a d _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / c u s t o m _ h e a d _ l o d 0 _ m e s h _ S k e l e t o n _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ e y e E d g e _ s h a d e r " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M H _ F r i e n d _ e y e E d g e _ s h a d e r " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ e y e E d g e _ s h a d e r _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e E d g e _ l o d 0 _ m e s h " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " e y e E d g e _ l o d 0 _ m e s h " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e E d g e _ l o d 0 _ m e s h _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e E d g e _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " e y e E d g e _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e E d g e _ l o d 0 _ m e s h _ S k e l e t o n _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ e y e L e f t _ s h a d e r " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M H _ F r i e n d _ e y e L e f t _ s h a d e r " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ e y e L e f t _ s h a d e r _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e s _ n o r m a l _ m a p " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " e y e s _ n o r m a l _ m a p " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e s _ n o r m a l _ m a p _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e L e f t _ l o d 0 _ m e s h " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " e y e L e f t _ l o d 0 _ m e s h " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e L e f t _ l o d 0 _ m e s h _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e L e f t _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " e y e L e f t _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e L e f t _ l o d 0 _ m e s h _ S k e l e t o n _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ e y e R i g h t _ s h a d e r " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M H _ F r i e n d _ e y e R i g h t _ s h a d e r " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ e y e R i g h t _ s h a d e r _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e R i g h t _ l o d 0 _ m e s h " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " e y e R i g h t _ l o d 0 _ m e s h " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e R i g h t _ l o d 0 _ m e s h _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e R i g h t _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " e y e R i g h t _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e R i g h t _ l o d 0 _ m e s h _ S k e l e t o n _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ e y e l a s h e s _ s h a d e r " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M H _ F r i e n d _ e y e l a s h e s _ s h a d e r " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ e y e l a s h e s _ s h a d e r _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e l a s h e s _ l o d 0 _ m e s h " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " e y e l a s h e s _ l o d 0 _ m e s h " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e l a s h e s _ l o d 0 _ m e s h _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e l a s h e s _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " e y e l a s h e s _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e l a s h e s _ l o d 0 _ m e s h _ S k e l e t o n _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ e y e s h e l l _ s h a d e r " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M H _ F r i e n d _ e y e s h e l l _ s h a d e r " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ e y e s h e l l _ s h a d e r _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e s h e l l _ l o d 0 _ m e s h " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " e y e s h e l l _ l o d 0 _ m e s h " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e s h e l l _ l o d 0 _ m e s h _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e s h e l l _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " e y e s h e l l _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / e y e s h e l l _ l o d 0 _ m e s h _ S k e l e t o n _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / h e a d _ l o d 0 _ m e s h " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " h e a d _ l o d 0 _ m e s h " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / h e a d _ l o d 0 _ m e s h _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / h e a d _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " h e a d _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / h e a d _ l o d 0 _ m e s h _ S k e l e t o n _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ s a l i v a _ s h a d e r " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M H _ F r i e n d _ s a l i v a _ s h a d e r " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ s a l i v a _ s h a d e r _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / s a l i v a _ l o d 0 _ m e s h " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " s a l i v a _ l o d 0 _ m e s h " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / s a l i v a _ l o d 0 _ m e s h _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / s a l i v a _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " s a l i v a _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / s a l i v a _ l o d 0 _ m e s h _ S k e l e t o n _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ t e e t h _ s h a d e r " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M H _ F r i e n d _ t e e t h _ s h a d e r " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / M H _ F r i e n d _ t e e t h _ s h a d e r _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / t e e t h _ n o r m a l _ m a p " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " t e e t h _ n o r m a l _ m a p " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / t e e t h _ n o r m a l _ m a p _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / t e e t h _ l o d 0 _ m e s h " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " t e e t h _ l o d 0 _ m e s h " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / t e e t h _ l o d 0 _ m e s h _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / t e e t h _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " t e e t h _ l o d 0 _ m e s h _ S k e l e t o n " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / u n t i t l e d _ c a t e g o r y / u n t i t l e d _ a s s e t / t e e t h _ l o d 0 _ m e s h _ S k e l e t o n _ A u t o 1 . u a s s e t " 
 
 	 	 } 
 
 	 ] 
 
 } 