{ 
 
 	 " R e s t o r e E n a b l e d " :   t r u e , 
 
 	 " P a c k a g e s " :   [ 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / M e t a H u m a n s / E x p o r t / M H _ F r i e n d _ h e a d _ s h a d e r " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M H _ F r i e n d _ h e a d _ s h a d e r " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / M e t a H u m a n s / E x p o r t / M H _ F r i e n d _ h e a d _ s h a d e r _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / M e t a H u m a n s / E x p o r t / h e a d _ r o u g h n e s s _ m a p " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " h e a d _ r o u g h n e s s _ m a p " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / M e t a H u m a n s / E x p o r t / h e a d _ r o u g h n e s s _ m a p _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / M e t a H u m a n s / E x p o r t / c u s t o m _ h e a d " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " c u s t o m _ h e a d " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / M e t a H u m a n s / E x p o r t / c u s t o m _ h e a d _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / M e t a H u m a n s / E x p o r t / c u s t o m _ h e a d _ S k e l e t o n " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " c u s t o m _ h e a d _ S k e l e t o n " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / M e t a H u m a n s / E x p o r t / c u s t o m _ h e a d _ S k e l e t o n _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / M e t a H u m a n s / E x p o r t / c u s t o m _ h e a d _ P h y s i c s A s s e t " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " c u s t o m _ h e a d _ P h y s i c s A s s e t " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / M e t a H u m a n s / E x p o r t / c u s t o m _ h e a d _ P h y s i c s A s s e t _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / _ _ E x t e r n a l A c t o r s _ _ / M e t a H u m a n s / T e s t / T e s t L e v e l / 3 / T A / X S J V 7 1 B H D G A 7 S M N 5 7 P S 0 Z X " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " c u s t o m _ h e a d " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / _ _ E x t e r n a l A c t o r s _ _ / M e t a H u m a n s / T e s t / T e s t L e v e l / 3 / T A / X S J V 7 1 B H D G A 7 S M N 5 7 P S 0 Z X _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / _ _ E x t e r n a l A c t o r s _ _ / M e t a H u m a n s / T e s t / T e s t L e v e l / D / A Z / T 9 G P W Z T O O 0 K K K 1 D H A X I O 7 T " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M e t a h u m a n " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / _ _ E x t e r n a l A c t o r s _ _ / M e t a H u m a n s / T e s t / T e s t L e v e l / D / A Z / T 9 G P W Z T O O 0 K K K 1 D H A X I O 7 T _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / M e t a H u m a n s / T e s t / T e s t " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " T e s t " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / M e t a H u m a n s / T e s t / T e s t _ A u t o 2 . u a s s e t " 
 
 	 	 } 
 
 	 ] 
 
 } 