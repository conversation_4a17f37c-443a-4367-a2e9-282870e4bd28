{ 
 
 	 " R e s t o r e E n a b l e d " :   t r u e , 
 
 	 " P a c k a g e s " :   [ 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / M H _ F r i e n d _ h e a d _ s h a d e r " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M H _ F r i e n d _ h e a d _ s h a d e r " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / M H _ F r i e n d _ h e a d _ s h a d e r _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / h e a d _ r o u g h n e s s _ m a p " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " h e a d _ r o u g h n e s s _ m a p " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / h e a d _ r o u g h n e s s _ m a p _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / c u s t o m _ h e a d " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " c u s t o m _ h e a d " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / c u s t o m _ h e a d _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / c u s t o m _ h e a d _ S k e l e t o n " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " c u s t o m _ h e a d _ S k e l e t o n " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / c u s t o m _ h e a d _ S k e l e t o n _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / c u s t o m _ h e a d _ P h y s i c s A s s e t " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " c u s t o m _ h e a d _ P h y s i c s A s s e t " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / c u s t o m _ h e a d _ P h y s i c s A s s e t _ A u t o 1 . u a s s e t " 
 
 	 	 } 
 
 	 ] 
 
 } 