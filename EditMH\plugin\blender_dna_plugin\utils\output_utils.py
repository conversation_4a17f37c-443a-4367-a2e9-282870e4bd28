"""
Utility functions for managing output items for Send to Unreal functionality.
"""

import bpy
from pathlib import Path

def get_lod_index(object_name):
    """Get the LOD index from an object name following MetaHuman naming convention"""
    if '_lod' in object_name:
        try:
            # Extract LOD number from name like "head_lod0_mesh"
            lod_part = object_name.split('_lod')[1]
            lod_number = lod_part.split('_')[0]
            return int(lod_number)
        except (IndexError, ValueError):
            pass
    return -1  # No LOD or invalid format

def get_mesh_output_items():
    """Get all mesh objects that should be included in the output"""
    mesh_objects = []
    dna_tools = bpy.context.scene.dna_tools
    
    # Get the head rig reference
    head_rig = dna_tools.head_rig
    if not head_rig:
        # Try to find the head rig automatically
        for obj in bpy.data.objects:
            if obj.type == 'ARMATURE' and obj.name == 'root':
                head_rig = obj
                dna_tools.head_rig = obj
                break
    
    if not head_rig:
        return mesh_objects
    
    # Get all mesh objects that are skinned to the head rig
    for scene_object in bpy.data.objects:
        if scene_object.type == 'MESH':
            for modifier in scene_object.modifiers:
                if modifier.type == 'ARMATURE' and modifier.object == head_rig:
                    mesh_objects.append(scene_object)
                    break
    
    return mesh_objects

def get_image_output_items():
    """Get all image textures that should be included in the output"""
    image_nodes = []
    dna_tools = bpy.context.scene.dna_tools
    
    # Get the material reference
    material = dna_tools.material
    if not material:
        # Try to find the head material automatically
        for mat in bpy.data.materials:
            if 'head' in mat.name.lower():
                material = mat
                dna_tools.material = mat
                break
    
    if not material or not material.use_nodes:
        return image_nodes
    
    # Look for texture nodes in the material
    for node in material.node_tree.nodes:
        if node.type == 'TEX_IMAGE' and node.image:
            # Map common texture types to file names
            texture_name = node.image.name
            if 'diffuse' in texture_name.lower() or 'albedo' in texture_name.lower():
                file_name = 'head_diffuse.png'
            elif 'normal' in texture_name.lower():
                file_name = 'head_normal.png'
            elif 'roughness' in texture_name.lower():
                file_name = 'head_roughness.png'
            elif 'specular' in texture_name.lower():
                file_name = 'head_specular.png'
            else:
                file_name = f'{texture_name}.png'
            
            image_nodes.append((node.image, file_name))
    
    return image_nodes

def update_output_items():
    """Update the output item list based on current scene objects"""
    dna_tools = bpy.context.scene.dna_tools
    
    # Clear existing output items
    dna_tools.output_item_list.clear()
    
    # Find and set head mesh reference
    if not dna_tools.head_mesh:
        for obj in bpy.data.objects:
            if obj.type == 'MESH' and 'head' in obj.name.lower() and 'lod0' in obj.name.lower():
                dna_tools.head_mesh = obj
                break
    
    # Find and set head rig reference  
    if not dna_tools.head_rig:
        for obj in bpy.data.objects:
            if obj.type == 'ARMATURE' and obj.name == 'root':
                dna_tools.head_rig = obj
                break
    
    # Add mesh objects to output list
    mesh_objects = get_mesh_output_items()
    if dna_tools.head_rig:
        mesh_objects.append(dna_tools.head_rig)
    
    for scene_object in mesh_objects:
        # Check if item already exists
        existing_item = None
        for item in dna_tools.output_item_list:
            if item.scene_object == scene_object:
                existing_item = item
                break
        
        if not existing_item:
            new_item = dna_tools.output_item_list.add()
            new_item.scene_object = scene_object
            
            if scene_object == dna_tools.head_mesh:
                new_item.name = 'head_lod0_mesh'
                new_item.editable_name = False
            elif scene_object == dna_tools.head_rig:
                new_item.name = 'rig'
                new_item.editable_name = False
            else:
                # Extract name without character prefix
                name = scene_object.name
                if dna_tools.dna_name and name.startswith(f'{dna_tools.dna_name}_'):
                    name = name.replace(f'{dna_tools.dna_name}_', '')
                new_item.name = name
                new_item.editable_name = True
    
    # Add image objects to output list
    for image_object, file_name in get_image_output_items():
        # Check if item already exists
        existing_item = None
        for item in dna_tools.output_item_list:
            if item.image_object == image_object:
                existing_item = item
                break
        
        if not existing_item:
            new_item = dna_tools.output_item_list.add()
            new_item.image_object = image_object
            new_item.name = file_name
            new_item.editable_name = False
    
    # Remove any output items that no longer have valid references
    items_to_remove = []
    for i, item in enumerate(dna_tools.output_item_list):
        if not item.scene_object and not item.image_object:
            items_to_remove.append(i)
    
    # Remove items in reverse order to maintain indices
    for i in reversed(items_to_remove):
        dna_tools.output_item_list.remove(i)

def update_material_slot_mappings():
    """Update material slot to Unreal material instance mappings"""
    dna_tools = bpy.context.scene.dna_tools
    
    # Clear existing mappings
    dna_tools.material_slot_mappings.clear()
    
    # Get the head mesh
    head_mesh = dna_tools.head_mesh
    if not head_mesh:
        return
    
    # Create mappings for each material slot
    for slot in head_mesh.material_slots:
        if slot.material:
            new_mapping = dna_tools.material_slot_mappings.add()
            new_mapping.name = slot.material.name
            
            # Set default Unreal material instance path
            character_name = dna_tools.dna_name or 'Character'
            content_folder = dna_tools.unreal_content_folder.rstrip('/')
            new_mapping.asset_path = f"{content_folder}/Materials/{slot.material.name}_Inst"
            new_mapping.valid_path = True

def get_included_objects():
    """Get list of objects that are marked for inclusion in export"""
    included_objects = []
    dna_tools = bpy.context.scene.dna_tools
    
    for item in dna_tools.output_item_list:
        if item.include and item.scene_object:
            included_objects.append(item.scene_object)
    
    return included_objects

def get_lod_indexes():
    """Get list of LOD indexes present in the output items"""
    lod_indexes = set()
    dna_tools = bpy.context.scene.dna_tools
    
    for item in dna_tools.output_item_list:
        if item.include and item.scene_object and item.scene_object.type == 'MESH':
            lod_index = get_lod_index(item.scene_object.name)
            if lod_index >= 0:
                lod_indexes.add(lod_index)
    
    return sorted(list(lod_indexes))

def get_objects_for_lod(lod_index):
    """Get objects that belong to a specific LOD level"""
    objects = []
    dna_tools = bpy.context.scene.dna_tools
    
    for item in dna_tools.output_item_list:
        if item.include and item.scene_object:
            if get_lod_index(item.scene_object.name) == lod_index:
                objects.append(item.scene_object)
    
    return objects
