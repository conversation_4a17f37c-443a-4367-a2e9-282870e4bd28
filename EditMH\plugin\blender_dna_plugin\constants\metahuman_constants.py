"""
MetaHuman constants and mappings following the example implementation structure.
"""

from pathlib import Path
import os

# Material shader mappings for Unreal Engine (copied from example)
MESH_SHADER_MAPPING = {
    "head_shader": "/Game/MetaHumans/Common/Face/Materials/MI_Head_Inst",
    "teeth_shader": "/Game/MetaHumans/Common/Face/Materials/MI_Teeth_Inst",
    "saliva_shader": "/Game/MetaHumans/Common/Face/Materials/MI_Saliva_Inst",
    "eyeLeft_shader": "/Game/MetaHumans/Common/Face/Materials/MI_EyeRefractive_Inst_L",
    "eyeRight_shader": "/Game/MetaHumans/Common/Face/Materials/MI_EyeRefractive_Inst_R",
    "eyeshell_shader": "/Game/MetaHumans/Common/Face/Materials/MI_EyeOcclusion_Inst",
    "eyelashes_shader": "/Game/MetaHumans/Common/Materials/M_EyelashLowerLODs_Inst",
    "eyelashesShadow_shader": "/Game/MetaHumans/Common/Face/Materials/MI_EyeOcclusion_Inst",
    "eyeEdge_shader": "/Game/MetaHumans/Common/Face/Materials/MI_lacrimal_fluid_Inst",
    "cartilage_shader": "/Game/MetaHumans/Common/Face/Materials/M_Cartilage",
}

# Face board name (from example)
FACE_BOARD_NAME = "face_gui"
HEAD_MATERIAL_NAME = "head_shader"
MASKS_TEXTURE = "combined_masks.tga"
TOPOLOGY_TEXTURE = "head_topology.png"
NUMBER_OF_FACE_LODS = 8

# Regex patterns (from example)
INVALID_NAME_CHARACTERS_REGEX = r"[^-+\w]+"
LOD_REGEX = r"(?i)(_LOD\d).*"

# Topology mesh names (from example)
HEAD_TOPOLOGY_MESH = "head_topology"
HEAD_TOPOLOGY_MESH_CAGE = "head_topology_cage"
HEAD_SHRINK_WRAP_MODIFIER_PREFIX = "shrink_wrap"
TOPO_GROUP_PREFIX = "TOPO_GROUP_"
SHAPE_KEY_GROUP_PREFIX = "SHAPE_KEY_"

# Texture file mappings (matching example)
HEAD_MAPS = {
    "Color_MAIN": "head_color_map.tga",
    "Color_CM1": "head_cm1_color_map.tga",
    "Color_CM2": "head_cm2_color_map.tga",
    "Color_CM3": "head_cm3_color_map.tga",
    "Normal_MAIN": "head_normal_map.tga",
    "Normal_WM1": "head_wm1_normal_map.tga",
    "Normal_WM2": "head_wm2_normal_map.tga",
    "Normal_WM3": "head_wm3_normal_map.tga",
    "Cavity_MAIN": "head_cavity_map.tga",
    "Roughness_MAIN": "head_roughness_map.tga"
}

# Alternate texture file names (matching example)
ALTERNATE_TEXTURE_FILE_NAMES = {
    "head_color_map.tga": "FaceColor_MAIN",
    "head_cm1_color_map.tga": "FaceColor_CM1",
    "head_cm2_color_map.tga": "FaceColor_CM2",
    "head_cm3_color_map.tga": "FaceColor_CM3",
    "head_normal_map.tga": "FaceNormal_MAIN",
    "head_wm1_normal_map.tga": "FaceNormal_WM1",
    "head_wm2_normal_map.tga": "FaceNormal_WM2",
    "head_wm3_normal_map.tga": "FaceNormal_WM3",
    "head_cavity_map.tga": "FaceCavity_MAIN",
    "head_roughness_map.tga": "FaceRoughness_MAIN"
}

# Alternate texture file extensions (matching example)
ALTERNATE_TEXTURE_FILE_EXTENSIONS = [
    ".tga",
    ".png"
]

# Unreal exported head material names (matching example)
UNREAL_EXPORTED_HEAD_MATERIAL_NAMES = [
    'MI_HeadSynthesized_Baked'
]

# Resource folder paths (matching example structure)
RESOURCES_FOLDER = Path(os.path.dirname(os.path.dirname(__file__)), "resources")
SEND2UE_FOLDER = RESOURCES_FOLDER / "send2ue"
POSES_FOLDER = RESOURCES_FOLDER / "poses"
IMAGES_FOLDER = RESOURCES_FOLDER / "images"
MAPPINGS_FOLDER = RESOURCES_FOLDER / "mappings"
UNREAL_FOLDER = RESOURCES_FOLDER / "unreal"

# Send2UE specific files (matching example)
SEND2UE_FACE_SETTINGS = SEND2UE_FOLDER / "meta-human_dna.json"
SEND2UE_EXTENSION = SEND2UE_FOLDER / "meta_human_dna_extension.py"

# Default export paths (matching example structure)
DEFAULT_OUTPUT_FOLDER = "//export/"
DEFAULT_UNREAL_CONTENT_FOLDER = "/Game/MetaHumans/Export/"

# LOD naming patterns (matching example)
LOD_REGEX = r'.*[_\.]lod(\d+).*'

# MetaHuman component naming patterns
METAHUMAN_MESH_PATTERNS = [
    "head_lod0_mesh",
    "teeth_lod0_mesh",
    "saliva_lod0_mesh",
    "eyeLeft_lod0_mesh",
    "eyeRight_lod0_mesh",
    "eyeshell_lod0_mesh",
    "eyelashes_lod0_mesh",
    "eyelashesShadow_lod0_mesh",
    "eyeEdge_lod0_mesh",
    "cartilage_lod0_mesh"
]

# Default character naming
DEFAULT_CHARACTER_NAME = "character"

# Export file extensions
DNA_FILE_EXTENSION = ".dna"
FBX_FILE_EXTENSION = ".fbx"
JSON_FILE_EXTENSION = ".json"

# Unreal Engine paths
UNREAL_METAHUMAN_COMMON_PATH = "/Game/MetaHumans/Common/"
UNREAL_FACE_MATERIALS_PATH = "/Game/MetaHumans/Common/Face/Materials/"
UNREAL_COMMON_MATERIALS_PATH = "/Game/MetaHumans/Common/Materials/"

# Export settings
EXPORT_BONES_ONLY = True  # For DNA files - meshes sent via FBX
EXPORT_MESHES_VIA_FBX = True  # Meshes exported via FBX, not DNA
EXPORT_LOD_SEPARATELY = True  # Export each LOD as separate file

# File naming patterns (matching example)
DNA_FILE_PATTERN = "{character_name}.dna"
FBX_FILE_PATTERN = "{character_name}.fbx"
LOD_FILE_PATTERN = "{character_name}_lod{lod_index}_mesh.fbx"

# Unreal asset naming patterns
UNREAL_ASSET_PATTERN = "{content_folder}{character_name}"
UNREAL_MATERIAL_INSTANCE_PATTERN = "{content_folder}Materials/{material_name}_Inst"

# Default Unreal Engine settings
DEFAULT_UNREAL_SETTINGS = {
    "face_control_rig_asset_path": "",
    "face_anim_bp_asset_path": "",
    "blueprint_asset_path": "",
    "level_sequence_asset_path": "",
    "copy_assets": False
}

# Platform detection
PLATFORM_NAMES = {
    "linux": "Linux",
    "linux2": "Linux",
    "win32": "Windows",
    "darwin": "Mac OS X",
}

# Export validation settings
VALIDATION_SETTINGS = {
    "validate_dna_loaded": True,
    "validate_head_mesh": True,
    "validate_armature": True,
    "validate_output_folder": True,
    "validate_unreal_paths": False  # Optional validation
}

# Debug settings
DEBUG_SETTINGS = {
    "save_export_log": False,
    "verbose_logging": False,
    "save_intermediate_files": False
}
