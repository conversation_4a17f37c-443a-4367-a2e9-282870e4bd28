"""
Send to Unreal operator for the Blender MetaHuman DNA Plugin.
"""

import bpy
from bpy.types import Operator
from pathlib import Path

# Try to import the DNA modules
try:
    import dna
    DNA_MODULES_AVAILABLE = True
except ImportError:
    DNA_MODULES_AVAILABLE = False

class DNA_OT_SendToUnreal(Operator):
    """Exports the MetaHuman DNA, SkeletalMesh, and Textures, then imports them into Unreal Engine. This requires the Send to Unreal addon to be installed"""
    bl_idname = "dna.send_to_unreal"
    bl_label = "Send to Unreal"
    bl_description = "Send the MetaHuman to Unreal Engine using the Send2UE addon"
    bl_options = {'REGISTER', 'UNDO'}

    @classmethod
    def poll(cls, context):
        """Check if the operator can be executed"""
        # Check if send2ue addon is available
        send2ue_properties = getattr(bpy.context.scene, 'send2ue', None)
        if not send2ue_properties:
            return False
        
        # Check if DNA is loaded
        dna_tools = context.scene.dna_tools
        if not dna_tools.is_dna_loaded:
            return False
            
        # Check if we have a head mesh
        if not dna_tools.head_mesh_name:
            return False
            
        return True

    def execute(self, context):
        """Execute the send to unreal operation"""
        # Check if send2ue addon is installed
        send2ue_properties = getattr(bpy.context.scene, 'send2ue', None)
        send2ue_addon_preferences = bpy.context.preferences.addons.get('send2ue')
        if not send2ue_properties:
            self.report({'ERROR'}, 'The Send to Unreal addon is not installed!')
            return {'CANCELLED'}

        dna_tools = context.scene.dna_tools
        
        # Get the head mesh object
        head_mesh = bpy.data.objects.get(dna_tools.head_mesh_name)
        if not head_mesh:
            self.report({'ERROR'}, 'Head mesh not found!')
            return {'CANCELLED'}

        # Get the armature object
        armature = bpy.data.objects.get(dna_tools.armature_name)
        if not armature:
            self.report({'ERROR'}, 'Armature not found!')
            return {'CANCELLED'}

        # Prepare objects for export
        included_objects = []
        
        # Add head mesh
        included_objects.append(head_mesh)
        
        # Add armature
        included_objects.append(armature)
        
        # Add any additional meshes (LODs, etc.)
        for obj in bpy.data.objects:
            if obj.type == 'MESH' and obj != head_mesh:
                # Check if this mesh is related to our MetaHuman
                if obj.name.startswith(dna_tools.head_mesh_name.split('_')[0]):
                    included_objects.append(obj)

        # Clear any existing object collection override
        bpy.context.window_manager.send2ue.object_collection_override.clear()
        
        # Set the objects to be exported
        bpy.context.window_manager.send2ue.object_collection_override.extend(included_objects)
        
        # Set the active object to the head mesh
        bpy.context.view_layer.objects.active = head_mesh
        
        # Enable the meta_human_dna extension
        if hasattr(bpy.context.scene.send2ue, 'extensions'):
            if hasattr(bpy.context.scene.send2ue.extensions, 'meta_human_dna'):
                bpy.context.scene.send2ue.extensions.meta_human_dna.enabled = True

        try:
            # Run send to unreal
            bpy.ops.wm.send2ue('INVOKE_DEFAULT')
            self.report({'INFO'}, "Successfully initiated send to Unreal Engine")
            
        except Exception as e:
            self.report({'ERROR'}, f"Failed to send to Unreal Engine: {str(e)}")
            return {'CANCELLED'}

        return {'FINISHED'}

# Classes to register
classes = [
    DNA_OT_SendToUnreal,
]

def register():
    """Register the operator classes"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister the operator classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)

if __name__ == "__main__":
    register()
