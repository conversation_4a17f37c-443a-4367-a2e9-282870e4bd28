"""
Send to Unreal operator for the Blender MetaHuman DNA Plugin.
"""

import bpy
from bpy.types import Operator
from pathlib import Path

# Try to import the DNA modules
try:
    import dna
    DNA_MODULES_AVAILABLE = True
except ImportError:
    DNA_MODULES_AVAILABLE = False

class DNA_OT_SendToUnreal(Operator):
    """Exports the MetaHuman DNA, SkeletalMesh, and Textures, then imports them into Unreal Engine. This requires the Send to Unreal addon to be installed"""
    bl_idname = "dna.send_to_unreal"
    bl_label = "Send to Unreal"
    bl_description = "Send the MetaHuman to Unreal Engine using the Send2UE addon"
    bl_options = {'REGISTER', 'UNDO'}

    @classmethod
    def poll(cls, context):
        """Check if the operator can be executed"""
        # Check if send2ue addon is available
        send2ue_properties = getattr(bpy.context.scene, 'send2ue', None)
        if not send2ue_properties:
            return False

        # Check if DNA is loaded
        dna_tools = context.scene.dna_tools
        if not dna_tools.is_dna_loaded:
            return False

        # Check if we have a head mesh (look for head_lod0_mesh or similar)
        head_mesh_found = False
        for obj in bpy.data.objects:
            if obj.type == 'MESH' and 'head' in obj.name.lower() and 'lod0' in obj.name.lower():
                head_mesh_found = True
                break

        if not head_mesh_found:
            return False

        return True

    def execute(self, context):
        """Execute the send to unreal operation"""
        # Check if send2ue addon is installed
        send2ue_properties = getattr(bpy.context.scene, 'send2ue', None)
        send2ue_addon_preferences = bpy.context.preferences.addons.get('send2ue')
        if not send2ue_properties:
            self.report({'ERROR'}, 'The Send to Unreal addon is not installed!')
            return {'CANCELLED'}

        dna_tools = context.scene.dna_tools

        # Find the head mesh object
        head_mesh = None
        for obj in bpy.data.objects:
            if obj.type == 'MESH' and 'head' in obj.name.lower() and 'lod0' in obj.name.lower():
                head_mesh = obj
                break

        if not head_mesh:
            self.report({'ERROR'}, 'Head mesh not found!')
            return {'CANCELLED'}

        # Find the armature object
        armature = None
        for obj in bpy.data.objects:
            if obj.type == 'ARMATURE' and obj.name == 'root':
                armature = obj
                break

        if not armature:
            self.report({'ERROR'}, 'Armature not found!')
            return {'CANCELLED'}

        # Export a separate DNA file since we are only going to export bone
        # transforms, mesh are sent across via FBX
        try:
            self.export_dna_file(dna_tools, head_mesh.name)
        except Exception as e:
            self.report({'ERROR'}, f'Failed to export DNA file: {str(e)}')
            return {'CANCELLED'}

        # Ensure the RPC response timeout is at least long enough to
        # import the metahuman meshes since they can be quite large.
        if send2ue_addon_preferences and hasattr(send2ue_addon_preferences, 'preferences'):
            if hasattr(send2ue_addon_preferences.preferences, 'rpc_response_timeout'):
                if send2ue_addon_preferences.preferences.rpc_response_timeout < 180:
                    send2ue_addon_preferences.preferences.rpc_response_timeout = 180

        # Set the active settings template to the face settings
        try:
            # Load the MetaHuman DNA settings template
            template_path = Path(__file__).parent.parent / 'resources' / 'send2ue' / 'meta-human_dna.json'
            if template_path.exists():
                bpy.ops.send2ue.load_template(filepath=str(template_path))
                bpy.context.scene.send2ue.active_settings_template = 'meta-human_dna'
        except Exception as e:
            print(f"Warning: Could not load send2ue template: {e}")

        # Include only the MetaHuman related objects
        included_objects = []

        # Add head mesh
        included_objects.append(head_mesh)

        # Add armature
        included_objects.append(armature)

        # Add all MetaHuman related meshes (teeth, eyes, etc.)
        head_mesh_prefix = head_mesh.name.split('_lod0_mesh')[0] if '_lod0_mesh' in head_mesh.name else head_mesh.name.split('_')[0]
        for obj in bpy.data.objects:
            if obj.type == 'MESH' and obj != head_mesh:
                # Check if this mesh is a MetaHuman component with the same prefix
                if obj.name.startswith(head_mesh_prefix) and 'lod0' in obj.name.lower():
                    included_objects.append(obj)

        # Override what objects are collected by the send2ue
        bpy.context.window_manager.send2ue.object_collection_override.clear()
        bpy.context.window_manager.send2ue.object_collection_override.extend(included_objects)
        bpy.context.view_layer.objects.active = head_mesh

        # Enable the MetaHuman DNA extension if available
        try:
            if hasattr(bpy.context.scene.send2ue.extensions, 'meta_human_dna'):
                bpy.context.scene.send2ue.extensions.meta_human_dna.enabled = True
                print("MetaHuman DNA extension enabled for this export")
            else:
                print("MetaHuman DNA extension not found - using basic export")
        except Exception as e:
            print(f"Could not enable MetaHuman DNA extension: {e}")

        try:
            # Run send to unreal
            bpy.ops.wm.send2ue('INVOKE_DEFAULT')
            self.report({'INFO'}, "Successfully sent to Unreal Engine")

        except Exception as e:
            self.report({'ERROR'}, f"Failed to send to Unreal Engine: {str(e)}")
            return {'CANCELLED'}

        return {'FINISHED'}

    def export_dna_file(self, dna_tools, head_mesh_name):
        """Export DNA file for bones only (meshes are sent via FBX)"""
        # Create export directory
        export_dir = Path.home() / 'Documents' / 'MetaHuman_Export'
        export_dir.mkdir(parents=True, exist_ok=True)

        # Generate DNA file name based on the head mesh
        character_name = head_mesh_name.replace('_lod0_mesh', '').replace('head_', '')
        dna_file_path = export_dir / f'{character_name}.dna'

        # For now, create a placeholder DNA file
        # In a full implementation, this would use the DNA export functionality
        # to export only bone transforms while excluding meshes
        try:
            # Copy the original DNA file as a base
            if dna_tools.dna_file_path and Path(dna_tools.dna_file_path).exists():
                import shutil
                shutil.copy2(dna_tools.dna_file_path, dna_file_path)
                print(f"DNA file exported to: {dna_file_path}")
            else:
                # Create a minimal DNA file placeholder
                with open(dna_file_path, 'wb') as f:
                    f.write(b'DNA_PLACEHOLDER')  # Placeholder content
                print(f"Placeholder DNA file created at: {dna_file_path}")
        except Exception as e:
            raise Exception(f"Failed to create DNA file: {e}")

        return dna_file_path

# Classes to register
classes = [
    DNA_OT_SendToUnreal,
]

def register():
    """Register the operator classes"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister the operator classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)

if __name__ == "__main__":
    register()
