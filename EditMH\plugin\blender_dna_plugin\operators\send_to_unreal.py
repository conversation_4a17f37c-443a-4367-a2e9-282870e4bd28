"""
Send to Unreal operator for the Blender MetaHuman DNA Plugin.
"""

import bpy
from bpy.types import Operator
from pathlib import Path

# Import our utility functions
from ..utils.output_utils import (
    update_output_items,
    update_material_slot_mappings,
    get_included_objects,
    get_lod_indexes,
    get_objects_for_lod,
    get_lod_index
)
from ..utils.dna_export_utils import export_dna_file, get_character_name_from_mesh

# Try to import the DNA modules
try:
    import dna
    DNA_MODULES_AVAILABLE = True
except ImportError:
    DNA_MODULES_AVAILABLE = False

class DNA_OT_SendToUnreal(Operator):
    """Exports the MetaHuman DNA, SkeletalMesh, and Textures, then imports them into Unreal Engine. This requires the Send to Unreal addon to be installed"""
    bl_idname = "dna.send_to_unreal"
    bl_label = "Send to Unreal"
    bl_description = "Send the MetaHuman to Unreal Engine using the Send2UE addon"
    bl_options = {'REGISTER', 'UNDO'}

    @classmethod
    def poll(cls, context):
        """Check if the operator can be executed"""
        # Check if send2ue addon is available
        send2ue_properties = getattr(bpy.context.scene, 'send2ue', None)
        if not send2ue_properties:
            return False

        # Check if DNA is loaded
        dna_tools = context.scene.dna_tools
        if not dna_tools.is_dna_loaded:
            return False

        # Check if we have a head mesh (look for head_lod0_mesh or similar)
        head_mesh_found = False
        for obj in bpy.data.objects:
            if obj.type == 'MESH' and 'head' in obj.name.lower() and 'lod0' in obj.name.lower():
                head_mesh_found = True
                break

        if not head_mesh_found:
            return False

        return True

    def execute(self, context):
        """Execute the send to unreal operation"""
        # Check if send2ue addon is installed
        send2ue_properties = getattr(bpy.context.scene, 'send2ue', None)
        if not send2ue_properties:
            self.report({'ERROR'}, 'The Send to Unreal addon is not installed!')
            return {'CANCELLED'}

        dna_tools = context.scene.dna_tools

        # Update output items to ensure we have the latest scene state
        update_output_items()
        update_material_slot_mappings()

        # Get the head mesh and rig references
        head_mesh = dna_tools.head_mesh
        head_rig = dna_tools.head_rig

        if not head_mesh:
            self.report({'ERROR'}, 'Head mesh not found! Please ensure a MetaHuman model is loaded.')
            return {'CANCELLED'}

        if not head_rig:
            self.report({'ERROR'}, 'Head rig not found! Please ensure a MetaHuman armature is loaded.')
            return {'CANCELLED'}

        # Export a separate DNA file since we are only going to export bone
        # transforms, mesh are sent across via FBX
        try:
            character_name = get_character_name_from_mesh(head_mesh.name)
            dna_file_name = f'{character_name}.dna'

            dna_file_path = export_dna_file(
                dna_tools=dna_tools,
                meshes=False,      # Only bones
                bones=True,        # Export bone transforms
                vertex_colors=False,
                file_name=dna_file_name
            )
            print(f"DNA file exported to: {dna_file_path}")

            # Check if we used fallback export
            if not DNA_MODULES_AVAILABLE:
                self.report({'WARNING'}, 'DNA file exported using fallback method. For full DNA processing, install DNA SDK.')

        except Exception as e:
            # Provide more helpful error message
            error_msg = str(e)
            if "DNA modules not available" in error_msg:
                error_msg = "DNA SDK not available. Using fallback export method."
                self.report({'WARNING'}, error_msg)
                # Continue with export even if DNA modules aren't available
            else:
                self.report({'ERROR'}, f'Failed to export DNA file: {error_msg}')
                return {'CANCELLED'}

        # Ensure the RPC response timeout is at least long enough to
        # import the metahuman meshes since they can be quite large.
        send2ue_addon_preferences = bpy.context.preferences.addons.get('send2ue')
        if send2ue_addon_preferences and hasattr(send2ue_addon_preferences, 'preferences'):
            if hasattr(send2ue_addon_preferences.preferences, 'rpc_response_timeout'):
                if send2ue_addon_preferences.preferences.rpc_response_timeout < 180:
                    send2ue_addon_preferences.preferences.rpc_response_timeout = 180

        # Set the active settings template to the face settings
        try:
            # Load the MetaHuman DNA settings template
            template_path = Path(__file__).parent.parent / 'resources' / 'send2ue' / 'meta-human_dna.json'
            if template_path.exists():
                bpy.ops.send2ue.load_template(filepath=str(template_path))
                bpy.context.scene.send2ue.active_settings_template = 'meta-human_dna'
        except Exception as e:
            print(f"Warning: Could not load send2ue template: {e}")

        # Get included objects from output item list (matching example implementation)
        included_objects = get_included_objects()

        # Ensure head mesh and rig are included
        if head_mesh not in included_objects:
            included_objects.append(head_mesh)
        if head_rig not in included_objects:
            included_objects.append(head_rig)

        # Override what objects are collected by the send2ue
        bpy.context.window_manager.send2ue.object_collection_override.clear()
        bpy.context.window_manager.send2ue.object_collection_override.extend(included_objects)
        bpy.context.view_layer.objects.active = head_mesh

        # Enable the MetaHuman DNA extension if available
        try:
            if hasattr(bpy.context.scene.send2ue.extensions, 'meta_human_dna'):
                bpy.context.scene.send2ue.extensions.meta_human_dna.enabled = True
                print("MetaHuman DNA extension enabled for this export")
            else:
                print("MetaHuman DNA extension not found - using basic export")
        except Exception as e:
            print(f"Could not enable MetaHuman DNA extension: {e}")

        try:
            # Run send to unreal
            bpy.ops.wm.send2ue('INVOKE_DEFAULT')
            self.report({'INFO'}, "Successfully sent to Unreal Engine")

        except Exception as e:
            self.report({'ERROR'}, f"Failed to send to Unreal Engine: {str(e)}")
            return {'CANCELLED'}

        return {'FINISHED'}



# Classes to register
classes = [
    DNA_OT_SendToUnreal,
]

def register():
    """Register the operator classes"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister the operator classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)

if __name__ == "__main__":
    register()
