"""
Send2UE Settings UI panels for the Blender MetaHuman DNA Plugin.
Matches the example implementation exactly.
"""

import bpy
from bpy.types import Panel


def send2ue_addon_is_valid():
    """Check if Send2UE addon is valid and available"""
    try:
        send2ue_properties = getattr(bpy.context.scene, 'send2ue', None)
        if not send2ue_properties:
            return False
        
        # Check for minimum version requirement (2.6.0 or greater)
        send2ue_addon_preferences = bpy.context.preferences.addons.get('send2ue')
        if send2ue_addon_preferences:
            # For now, assume it's valid if the addon is installed
            return True
        return False
    except:
        return False


class DNA_PT_Send2UESettings(Panel):
    """Send to Unreal Settings panel (matching example exactly)"""
    bl_label = "Send to Unreal Settings"
    bl_idname = "DNA_PT_send2ue_settings"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'MetaHuman DNA'
    bl_parent_id = "DNA_PT_output"
    bl_options = {'DEFAULT_CLOSED'}

    @classmethod
    def poll(cls, context):
        """Only show if Send2UE addon is available and DNA is loaded"""
        dna_tools = context.scene.dna_tools
        if not dna_tools.is_dna_loaded:
            return False
        return send2ue_addon_is_valid()

    def draw(self, context):
        layout = self.layout
        dna_tools = context.scene.dna_tools
        
        # Check if Send2UE addon is installed
        if not getattr(context.scene, 'send2ue', None):
            row = layout.row()
            row.alert = True
            row.label(
                text='Send to Unreal Addon must be installed and enabled', 
                icon='ERROR'
            )
            return        
        
        # Check if Send2UE addon version is valid
        if not send2ue_addon_is_valid():
            row = layout.row()
            row.alert = True
            row.label(
                text='Send to Unreal Addon version 2.6.0 or greater is required.', 
                icon='ERROR'
            )
            return

        # Settings Template
        row = layout.row()
        row.label(text='Settings Template:')
        row = layout.row()
        row.prop(dna_tools, 'send2ue_settings_template', text='')
        
        # Copy Supporting Unreal Assets
        row = layout.row()
        row.prop(dna_tools, 'unreal_copy_assets')
        
        # Content Folder (Unreal)
        row = layout.row()
        row.label(text='Content Folder (Unreal):')
        row = layout.row()
        row.prop(dna_tools, 'unreal_content_folder', text='')
        
        # Blueprint Asset (Unreal)
        row = layout.row()
        row.label(text='Blueprint Asset (Unreal):')
        row = layout.row()
        row.prop(dna_tools, 'auto_sync_spine_with_body')
        row = layout.row()
        row.prop(dna_tools, 'unreal_blueprint_asset_path', text='')
        
        # Level Sequence Asset (Unreal)
        row = layout.row()
        row.label(text='Level Sequence Asset (Unreal):')
        row = layout.row()
        row.prop(dna_tools, 'unreal_level_sequence_asset_path', text='')
        
        # Face Control Rig Asset (Unreal)
        row = layout.row()
        row.label(text='Face Control Rig Asset (Unreal):')
        row = layout.row()
        row.prop(dna_tools, 'unreal_face_control_rig_asset_path', text='')
        
        # Face Anim BP Asset (Unreal)
        row = layout.row()
        row.label(text='Face Anim BP Asset (Unreal):')
        row = layout.row()
        row.prop(dna_tools, 'unreal_face_anim_bp_asset_path', text='')
        
        # Material Slot to Unreal Material Instance
        row = layout.row()
        row.label(text='Material Slot to Unreal Material Instance:')
        row = layout.row()
        
        # Material slot mappings list
        col = row.column()
        col.template_list(
            "DNA_UL_MaterialSlotMappings", "",
            dna_tools, "material_slot_mappings",
            dna_tools, "material_slot_active_index",
            rows=3
        )
        
        # Buttons for material slot management
        col = row.column(align=True)
        col.operator('dna.refresh_material_slot_names', icon='FILE_REFRESH', text='')
        col.operator('dna.revert_material_slot_values', icon='LOOP_BACK', text='')


class DNA_UL_MaterialSlotMappings(bpy.types.UIList):
    """UI List for material slot mappings (matching example)"""
    
    def draw_item(self, context, layout, data, item, icon, active_data, active_propname):
        if self.layout_type in {'DEFAULT', 'COMPACT'}:
            row = layout.row(align=True)
            row.prop(item, "name", text="", emboss=False)
            row.prop(item, "asset_path", text="", emboss=False)
            if not item.valid_path:
                row.alert = True
        elif self.layout_type in {'GRID'}:
            layout.alignment = 'CENTER'
            layout.label(text="", icon_value=icon)


# Classes to register
classes = [
    DNA_PT_Send2UESettings,
    DNA_UL_MaterialSlotMappings,
]


def register():
    """Register the UI classes"""
    for cls in classes:
        bpy.utils.register_class(cls)


def unregister():
    """Unregister the UI classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)


if __name__ == "__main__":
    register()
