"""
Main plugin module for the Blender MetaHuman DNA Plugin.
"""

import bpy

# Import submodules
from . import operators
from . import panels
from . import properties
from . import utils

# Classes to register
classes = []

def install_send2ue_extension():
    """Install our extension to Send2UE's extensions directory"""
    try:
        import os
        import shutil
        from pathlib import Path

        # Check if Send2UE is available
        send2ue_addon = bpy.context.preferences.addons.get('send2ue')
        if not send2ue_addon:
            print("Send2UE addon not found, skipping extension installation")
            return

        # Get Send2UE extensions directory
        try:
            from send2ue.constants import Extensions
            ext_constants = Extensions()
            send2ue_extensions_dir = Path(ext_constants.FOLDER)
        except:
            # Fallback to manual path construction
            send2ue_path = Path(send2ue_addon.module.__file__).parent
            send2ue_extensions_dir = send2ue_path / 'resources' / 'extensions'

        # Get our extension file
        our_extension_file = Path(__file__).parent / 'resources' / 'send2ue' / 'meta_human_dna_extension.py'
        target_extension_file = send2ue_extensions_dir / 'meta_human_dna_extension.py'

        # Check if our extension file exists
        if not our_extension_file.exists():
            print(f"Extension file not found: {our_extension_file}")
            return

        # Create extensions directory if it doesn't exist
        send2ue_extensions_dir.mkdir(parents=True, exist_ok=True)

        # Copy our extension to Send2UE extensions directory
        if not target_extension_file.exists() or our_extension_file.stat().st_mtime > target_extension_file.stat().st_mtime:
            shutil.copy2(our_extension_file, target_extension_file)
            print(f"MetaHuman DNA extension installed to Send2UE: {target_extension_file}")

            # Reload Send2UE to discover the extension
            try:
                bpy.ops.preferences.addon_disable(module='send2ue')
                bpy.ops.preferences.addon_enable(module='send2ue')
                print("Send2UE reloaded to discover MetaHuman DNA extension")
            except:
                print("Could not reload Send2UE automatically - restart Blender to activate extension")
        else:
            print("MetaHuman DNA extension already up to date in Send2UE")

    except Exception as e:
        print(f"Error installing Send2UE extension: {e}")

def register():
    """Register the plugin modules"""
    # Register properties
    properties.register()

    # Register operators
    operators.register()

    # Register panels
    panels.register()

    # Register utils
    utils.register()

    # Register classes
    for cls in classes:
        bpy.utils.register_class(cls)

    # Install Send2UE extension
    install_send2ue_extension()

def remove_send2ue_extension():
    """Remove our extension from Send2UE's extensions directory"""
    try:
        from pathlib import Path

        # Check if Send2UE is available
        send2ue_addon = bpy.context.preferences.addons.get('send2ue')
        if not send2ue_addon:
            return

        # Get Send2UE extensions directory
        try:
            from send2ue.constants import Extensions
            ext_constants = Extensions()
            send2ue_extensions_dir = Path(ext_constants.FOLDER)
        except:
            # Fallback to manual path construction
            send2ue_path = Path(send2ue_addon.module.__file__).parent
            send2ue_extensions_dir = send2ue_path / 'resources' / 'extensions'

        # Remove our extension file
        target_extension_file = send2ue_extensions_dir / 'meta_human_dna_extension.py'
        if target_extension_file.exists():
            target_extension_file.unlink()
            print(f"MetaHuman DNA extension removed from Send2UE: {target_extension_file}")

            # Also remove compiled Python cache
            cache_file = send2ue_extensions_dir / '__pycache__' / 'meta_human_dna_extension.cpython-311.pyc'
            if cache_file.exists():
                cache_file.unlink()

    except Exception as e:
        print(f"Error removing Send2UE extension: {e}")

def unregister():
    """Unregister the plugin modules"""
    # Remove Send2UE extension
    remove_send2ue_extension()

    # Unregister classes
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)

    # Unregister utils
    utils.unregister()

    # Unregister panels
    panels.unregister()

    # Unregister operators
    operators.unregister()

    # Unregister properties
    properties.unregister()
