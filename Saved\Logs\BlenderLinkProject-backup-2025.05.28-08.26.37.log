﻿Log file open, 05/28/25 13:16:35
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=35924)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: BlenderLinkProject
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\Plugins\BlenderLinkProject\BlenderLinkProject.uproject""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.267034
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-9D3FD7374B554501FDD9AA8BAAA5758D
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/Plugins/BlenderLinkProject/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading VulkanPC ini files took 0.04 seconds
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading IOS ini files took 0.05 seconds
LogConfig: Display: Loading Mac ini files took 0.05 seconds
LogConfig: Display: Loading Android ini files took 0.05 seconds
LogConfig: Display: Loading TVOS ini files took 0.05 seconds
LogConfig: Display: Loading Unix ini files took 0.05 seconds
LogConfig: Display: Loading VisionOS ini files took 0.05 seconds
LogConfig: Display: Loading Windows ini files took 0.05 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.06 seconds
LogConfig: Display: Loading Linux ini files took 0.06 seconds
LogAssetRegistry: Display: Asset registry cache read as 43.8 MiB from H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin Mutable
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaIOFramework
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SkeletalMerging
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin XRBase
LogPluginManager: Mounting Engine plugin CaptureData
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin LensComponent
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin OpenColorIO
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin LiveLinkControlRig
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RigLogicMutable
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin MetaHuman
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin ARUtilities
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin AppleARKit
LogPluginManager: Mounting Engine plugin AppleARKitFaceSupport
LogPluginManager: Mounting Project plugin BlenderLink
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 2147483647 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: AMD Radeon RX 6900 XT
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: 
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.58ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'SkeletalMerging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XRBase' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CaptureData' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.39ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LiveLinkControlRig' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogicMutable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'MetaHuman' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ARUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AppleARKit' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKitFaceSupport' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'BlenderLink' had been unloaded. Reloading on-demand took 0.07ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.05.28-07.46.35:648][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.05.28-07.46.35:648][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.05.28-07.46.35:648][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.05.28-07.46.35:648][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.05.28-07.46.35:648][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.05.28-07.46.35:652][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.05.28-07.46.35:652][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.05.28-07.46.35:652][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.05.28-07.46.35:652][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.05.28-07.46.35:652][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.05.28-07.46.35:652][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.05.28-07.46.35:652][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.05.28-07.46.35:655][  0]LogRHI: Using Default RHI: D3D12
[2025.05.28-07.46.35:655][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.28-07.46.35:655][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.28-07.46.35:659][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.05.28-07.46.35:659][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.28-07.46.35:757][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.05.28-07.46.35:757][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.28-07.46.35:757][  0]LogD3D12RHI:   Adapter has 16338MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 3 output[s]
[2025.05.28-07.46.35:757][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.28-07.46.35:757][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.05.28-07.46.35:912][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.05.28-07.46.35:912][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.28-07.46.35:912][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.28-07.46.35:913][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.05.28-07.46.35:913][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.05.28-07.46.35:922][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.05.28-07.46.35:922][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.05.28-07.46.35:922][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.28-07.46.35:922][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.05.28-07.46.35:922][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.05.28-07.46.35:922][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.28-07.46.35:923][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.05.28-07.46.35:923][  0]LogHAL: Display: Platform has ~ 64 GB [68632862720 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.05.28-07.46.35:923][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.05.28-07.46.35:923][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-07.46.35:923][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.05.28-07.46.35:923][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.05.28-07.46.35:923][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.28-07.46.35:923][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.05.28-07.46.35:923][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.05.28-07.46.35:923][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.05.28-07.46.35:923][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.05.28-07.46.35:923][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.05.28-07.46.35:923][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.28-07.46.35:923][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.05.28-07.46.35:923][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.05.28-07.46.35:923][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/Plugins/BlenderLinkProject/Saved/Config/WindowsEditor/Editor.ini]
[2025.05.28-07.46.35:923][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.05.28-07.46.35:923][  0]LogInit: User: Shashank
[2025.05.28-07.46.35:923][  0]LogInit: CPU Page size=4096, Cores=16
[2025.05.28-07.46.35:923][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.05.28-07.46.36:229][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.05.28-07.46.36:229][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.05.28-07.46.36:229][  0]LogMemory: Process Physical Memory: 627.27 MB used, 645.51 MB peak
[2025.05.28-07.46.36:229][  0]LogMemory: Process Virtual Memory: 757.18 MB used, 757.18 MB peak
[2025.05.28-07.46.36:229][  0]LogMemory: Physical Memory: 24298.00 MB used,  41155.40 MB free, 65453.40 MB total
[2025.05.28-07.46.36:229][  0]LogMemory: Virtual Memory: 39566.29 MB used,  29983.11 MB free, 69549.40 MB total
[2025.05.28-07.46.36:229][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.05.28-07.46.36:234][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.05.28-07.46.36:240][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.05.28-07.46.36:240][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.05.28-07.46.36:241][  0]LogInit: Using OS detected language (en-GB).
[2025.05.28-07.46.36:241][  0]LogInit: Using OS detected locale (en-IN).
[2025.05.28-07.46.36:243][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.05.28-07.46.36:243][  0]LogInit: Setting process to per monitor DPI aware
[2025.05.28-07.46.36:567][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.05.28-07.46.36:567][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.05.28-07.46.36:567][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.05.28-07.46.36:581][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.05.28-07.46.36:581][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.05.28-07.46.36:673][  0]LogRHI: Using Default RHI: D3D12
[2025.05.28-07.46.36:673][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.28-07.46.36:673][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.28-07.46.36:673][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.28-07.46.36:673][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.28-07.46.36:673][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.05.28-07.46.36:674][  0]LogWindows: Attached monitors:
[2025.05.28-07.46.36:674][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY8' [PRIMARY]
[2025.05.28-07.46.36:674][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.05.28-07.46.36:674][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY2'
[2025.05.28-07.46.36:674][  0]LogWindows: Found 3 attached monitors.
[2025.05.28-07.46.36:674][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.28-07.46.36:674][  0]LogRHI: RHI Adapter Info:
[2025.05.28-07.46.36:674][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.05.28-07.46.36:674][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.28-07.46.36:674][  0]LogRHI:      Driver Date: 4-25-2025
[2025.05.28-07.46.36:674][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.05.28-07.46.36:706][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.05.28-07.46.36:777][  0]LogNvidiaAftermath: Aftermath initialized
[2025.05.28-07.46.36:777][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.05.28-07.46.36:854][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: Bindless resources are supported
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: Raster order views are supported
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.05.28-07.46.36:854][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.05.28-07.46.36:884][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000AB156E95300)
[2025.05.28-07.46.36:884][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000AB156E95580)
[2025.05.28-07.46.36:884][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000AB156E95800)
[2025.05.28-07.46.36:884][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.05.28-07.46.36:884][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.05.28-07.46.36:884][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.05.28-07.46.36:884][  0]LogRHI: Texture pool is 9809 MB (70% of 14013 MB)
[2025.05.28-07.46.36:884][  0]LogD3D12RHI: Async texture creation enabled
[2025.05.28-07.46.36:884][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.05.28-07.46.36:897][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.05.28-07.46.36:902][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.05.28-07.46.36:909][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all'
[2025.05.28-07.46.36:909][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all" ]
[2025.05.28-07.46.36:928][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.05.28-07.46.36:928][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.05.28-07.46.36:928][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.05.28-07.46.36:928][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.05.28-07.46.36:928][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.05.28-07.46.36:928][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.05.28-07.46.36:928][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.05.28-07.46.36:928][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.05.28-07.46.36:929][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.05.28-07.46.36:954][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.05.28-07.46.36:954][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.05.28-07.46.36:954][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.05.28-07.46.36:954][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.05.28-07.46.36:954][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.05.28-07.46.36:954][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.05.28-07.46.36:954][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.05.28-07.46.36:954][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.05.28-07.46.36:954][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.05.28-07.46.36:954][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.05.28-07.46.36:970][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.05.28-07.46.36:970][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.05.28-07.46.36:985][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.05.28-07.46.36:985][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.05.28-07.46.36:985][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.05.28-07.46.36:985][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.05.28-07.46.37:001][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.05.28-07.46.37:002][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.05.28-07.46.37:002][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.05.28-07.46.37:016][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.05.28-07.46.37:016][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.05.28-07.46.37:016][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.05.28-07.46.37:016][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.05.28-07.46.37:032][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.05.28-07.46.37:032][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.05.28-07.46.37:049][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.05.28-07.46.37:049][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.05.28-07.46.37:049][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.05.28-07.46.37:049][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.05.28-07.46.37:049][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.05.28-07.46.37:090][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.05.28-07.46.37:092][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.05.28-07.46.37:092][  0]LogTargetPlatformManager:   SF_METAL
[2025.05.28-07.46.37:092][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.05.28-07.46.37:092][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.05.28-07.46.37:092][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.05.28-07.46.37:092][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.05.28-07.46.37:092][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.05.28-07.46.37:092][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager:   VVM_1_0
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.05.28-07.46.37:093][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.05.28-07.46.37:093][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.05.28-07.46.37:093][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.05.28-07.46.37:094][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.05.28-07.46.37:094][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.05.28-07.46.37:094][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.05.28-07.46.37:094][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.28-07.46.37:094][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.05.28-07.46.37:156][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.05.28-07.46.37:156][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.28-07.46.37:156][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.05.28-07.46.37:157][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.05.28-07.46.37:157][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.28-07.46.37:158][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.28-07.46.37:158][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.05.28-07.46.37:158][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 35276 --child-id Zen_35276_Startup'
[2025.05.28-07.46.37:226][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.05.28-07.46.37:226][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.069 seconds
[2025.05.28-07.46.37:227][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.05.28-07.46.37:232][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.00 seconds.
[2025.05.28-07.46.37:232][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.02ms. RandomReadSpeed=2002.34MBs, RandomWriteSpeed=232.77MBs. Assigned SpeedClass 'Local'
[2025.05.28-07.46.37:233][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.05.28-07.46.37:233][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.05.28-07.46.37:233][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.05.28-07.46.37:233][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.05.28-07.46.37:233][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.05.28-07.46.37:233][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.05.28-07.46.37:233][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.05.28-07.46.37:233][  0]LogShaderCompilers: Guid format shader working directory is 19 characters bigger than the processId version (H:/Plugins/BlenderLinkProject/Intermediate/Shaders/WorkingDirectory/35276/).
[2025.05.28-07.46.37:234][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/E29F7A6E4D6048E2838A92ACA5AE2F72/'.
[2025.05.28-07.46.37:234][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.05.28-07.46.37:234][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.05.28-07.46.37:235][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/Plugins/BlenderLinkProject/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.05.28-07.46.37:235][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.05.28-07.46.37:694][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.05.28-07.46.38:344][  0]LogSlate: Using FreeType 2.10.0
[2025.05.28-07.46.38:344][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.05.28-07.46.38:345][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.28-07.46.38:345][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.28-07.46.38:345][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.28-07.46.38:345][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.28-07.46.38:345][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.28-07.46.38:345][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.28-07.46.38:370][  0]LogAssetRegistry: FAssetRegistry took 0.0026 seconds to start up
[2025.05.28-07.46.38:372][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.05.28-07.46.38:376][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.002s loading caches H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin.
[2025.05.28-07.46.38:376][  0]LogAssetRegistry: Error: Package is unloadable: H:/Plugins/BlenderLinkProject/Content/SM_Rock_ukxmdhuga.uasset. Reason: Invalid value for PACKAGE_FILE_TAG at start of file.
[2025.05.28-07.46.38:575][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-07.46.38:577][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.05.28-07.46.38:577][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.05.28-07.46.38:577][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.05.28-07.46.38:588][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.05.28-07.46.38:588][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.05.28-07.46.38:612][  0]LogDeviceProfileManager: Active device profile: [00000AB17565F200][00000AB1737F0000 66] WindowsEditor
[2025.05.28-07.46.38:612][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.05.28-07.46.38:613][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.05.28-07.46.38:615][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.05.28-07.46.38:616][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.05.28-07.46.38:653][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:653][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.05.28-07.46.38:653][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-07.46.38:653][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:653][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.05.28-07.46.38:653][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-07.46.38:653][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:654][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.05.28-07.46.38:654][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-07.46.38:654][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:654][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.05.28-07.46.38:654][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-07.46.38:654][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:654][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:654][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.05.28-07.46.38:654][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-07.46.38:655][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:655][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.05.28-07.46.38:655][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-07.46.38:655][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:655][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:655][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.05.28-07.46.38:655][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-07.46.38:655][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:655][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.05.28-07.46.38:655][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-07.46.38:655][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:655][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-07.46.38:656][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:656][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.05.28-07.46.38:656][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.28-07.46.38:656][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:656][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.05.28-07.46.38:656][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.28-07.46.38:656][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-07.46.38:657][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.05.28-07.46.38:657][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-07.46.38:657][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:657][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.05.28-07.46.38:657][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-07.46.38:657][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.05.28-07.46.38:657][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-07.46.38:657][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:657][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.05.28-07.46.38:657][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-07.46.38:657][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.05.28-07.46.38:657][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-07.46.38:657][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-07.46.38:657][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.05.28-07.46.38:657][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-07.46.38:817][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.05.28-07.46.38:817][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.05.28-07.46.38:817][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.05.28-07.46.38:817][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.05.28-07.46.38:817][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.05.28-07.46.38:968][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.89ms
[2025.05.28-07.46.39:003][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.90ms
[2025.05.28-07.46.39:021][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 1.01ms
[2025.05.28-07.46.39:023][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.87ms
[2025.05.28-07.46.39:289][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.05.28-07.46.39:289][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.05.28-07.46.39:301][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.05.28-07.46.39:301][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.05.28-07.46.39:305][  0]LogLiveCoding: Display: First instance in process group "UE_BlenderLinkProject_0xe99fe6f9", spawning console
[2025.05.28-07.46.39:310][  0]LogLiveCoding: Display: Waiting for server
[2025.05.28-07.46.39:329][  0]LogSlate: Border
[2025.05.28-07.46.39:329][  0]LogSlate: BreadcrumbButton
[2025.05.28-07.46.39:329][  0]LogSlate: Brushes.Title
[2025.05.28-07.46.39:329][  0]LogSlate: Default
[2025.05.28-07.46.39:329][  0]LogSlate: Icons.Save
[2025.05.28-07.46.39:329][  0]LogSlate: Icons.Toolbar.Settings
[2025.05.28-07.46.39:329][  0]LogSlate: ListView
[2025.05.28-07.46.39:329][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.05.28-07.46.39:330][  0]LogSlate: SoftwareCursor_Grab
[2025.05.28-07.46.39:330][  0]LogSlate: TableView.DarkRow
[2025.05.28-07.46.39:330][  0]LogSlate: TableView.Row
[2025.05.28-07.46.39:330][  0]LogSlate: TreeView
[2025.05.28-07.46.39:403][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.05.28-07.46.39:416][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.05.28-07.46.39:418][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 2.072 ms
[2025.05.28-07.46.39:428][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.54ms
[2025.05.28-07.46.39:447][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.05.28-07.46.39:447][  0]LogInit: XR: MultiViewport is Disabled
[2025.05.28-07.46.39:447][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.05.28-07.46.39:447][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.05.28-07.46.40:022][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.05.28-07.46.40:022][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.05.28-07.46.40:022][  0]LogNNERuntimeORT: 1: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.05.28-07.46.40:022][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.05.28-07.46.40:022][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.05.28-07.46.40:183][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.05.28-07.46.40:183][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.05.28-07.46.40:200][  0]LogMetaSound: MetaSound Engine Initialized
[2025.05.28-07.46.40:373][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.05.28-07.46.40:381][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.28-07.46.40:381][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.28-07.46.40:413][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.55ms
[2025.05.28-07.46.40:553][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: B2A64808D53C43628000000000000300 | Instance: 6917F0D74899E97D567C18A71388CD3F (DESKTOP-E41IK6R-35276).
[2025.05.28-07.46.40:626][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.05.28-07.46.40:631][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.05.28-07.46.40:632][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group *********:6666.
[2025.05.28-07.46.40:632][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:60547'.
[2025.05.28-07.46.40:634][  0]LogUdpMessaging: Display: Added local interface '192.168.1.12' to multicast group '*********:6666'
[2025.05.28-07.46.40:634][  0]LogUdpMessaging: Display: Added local interface '172.27.16.1' to multicast group '*********:6666'
[2025.05.28-07.46.40:717][  0]LogMutable: Creating Mutable Customizable Object System.
[2025.05.28-07.46.40:761][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.28-07.46.40:761][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.28-07.46.40:811][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.05.28-07.46.40:820][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.05.28-07.46.40:836][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.28-07.46.40:836][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.28-07.46.40:937][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.05.28-07.46.40:937][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.05.28-07.46.40:937][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.05.28-07.46.40:937][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.05.28-07.46.40:938][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.05.28-07.46.40:938][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.05.28-07.46.40:939][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.05.28-07.46.40:939][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.05.28-07.46.40:939][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.05.28-07.46.40:940][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.05.28-07.46.40:941][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.05.28-07.46.40:941][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.05.28-07.46.40:941][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.05.28-07.46.40:941][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.05.28-07.46.40:942][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.05.28-07.46.40:942][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.05.28-07.46.40:944][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.05.28-07.46.40:944][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.05.28-07.46.40:945][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.05.28-07.46.40:946][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.05.28-07.46.40:946][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.05.28-07.46.40:947][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.05.28-07.46.40:947][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.05.28-07.46.40:947][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.05.28-07.46.40:948][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.05.28-07.46.40:948][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.05.28-07.46.40:948][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.05.28-07.46.40:949][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.05.28-07.46.40:949][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.05.28-07.46.40:949][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.05.28-07.46.40:950][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.05.28-07.46.40:950][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.05.28-07.46.40:951][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.05.28-07.46.40:951][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.05.28-07.46.40:951][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.05.28-07.46.41:156][  0]SourceControl: Revision control is disabled
[2025.05.28-07.46.41:186][  0]SourceControl: Revision control is disabled
[2025.05.28-07.46.41:218][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.57ms
[2025.05.28-07.46.41:226][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.56ms
[2025.05.28-07.46.41:766][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.05.28-07.46.42:257][  0]LogSkeletalMesh: Building Skeletal Mesh Face_Archetype...
[2025.05.28-07.46.42:362][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (Face_Archetype) ...
[2025.05.28-07.46.49:588][  0]LogSkeletalMesh: Skeletal mesh [/MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-07.46.49:605][  0]LogSkeletalMesh: Built Skeletal Mesh [7.35s] /MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype
[2025.05.28-07.46.49:626][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.28-07.46.49:627][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.28-07.46.49:628][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.28-07.46.49:628][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.28-07.46.49:628][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.28-07.46.49:628][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.28-07.46.49:636][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.28-07.46.49:636][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.28-07.46.49:695][  0]LogCollectionManager: Loaded 0 collections in 0.000952 seconds
[2025.05.28-07.46.49:699][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Saved/Collections/' took 0.00s
[2025.05.28-07.46.49:699][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Developers/Shashank/Collections/' took 0.00s
[2025.05.28-07.46.49:702][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Collections/' took 0.00s
[2025.05.28-07.46.49:766][  0]LogBlenderLink: Initializing BlenderLink socket listener
[2025.05.28-07.46.49:766][  0]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.28-07.46.49:766][  0]LogBlenderLink: Set socket buffer sizes to 524288 bytes
[2025.05.28-07.46.49:766][  0]LogBlenderLink: Binding socket to 127.0.0.1:2907
[2025.05.28-07.46.49:766][  0]LogBlenderLink: Warning: Socket bind success: true
[2025.05.28-07.46.49:766][  0]LogBlenderLink: Warning: Socket listen success: true
[2025.05.28-07.46.49:766][  0]LogBlenderLink: Waiting for client connection...
[2025.05.28-07.46.49:788][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.28-07.46.49:788][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.28-07.46.49:789][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.28-07.46.49:789][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.28-07.46.49:789][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.28-07.46.49:789][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.28-07.46.49:795][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.28-07.46.49:795][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.28-07.46.49:821][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-05-28T07:46:49.821Z using C
[2025.05.28-07.46.49:822][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=BlenderLinkProject, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.05.28-07.46.49:822][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.28-07.46.49:822][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.05.28-07.46.49:843][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.05.28-07.46.49:843][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.05.28-07.46.49:843][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.05.28-07.46.49:843][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000056
[2025.05.28-07.46.49:843][  0]LogFab: Display: Logging in using persist
[2025.05.28-07.46.49:844][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.05.28-07.46.49:883][  0]LogUObjectArray: 52283 objects as part of root set at end of initial load.
[2025.05.28-07.46.49:883][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.05.28-07.46.49:897][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 38255 public script object entries (1030.17 KB)
[2025.05.28-07.46.49:897][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.05.28-07.46.50:023][  0]LogEngine: Initializing Engine...
[2025.05.28-07.46.50:027][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.05.28-07.46.50:027][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.05.28-07.46.50:111][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.05.28-07.46.50:127][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.05.28-07.46.50:142][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.05.28-07.46.50:142][  0]LogInit: Texture streaming: Enabled
[2025.05.28-07.46.50:153][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.05.28-07.46.50:166][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.05.28-07.46.50:172][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.05.28-07.46.50:172][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.05.28-07.46.50:173][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.05.28-07.46.50:173][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.05.28-07.46.50:173][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.05.28-07.46.50:173][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.05.28-07.46.50:173][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.05.28-07.46.50:173][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.05.28-07.46.50:173][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.05.28-07.46.50:173][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.05.28-07.46.50:173][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.05.28-07.46.50:173][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.05.28-07.46.50:173][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.05.28-07.46.50:173][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.05.28-07.46.50:173][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.05.28-07.46.50:179][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.05.28-07.46.50:245][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter Input (VB-Audio Voicemeeter VAIO)
[2025.05.28-07.46.50:247][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.05.28-07.46.50:248][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.05.28-07.46.50:248][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.05.28-07.46.50:251][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.05.28-07.46.50:251][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.05.28-07.46.50:254][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.05.28-07.46.50:254][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.05.28-07.46.50:254][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.05.28-07.46.50:254][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.05.28-07.46.50:254][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.05.28-07.46.50:263][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.05.28-07.46.50:266][  0]LogInit: Undo buffer set to 256 MB
[2025.05.28-07.46.50:266][  0]LogInit: Transaction tracking system initialized
[2025.05.28-07.46.50:280][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded H:/Plugins/BlenderLinkProject/Saved/SourceControl/UncontrolledChangelists.json
[2025.05.28-07.46.50:339][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.59ms
[2025.05.28-07.46.50:340][  0]LocalizationService: Localization service is disabled
[2025.05.28-07.46.50:355][  0]LogTimingProfiler: Initialize
[2025.05.28-07.46.50:356][  0]LogTimingProfiler: OnSessionChanged
[2025.05.28-07.46.50:356][  0]LoadingProfiler: Initialize
[2025.05.28-07.46.50:356][  0]LoadingProfiler: OnSessionChanged
[2025.05.28-07.46.50:356][  0]LogNetworkingProfiler: Initialize
[2025.05.28-07.46.50:356][  0]LogNetworkingProfiler: OnSessionChanged
[2025.05.28-07.46.50:356][  0]LogMemoryProfiler: Initialize
[2025.05.28-07.46.50:356][  0]LogMemoryProfiler: OnSessionChanged
[2025.05.28-07.46.50:515][  0]LogAutoReimportManager: Warning: Unable to watch directory H:/Plugins/BlenderLinkProject/Content/ as it will conflict with another watching H:/Plugins/BlenderLinkProject/Content/.
[2025.05.28-07.46.50:527][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/' took 0.01s
[2025.05.28-07.46.50:576][  0]LogPython: Using Python 3.11.8
[2025.05.28-07.46.51:750][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.05.28-07.46.51:766][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.05.28-07.46.51:881][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.05.28-07.46.51:881][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.05.28-07.46.51:894][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.05.28-07.46.51:925][  0]LogEditorDataStorage: Initializing
[2025.05.28-07.46.51:926][  0]LogEditorDataStorage: Initialized
[2025.05.28-07.46.51:928][  0]LogWindows: Attached monitors:
[2025.05.28-07.46.51:928][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY8' [PRIMARY]
[2025.05.28-07.46.51:928][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.05.28-07.46.51:928][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY2'
[2025.05.28-07.46.51:928][  0]LogWindows: Found 3 attached monitors.
[2025.05.28-07.46.51:928][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.28-07.46.51:931][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.05.28-07.46.51:944][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.05.28-07.46.51:947][  0]SourceControl: Revision control is disabled
[2025.05.28-07.46.51:947][  0]LogUnrealEdMisc: Loading editor; pre map load, took 17.105
[2025.05.28-07.46.51:948][  0]Cmd: MAP LOAD FILE="H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/TestLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.05.28-07.46.51:950][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.05.28-07.46.51:950][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-07.46.52:003][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.05.28-07.46.52:005][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.77ms
[2025.05.28-07.46.52:011][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'TestLevel'.
[2025.05.28-07.46.52:011][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world TestLevel
[2025.05.28-07.46.52:013][  0]LogWorldPartition: ULevel::OnLevelLoaded(TestLevel)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.05.28-07.46.52:013][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.05.28-07.46.52:013][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.05.28-07.46.52:799][  0]LogAssetRegistry: Display: Asset registry cache written as 43.8 MiB to H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin
[2025.05.28-07.46.55:312][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-07.46.55:317][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-07.46.55:319][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-07.46.55:320][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.05.28-07.46.55:320][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.05.28-07.46.55:320][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-07.46.55:322][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.05.28-07.46.57:499][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.05.28-07.46.57:557][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.05.28-07.46.58:016][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-07.46.58:021][  0]LogSkeletalMesh: Built Skeletal Mesh [0.46s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.05.28-07.46.58:036][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.05.28-07.46.58:037][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.05.28-07.46.58:453][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-07.46.58:455][  0]LogSkeletalMesh: Built Skeletal Mesh [0.42s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.05.28-07.46.59:599][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-07.46.59:604][  0]LogSkeletalMesh: Built Skeletal Mesh [1.57s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.05.28-07.46.59:762][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.05.28-07.46.59:994][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.05.28-07.47.00:018][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-07.47.00:025][  0]LogSkeletalMesh: Built Skeletal Mesh [0.26s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.05.28-07.47.00:361][  0]LogWorldPartition: Display: WorldPartition initialize took 8.34 sec
[2025.05.28-07.47.00:442][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.05.28-07.47.05:871][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-07.47.05:889][  0]LogSkeletalMesh: Built Skeletal Mesh [5.90s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.05.28-07.47.06:606][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.05.28-07.47.06:889][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.21ms
[2025.05.28-07.47.06:890][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.05.28-07.47.06:893][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 1.835ms to complete.
[2025.05.28-07.47.06:903][  0]LogUnrealEdMisc: Total Editor Startup Time, took 32.061
[2025.05.28-07.47.07:071][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.05.28-07.47.07:175][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-07.47.07:226][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-07.47.07:273][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-07.47.07:340][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-07.47.07:380][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-07.47.07:380][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.05.28-07.47.07:380][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-07.47.07:380][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.05.28-07.47.07:380][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-07.47.07:380][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.05.28-07.47.07:380][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-07.47.07:381][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.05.28-07.47.07:381][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-07.47.07:381][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.05.28-07.47.07:382][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-07.47.07:382][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.05.28-07.47.07:382][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-07.47.07:382][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.05.28-07.47.07:382][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-07.47.07:382][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.05.28-07.47.07:382][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-07.47.07:382][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.05.28-07.47.07:382][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-07.47.07:382][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.05.28-07.47.07:431][  0]LogSlate: Took 0.000143 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.05.28-07.47.07:622][  0]LogSlate: Took 0.000260 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.05.28-07.47.07:680][  0]LogSlate: Took 0.000287 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.05.28-07.47.07:719][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.05.28-07.47.07:723][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.05.28-07.47.07:723][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.05.28-07.47.07:723][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.28-07.47.07:801][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.28-07.47.07:801][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.05.28-07.47.07:802][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.05.28-07.47.07:802][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.05.28-07.47.07:802][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.28-07.47.07:869][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.28-07.47.07:869][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.05.28-07.47.08:249][  0]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 44.91 ms. Compile time 27.46 ms, link time 17.02 ms.
[2025.05.28-07.47.08:466][  0]LogStall: Startup...
[2025.05.28-07.47.08:471][  0]LogStall: Startup complete.
[2025.05.28-07.47.08:477][  0]LogLoad: (Engine Initialization) Total time: 33.63 seconds
[2025.05.28-07.47.09:002][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.05.28-07.47.09:002][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.05.28-07.47.09:194][  0]LogSlate: Took 0.000060 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.05.28-07.47.09:213][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-07.47.09:215][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.05.28-07.47.09:215][  0]LogFab: Display: Logging in using exchange code
[2025.05.28-07.47.09:215][  0]LogFab: Display: Reading exchange code from commandline
[2025.05.28-07.47.09:215][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.05.28-07.47.09:215][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.05.28-07.47.09:248][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.05.28-07.47.09:250][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 34.766 ms
[2025.05.28-07.47.09:250][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BlenderLinkProjectEditor Win64 Development
[2025.05.28-07.47.09:429][  1]LogAssetRegistry: AssetRegistryGather time 0.1149s: AssetDataDiscovery 0.0172s, AssetDataGather 0.0156s, StoreResults 0.0820s. Wall time 31.0610s.
	NumCachedDirectories 0. NumUncachedDirectories 1854. NumCachedFiles 7964. NumUncachedFiles 0.
	BackgroundTickInterruptions 0.
[2025.05.28-07.47.09:482][  1]LogSourceControl: Uncontrolled asset enumeration started...
[2025.05.28-07.47.09:482][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.05.28-07.47.09:608][  1]LogSourceControl: Uncontrolled asset enumeration finished in 0.12522 seconds (Found 7940 uncontrolled assets)
[2025.05.28-07.47.09:613][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.05.28-07.47.10:230][ 10]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 20.336702
[2025.05.28-07.47.10:232][ 10]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.05.28-07.47.10:232][ 10]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 20.387075
[2025.05.28-07.47.10:716][ 17]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.28-07.47.11:260][ 24]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 21.352304
[2025.05.28-07.47.11:262][ 24]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 607272702
[2025.05.28-07.47.11:262][ 24]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 21.352304, Update Interval: 300.866119
[2025.05.28-07.47.18:509][323]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.47.28:511][285]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.47.38:516][256]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.47.48:520][217]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.47.58:527][118]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.48.08:531][ 38]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.48.18:533][942]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.48.28:534][870]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.48.37:233][679]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.05.28-07.48.38:534][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.48.48:542][777]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.48.58:542][752]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.49.08:546][723]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.49.18:549][688]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.49.28:553][661]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.49.38:561][637]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.49.48:565][601]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.49.58:578][541]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.50.08:580][445]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.50.18:588][372]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.50.28:596][301]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.50.38:597][229]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.50.48:598][155]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.50.58:602][ 79]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.51.08:610][978]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.51.18:618][888]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.51.28:620][858]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.51.38:624][805]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.51.48:624][731]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.51.58:633][663]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.52.08:634][604]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.52.12:084][926]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 322.241180
[2025.05.28-07.52.12:366][952]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.28-07.52.12:366][952]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 322.513550, Update Interval: 335.541870
[2025.05.28-07.52.18:644][529]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.52.28:650][469]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.52.38:659][372]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.52.48:664][300]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.52.58:665][216]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.53.08:674][173]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.53.18:682][137]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.53.28:691][ 99]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.53.38:700][ 60]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.53.48:701][ 23]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.53.58:705][989]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.54.08:716][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.54.18:717][921]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.54.28:727][893]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.54.38:727][865]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.54.48:734][841]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.54.58:737][817]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.55.08:746][783]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.55.18:752][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.55.28:755][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.55.38:762][680]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.55.48:772][631]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.55.58:775][576]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.56.08:779][543]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.56.18:784][509]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.56.28:790][487]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.56.38:791][465]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.56.47:589][944]LogSlate: Took 0.000094 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.05.28-07.56.48:858][983]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.56.53:022][ 10]LogWebBrowser: Loaded CEF3 version 90.6.7.2358 from D:/UE_5.5/Engine/Binaries/ThirdParty/CEF3/Win64
[2025.05.28-07.56.53:609][ 10]LogCEFBrowser: CEF GPU acceleration enabled
[2025.05.28-07.57.04:467][ 11]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.57.14:470][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.57.24:476][290]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.57.34:477][489]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.57.44:576][422]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.57.50:801][536]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 660.958679
[2025.05.28-07.57.51:159][543]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.28-07.57.51:159][543]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 661.266479, Update Interval: 342.864471
[2025.05.28-07.57.54:609][610]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.58.04:622][805]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.58.14:654][995]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.58.24:709][183]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.58.34:738][375]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.58.44:736][570]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.58.54:781][768]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.59.04:824][964]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.59.14:837][162]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.59.24:866][359]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.59.34:892][556]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.59.44:934][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.59.54:950][948]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-07.59.55:358][956]LogSlate: Window 'Login' being destroyed
[2025.05.28-08.00.01:987][731]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.05.28-08.00.01:987][731]LogWebBrowser: Deleting browser for Url=https://www.epicgames.com/id/login?client_id=b9101103b8814baa9bb4e79e5eb107d0&response_type=code.
[2025.05.28-08.00.04:967][789]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.00.06:897][821]LogSlate: Window 'Login' being destroyed
[2025.05.28-08.00.09:927][170]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.05.28-08.00.09:927][170]LogWebBrowser: Deleting browser for Url=https://www.epicgames.com/id/login?client_id=b9101103b8814baa9bb4e79e5eb107d0&response_type=code.
[2025.05.28-08.00.14:995][266]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.00.25:011][469]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.00.35:056][666]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.00.45:087][862]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.00.55:126][ 62]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.01.05:155][267]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.01.15:175][470]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.01.25:222][668]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.01.35:224][873]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.01.45:235][ 77]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.01.55:277][281]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.02.03:379][446]LogSlate: Window 'Login' being destroyed
[2025.05.28-08.02.05:233][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.02.15:238][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.02.25:244][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.02.35:249][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.02.45:249][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.02.55:249][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.03.05:249][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.03.15:250][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.03.25:251][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.03.35:251][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.03.38:235][809]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1008.392456
[2025.05.28-08.03.38:518][843]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.28-08.03.38:518][843]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1008.667297, Update Interval: 328.827179
[2025.05.28-08.03.45:252][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.03.55:254][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.04.05:254][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.04.15:254][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.04.25:257][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.04.35:258][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.04.45:258][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.04.55:259][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.05.05:260][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.05.15:261][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.05.25:264][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.05.35:266][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.05.45:266][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.05.55:266][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.06.05:266][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.06.15:267][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.06.25:267][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.06.35:269][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.06.45:270][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.06.55:272][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.07.05:272][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.07.15:272][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.07.25:274][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.07.35:275][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.07.45:276][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.07.55:275][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.08.05:276][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.08.15:276][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.08.25:277][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.08.35:277][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.08.45:278][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.08.55:280][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.09.05:281][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.09.15:281][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.09.16:497][397]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1346.650879
[2025.05.28-08.09.16:781][431]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.28-08.09.16:781][431]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1346.926025, Update Interval: 347.634521
[2025.05.28-08.09.25:281][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.09.35:282][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.09.45:281][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.09.55:284][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.10.05:284][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.10.15:284][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.10.25:285][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.10.35:284][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.10.45:287][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.10.55:287][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.11.05:290][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.11.15:290][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.11.25:291][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.11.35:293][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.11.45:293][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.11.55:297][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.12.05:300][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.12.15:302][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.12.25:303][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.12.35:304][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.12.45:305][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.12.55:306][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.13.05:307][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.13.15:309][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.13.25:310][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.13.35:310][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.13.45:311][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.13.55:312][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.14.05:315][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.14.15:314][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.14.25:316][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.14.35:318][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.14.45:318][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.14.55:320][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.15.05:320][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.15.15:320][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.15.25:321][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.15.33:113][586]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1723.274902
[2025.05.28-08.15.33:371][617]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.28-08.15.33:371][617]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1723.525024, Update Interval: 304.517365
[2025.05.28-08.15.35:322][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.15.45:324][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.15.55:325][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.16.05:327][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.16.15:327][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.16.25:330][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.16.35:330][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.16.45:332][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.16.55:334][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.17.05:334][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.17.15:337][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.17.25:341][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.17.35:343][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.17.45:346][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.17.55:348][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.18.05:350][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.18.15:350][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.18.25:353][251]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.18.35:353][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.18.45:354][651]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.18.55:354][851]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.19.05:356][ 51]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field assetType was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field assetType was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isBulkExported was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:690][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:691][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Field isLegacy was not found.
[2025.05.28-08.19.14:692][169]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.05.28-08.19.14:712][169]LogMetaHumanImport: Display: Importing MetaHuman: MHI_Baoli to /Game/MetaHumans/MHI_Baoli
[2025.05.28-08.19.14:778][169]LogStreaming: Display: FlushAsyncLoading(511): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.19.15:415][169]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.05.28-08.19.15:443][169]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.6.dll
[2025.05.28-08.19.15:463][169]LogSkeletalMesh: Building Skeletal Mesh SKM_Face_Preview...
[2025.05.28-08.19.15:765][169]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Face/SKM_Face_Preview.SKM_Face_Preview]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-08.19.15:778][169]LogSkeletalMesh: Built Skeletal Mesh [0.32s] /Game/MetaHumans/Common/Face/SKM_Face_Preview.SKM_Face_Preview
[2025.05.28-08.19.17:783][170]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.19.27:865][663]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.19.37:903][867]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.19.47:943][ 70]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.19.57:947][272]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.20.07:944][475]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.20.17:984][680]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.20.28:033][882]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.20.36:112][ 44]LogSlate: Took 0.000089 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.05.28-08.20.36:272][ 45]LogFactory: FactoryCreateFile: DNAAsset with DNAAssetImportFactory (0 0 H:/Plugins/BlenderLinkProject/Content/MetaHumans/MHI_Baoli/SourceAssets/MHI_Baoli.dna)
[2025.05.28-08.20.45:567][ 45]LogSlate: Window 'DNA Import Options' being destroyed
[2025.05.28-08.20.45:755][ 45]LogUObjectHash: Compacting FUObjectHashTables data took   0.88ms
[2025.05.28-08.20.45:774][ 45]LogUObjectHash: Compacting FUObjectHashTables data took   0.84ms
[2025.05.28-08.20.45:791][ 46]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.20.48:816][ 94]AssetReimport: Importing new asset /Game/MetaHumans/MHI_Baoli/SourceAssets/MHI_Baoli.
[2025.05.28-08.20.48:816][ 94]AssetReimport: Failed to import file H:/Plugins/BlenderLinkProject/Content/MetaHumans/MHI_Baoli/SourceAssets/MHI_Baoli.dna.
[2025.05.28-08.20.55:791][401]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.21.05:792][385]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.21.15:885][798]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.21.20:872][880]LogSlate: Window 'Content Browser' being destroyed
[2025.05.28-08.21.20:967][880]LogSlate: Window 'Content Browser' being destroyed
[2025.05.28-08.21.25:854][ 77]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.21.35:853][996]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.21.38:716][136]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2088.881104
[2025.05.28-08.21.39:234][185]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.28-08.21.39:234][185]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2089.388672, Update Interval: 358.575409
[2025.05.28-08.21.45:861][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.21.55:869][808]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.22.05:879][787]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.22.15:881][762]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.22.25:886][733]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.22.35:887][705]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.22.45:893][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.22.55:902][642]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.23.05:902][612]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.23.15:906][582]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.23.25:913][538]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.23.35:975][397]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.23.40:076][463]Cmd: Interchange.FeatureFlags.Import.FBX False
[2025.05.28-08.23.40:076][463]Interchange.FeatureFlags.Import.FBX = "false"
[2025.05.28-08.23.45:831][  5]LogStreaming: Display: FlushAsyncLoading(595): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.23.45:831][  5]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/cartilage_lod0_mesh_PhysicsAsset (0x8306E6F3EEA7F81A) - The package to load does not exist on disk or in the loader
[2025.05.28-08.23.45:831][  5]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/cartilage_lod0_mesh_PhysicsAsset'
[2025.05.28-08.23.45:908][  5]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/cartilage_lod0_mesh.fbx)
[2025.05.28-08.23.45:915][  5]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/cartilage_lod0_mesh.fbx
[2025.05.28-08.23.45:946][  5]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.23.45:950][  5]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader.MH_Friend_cartilage_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.23.45:968][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27D520A00 with pending SubmitJob call.
[2025.05.28-08.23.45:968][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27D952800 with pending SubmitJob call.
[2025.05.28-08.23.45:968][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB9DC00 with pending SubmitJob call.
[2025.05.28-08.23.45:971][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB9E600 with pending SubmitJob call.
[2025.05.28-08.23.45:971][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27F98F000 with pending SubmitJob call.
[2025.05.28-08.23.45:971][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB98200 with pending SubmitJob call.
[2025.05.28-08.23.45:973][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27D955A00 with pending SubmitJob call.
[2025.05.28-08.23.45:973][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB95A00 with pending SubmitJob call.
[2025.05.28-08.23.45:973][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB9A000 with pending SubmitJob call.
[2025.05.28-08.23.45:974][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27F98DC00 with pending SubmitJob call.
[2025.05.28-08.23.45:974][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB9C800 with pending SubmitJob call.
[2025.05.28-08.23.45:974][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27D957800 with pending SubmitJob call.
[2025.05.28-08.23.45:975][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27D954600 with pending SubmitJob call.
[2025.05.28-08.23.45:975][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB94600 with pending SubmitJob call.
[2025.05.28-08.23.45:976][  5]LogFbx: Triangulating skeletal mesh cartilage_lod0_mesh
[2025.05.28-08.23.45:976][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB97800 with pending SubmitJob call.
[2025.05.28-08.23.45:976][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27D950A00 with pending SubmitJob call.
[2025.05.28-08.23.45:976][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27D950000 with pending SubmitJob call.
[2025.05.28-08.23.45:977][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27D955000 with pending SubmitJob call.
[2025.05.28-08.23.45:978][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB95000 with pending SubmitJob call.
[2025.05.28-08.23.45:987][  5]LogShaderCompilers: Display: Cancelled job 0x00000AB27D953C00 with pending SubmitJob call.
[2025.05.28-08.23.45:998][  5]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.23.46:006][  5]LogSkeletalMesh: Section 0: Material=0, 576 triangles
[2025.05.28-08.23.46:007][  5]LogSkeletalMesh: Building Skeletal Mesh cartilage_lod0_mesh...
[2025.05.28-08.23.46:013][  5]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/cartilage_lod0_mesh.cartilage_lod0_mesh
[2025.05.28-08.23.46:014][  5]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.05.28-08.23.46:021][  5]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.23.46:021][  5]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.23.46:021][  5]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.23.46:022][  5]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.05.28-08.23.46:030][  5]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.23.46:030][  5]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.23.46:030][  5]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.23.46:062][  5]LogUObjectHash: Compacting FUObjectHashTables data took   0.83ms
[2025.05.28-08.23.46:094][  5]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.28-08.23.46:111][  5]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.05.28-08.23.46:126][  5]LogUObjectHash: Compacting FUObjectHashTables data took   0.38ms
[2025.05.28-08.23.46:157][  5]LogSkeletalMesh: Building Skeletal Mesh cartilage_lod0_mesh...
[2025.05.28-08.23.46:173][  5]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (cartilage_lod0_mesh) ...
[2025.05.28-08.23.46:174][  5]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/cartilage_lod0_mesh.cartilage_lod0_mesh
[2025.05.28-08.23.46:175][  5]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.23.46:175][  5]FBXImport: Warning: The bone size is too small to create Physics Asset 'cartilage_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'cartilage_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.23.46:259][  6]LogUObjectHash: Compacting FUObjectHashTables data took   0.73ms
[2025.05.28-08.23.46:272][  6]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.23.46:399][ 10]LogStreaming: Display: FlushAsyncLoading(601): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.23.46:399][ 10]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_PhysicsAsset (0x66E38450F451F47D) - The package to load does not exist on disk or in the loader
[2025.05.28-08.23.46:399][ 10]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_PhysicsAsset'
[2025.05.28-08.23.46:616][ 10]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeEdge_lod0_mesh.fbx)
[2025.05.28-08.23.46:621][ 10]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeEdge_lod0_mesh.fbx
[2025.05.28-08.23.46:623][ 10]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.23.46:625][ 10]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader.MH_Friend_eyeEdge_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.23.46:640][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB9B400 with pending SubmitJob call.
[2025.05.28-08.23.46:640][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB247EC8C00 with pending SubmitJob call.
[2025.05.28-08.23.46:640][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB27D955000 with pending SubmitJob call.
[2025.05.28-08.23.46:640][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB27E338200 with pending SubmitJob call.
[2025.05.28-08.23.46:640][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB1EB5E2800 with pending SubmitJob call.
[2025.05.28-08.23.46:642][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB27E331E00 with pending SubmitJob call.
[2025.05.28-08.23.46:643][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB27D956E00 with pending SubmitJob call.
[2025.05.28-08.23.46:644][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB27D953200 with pending SubmitJob call.
[2025.05.28-08.23.46:644][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB94600 with pending SubmitJob call.
[2025.05.28-08.23.46:645][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB259F79600 with pending SubmitJob call.
[2025.05.28-08.23.46:645][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB259F76E00 with pending SubmitJob call.
[2025.05.28-08.23.46:645][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB247ECD200 with pending SubmitJob call.
[2025.05.28-08.23.46:645][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB259F78C00 with pending SubmitJob call.
[2025.05.28-08.23.46:645][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB1F6471E00 with pending SubmitJob call.
[2025.05.28-08.23.46:647][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB259F73C00 with pending SubmitJob call.
[2025.05.28-08.23.46:648][ 10]LogFbx: Triangulating skeletal mesh eyeEdge_lod0_mesh
[2025.05.28-08.23.46:648][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB259F76400 with pending SubmitJob call.
[2025.05.28-08.23.46:650][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB93200 with pending SubmitJob call.
[2025.05.28-08.23.46:650][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB95000 with pending SubmitJob call.
[2025.05.28-08.23.46:651][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB27D950A00 with pending SubmitJob call.
[2025.05.28-08.23.46:651][ 10]LogShaderCompilers: Display: Cancelled job 0x00000AB259F7A000 with pending SubmitJob call.
[2025.05.28-08.23.46:665][ 10]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.23.46:670][ 10]LogSkeletalMesh: Section 0: Material=0, 386 triangles
[2025.05.28-08.23.46:671][ 10]LogSkeletalMesh: Building Skeletal Mesh eyeEdge_lod0_mesh...
[2025.05.28-08.23.46:675][ 10]LogSkeletalMesh: Built Skeletal Mesh [0.00s] /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh.eyeEdge_lod0_mesh
[2025.05.28-08.23.46:676][ 10]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.05.28-08.23.46:683][ 10]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_2:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.23.46:683][ 10]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.23.46:684][ 10]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.23.46:684][ 10]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.05.28-08.23.46:690][ 10]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_3:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.23.46:690][ 10]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.23.46:690][ 10]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.23.46:707][ 10]LogUObjectHash: Compacting FUObjectHashTables data took   0.84ms
[2025.05.28-08.23.46:740][ 10]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.05.28-08.23.46:757][ 10]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.05.28-08.23.46:774][ 10]LogUObjectHash: Compacting FUObjectHashTables data took   0.38ms
[2025.05.28-08.23.46:777][ 10]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.23.46:777][ 10]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeEdge_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeEdge_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.23.46:800][ 11]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.05.28-08.23.46:926][ 15]LogStreaming: Display: FlushAsyncLoading(607): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.23.46:927][ 15]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_PhysicsAsset (0x3503C37BEAAD4328) - The package to load does not exist on disk or in the loader
[2025.05.28-08.23.46:927][ 15]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_PhysicsAsset'
[2025.05.28-08.23.47:149][ 15]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeLeft_lod0_mesh.fbx)
[2025.05.28-08.23.47:155][ 15]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeLeft_lod0_mesh.fbx
[2025.05.28-08.23.47:159][ 15]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.23.47:161][ 15]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader.MH_Friend_eyeLeft_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.23.47:261][ 15]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-08.23.47:384][ 15]LogFbx: Triangulating skeletal mesh eyeLeft_lod0_mesh
[2025.05.28-08.23.47:408][ 15]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.23.47:434][ 15]LogSkeletalMesh: Section 0: Material=0, 1536 triangles
[2025.05.28-08.23.47:435][ 15]LogSkeletalMesh: Building Skeletal Mesh eyeLeft_lod0_mesh...
[2025.05.28-08.23.47:452][ 15]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeLeft_lod0_mesh) ...
[2025.05.28-08.23.47:457][ 15]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh.eyeLeft_lod0_mesh
[2025.05.28-08.23.47:457][ 15]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.05.28-08.23.47:465][ 15]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_4:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.23.47:465][ 15]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.23.47:465][ 15]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.23.47:466][ 15]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.05.28-08.23.47:473][ 15]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_5:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.23.47:473][ 15]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.23.47:473][ 15]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.23.47:490][ 15]LogUObjectHash: Compacting FUObjectHashTables data took   0.88ms
[2025.05.28-08.23.47:522][ 15]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.28-08.23.47:540][ 15]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.05.28-08.23.47:559][ 15]LogUObjectHash: Compacting FUObjectHashTables data took   0.40ms
[2025.05.28-08.23.47:567][ 15]LogSkeletalMesh: Building Skeletal Mesh eyeLeft_lod0_mesh...
[2025.05.28-08.23.47:583][ 15]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeLeft_lod0_mesh) ...
[2025.05.28-08.23.47:598][ 15]LogSkeletalMesh: Built Skeletal Mesh [0.03s] /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh.eyeLeft_lod0_mesh
[2025.05.28-08.23.47:599][ 15]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.23.47:599][ 15]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeLeft_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeLeft_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.23.47:624][ 16]LogUObjectHash: Compacting FUObjectHashTables data took   0.86ms
[2025.05.28-08.23.47:689][ 19]LogStreaming: Display: FlushAsyncLoading(615): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.23.47:689][ 19]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_PhysicsAsset (0xB68C5B7FAED625F8) - The package to load does not exist on disk or in the loader
[2025.05.28-08.23.47:689][ 19]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_PhysicsAsset'
[2025.05.28-08.23.47:902][ 19]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeRight_lod0_mesh.fbx)
[2025.05.28-08.23.47:907][ 19]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeRight_lod0_mesh.fbx
[2025.05.28-08.23.47:911][ 19]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.23.47:912][ 19]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader.MH_Friend_eyeRight_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.23.47:940][ 19]LogFbxMaterialImport: Warning: Manual texture reimport and recompression may be needed for eyes_normal_map
[2025.05.28-08.23.47:980][ 19]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-08.23.48:098][ 19]LogFbx: Triangulating skeletal mesh eyeRight_lod0_mesh
[2025.05.28-08.23.48:123][ 19]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.23.48:148][ 19]LogSkeletalMesh: Section 0: Material=0, 1536 triangles
[2025.05.28-08.23.48:149][ 19]LogSkeletalMesh: Building Skeletal Mesh eyeRight_lod0_mesh...
[2025.05.28-08.23.48:165][ 19]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeRight_lod0_mesh) ...
[2025.05.28-08.23.48:171][ 19]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh.eyeRight_lod0_mesh
[2025.05.28-08.23.48:172][ 19]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.05.28-08.23.48:180][ 19]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.23.48:180][ 19]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.23.48:180][ 19]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.23.48:180][ 19]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.05.28-08.23.48:187][ 19]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_7:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.23.48:187][ 19]LogWorld: UWorld::CleanupWorld for World_7, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.23.48:187][ 19]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.23.48:204][ 19]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.05.28-08.23.48:238][ 19]LogUObjectHash: Compacting FUObjectHashTables data took   0.80ms
[2025.05.28-08.23.48:254][ 19]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.28-08.23.48:270][ 19]LogUObjectHash: Compacting FUObjectHashTables data took   0.38ms
[2025.05.28-08.23.48:278][ 19]LogSkeletalMesh: Building Skeletal Mesh eyeRight_lod0_mesh...
[2025.05.28-08.23.48:294][ 19]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeRight_lod0_mesh) ...
[2025.05.28-08.23.48:309][ 19]LogSkeletalMesh: Built Skeletal Mesh [0.03s] /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh.eyeRight_lod0_mesh
[2025.05.28-08.23.48:310][ 19]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.23.48:310][ 19]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeRight_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeRight_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.23.48:335][ 20]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.05.28-08.23.48:406][ 23]LogStreaming: Display: FlushAsyncLoading(621): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.23.48:406][ 23]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_PhysicsAsset (0xC8AA4E7A36074A88) - The package to load does not exist on disk or in the loader
[2025.05.28-08.23.48:406][ 23]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_PhysicsAsset'
[2025.05.28-08.23.48:613][ 23]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyelashes_lod0_mesh.fbx)
[2025.05.28-08.23.48:617][ 23]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyelashes_lod0_mesh.fbx
[2025.05.28-08.23.48:621][ 23]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.23.48:622][ 23]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader.MH_Friend_eyelashes_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.23.48:635][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB268F70A00 with pending SubmitJob call.
[2025.05.28-08.23.48:635][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB268F72800 with pending SubmitJob call.
[2025.05.28-08.23.48:636][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB27D523200 with pending SubmitJob call.
[2025.05.28-08.23.48:636][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB1F604BE00 with pending SubmitJob call.
[2025.05.28-08.23.48:637][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB28BC8D200 with pending SubmitJob call.
[2025.05.28-08.23.48:637][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB2677FE600 with pending SubmitJob call.
[2025.05.28-08.23.48:638][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB247EC8C00 with pending SubmitJob call.
[2025.05.28-08.23.48:638][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB268F79600 with pending SubmitJob call.
[2025.05.28-08.23.48:639][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB288FEF000 with pending SubmitJob call.
[2025.05.28-08.23.48:639][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB98200 with pending SubmitJob call.
[2025.05.28-08.23.48:639][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB288FEC800 with pending SubmitJob call.
[2025.05.28-08.23.48:640][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB9F000 with pending SubmitJob call.
[2025.05.28-08.23.48:640][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB247EC5000 with pending SubmitJob call.
[2025.05.28-08.23.48:641][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB288FEAA00 with pending SubmitJob call.
[2025.05.28-08.23.48:641][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB288FED200 with pending SubmitJob call.
[2025.05.28-08.23.48:641][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB247EC6E00 with pending SubmitJob call.
[2025.05.28-08.23.48:641][ 23]LogFbx: Triangulating skeletal mesh eyelashes_lod0_mesh
[2025.05.28-08.23.48:642][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB9B400 with pending SubmitJob call.
[2025.05.28-08.23.48:642][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB99600 with pending SubmitJob call.
[2025.05.28-08.23.48:643][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB288FE9600 with pending SubmitJob call.
[2025.05.28-08.23.48:643][ 23]LogShaderCompilers: Display: Cancelled job 0x00000AB232558C00 with pending SubmitJob call.
[2025.05.28-08.23.48:671][ 23]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.23.48:688][ 23]LogSkeletalMesh: Section 0: Material=0, 1722 triangles
[2025.05.28-08.23.48:689][ 23]LogSkeletalMesh: Building Skeletal Mesh eyelashes_lod0_mesh...
[2025.05.28-08.23.48:700][ 23]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh.eyelashes_lod0_mesh
[2025.05.28-08.23.48:701][ 23]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.05.28-08.23.48:709][ 23]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_8:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.23.48:709][ 23]LogWorld: UWorld::CleanupWorld for World_8, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.23.48:709][ 23]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.23.48:710][ 23]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.05.28-08.23.48:717][ 23]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_9:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.23.48:717][ 23]LogWorld: UWorld::CleanupWorld for World_9, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.23.48:717][ 23]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.23.48:735][ 23]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.05.28-08.23.48:768][ 23]LogUObjectHash: Compacting FUObjectHashTables data took   0.83ms
[2025.05.28-08.23.48:784][ 23]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.28-08.23.48:800][ 23]LogUObjectHash: Compacting FUObjectHashTables data took   0.39ms
[2025.05.28-08.23.48:804][ 23]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.23.48:804][ 23]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyelashes_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyelashes_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.23.48:827][ 24]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.28-08.23.48:947][ 28]LogStreaming: Display: FlushAsyncLoading(627): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.23.48:947][ 28]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_PhysicsAsset (0xA8E5BC927DC4F667) - The package to load does not exist on disk or in the loader
[2025.05.28-08.23.48:947][ 28]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_PhysicsAsset'
[2025.05.28-08.23.49:157][ 28]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeshell_lod0_mesh.fbx)
[2025.05.28-08.23.49:162][ 28]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeshell_lod0_mesh.fbx
[2025.05.28-08.23.49:165][ 28]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.23.49:167][ 28]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader.MH_Friend_eyeshell_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.23.49:177][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB90000 with pending SubmitJob call.
[2025.05.28-08.23.49:177][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB95000 with pending SubmitJob call.
[2025.05.28-08.23.49:178][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB288FEE600 with pending SubmitJob call.
[2025.05.28-08.23.49:178][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB9BE00 with pending SubmitJob call.
[2025.05.28-08.23.49:179][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB255B9F000 with pending SubmitJob call.
[2025.05.28-08.23.49:180][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB9D200 with pending SubmitJob call.
[2025.05.28-08.23.49:181][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB259F76400 with pending SubmitJob call.
[2025.05.28-08.23.49:181][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB288FEB400 with pending SubmitJob call.
[2025.05.28-08.23.49:181][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB288FEDC00 with pending SubmitJob call.
[2025.05.28-08.23.49:181][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB99600 with pending SubmitJob call.
[2025.05.28-08.23.49:182][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB259F7E600 with pending SubmitJob call.
[2025.05.28-08.23.49:182][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB94600 with pending SubmitJob call.
[2025.05.28-08.23.49:182][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB259F76E00 with pending SubmitJob call.
[2025.05.28-08.23.49:185][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB259F78C00 with pending SubmitJob call.
[2025.05.28-08.23.49:185][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB255B9D200 with pending SubmitJob call.
[2025.05.28-08.23.49:185][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB9E600 with pending SubmitJob call.
[2025.05.28-08.23.49:185][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB27FB9F000 with pending SubmitJob call.
[2025.05.28-08.23.49:185][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB255B9DC00 with pending SubmitJob call.
[2025.05.28-08.23.49:186][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB288FE7800 with pending SubmitJob call.
[2025.05.28-08.23.49:186][ 28]LogFbx: Triangulating skeletal mesh eyeshell_lod0_mesh
[2025.05.28-08.23.49:186][ 28]LogShaderCompilers: Display: Cancelled job 0x00000AB247EC5000 with pending SubmitJob call.
[2025.05.28-08.23.49:206][ 28]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.23.49:220][ 28]LogSkeletalMesh: Section 0: Material=0, 980 triangles
[2025.05.28-08.23.49:221][ 28]LogSkeletalMesh: Building Skeletal Mesh eyeshell_lod0_mesh...
[2025.05.28-08.23.49:231][ 28]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh.eyeshell_lod0_mesh
[2025.05.28-08.23.49:233][ 28]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.05.28-08.23.49:240][ 28]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.23.49:240][ 28]LogWorld: UWorld::CleanupWorld for World_10, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.23.49:240][ 28]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.23.49:241][ 28]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.05.28-08.23.49:247][ 28]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_11:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.23.49:247][ 28]LogWorld: UWorld::CleanupWorld for World_11, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.23.49:247][ 28]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.23.49:265][ 28]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.28-08.23.49:298][ 28]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.05.28-08.23.49:315][ 28]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.05.28-08.23.49:332][ 28]LogUObjectHash: Compacting FUObjectHashTables data took   0.41ms
[2025.05.28-08.23.49:335][ 28]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.23.49:335][ 28]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeshell_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeshell_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.23.49:361][ 29]LogUObjectHash: Compacting FUObjectHashTables data took   0.94ms
[2025.05.28-08.23.49:391][ 31]LogStreaming: Display: FlushAsyncLoading(633): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.23.49:391][ 31]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/head_lod0_mesh_PhysicsAsset (0xE7DA988AE0F17ACA) - The package to load does not exist on disk or in the loader
[2025.05.28-08.23.49:391][ 31]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/head_lod0_mesh_PhysicsAsset'
[2025.05.28-08.23.49:647][ 31]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/head_lod0_mesh.fbx)
[2025.05.28-08.23.49:715][ 31]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/head_lod0_mesh.fbx
[2025.05.28-08.23.57:337][ 31]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.23.57:341][ 31]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_head_shader.MH_Friend_head_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.23.57:412][ 31]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-08.23.57:536][ 31]LogFbx: Triangulating skeletal mesh head_lod0_mesh
[2025.05.28-08.24.01:842][ 31]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.24.02:097][ 31]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.28-08.24.02:605][ 31]LogSkeletalMesh: Section 0: Material=0, 48004 triangles
[2025.05.28-08.24.02:675][ 31]LogSkeletalMesh: Building Skeletal Mesh head_lod0_mesh...
[2025.05.28-08.24.02:717][ 31]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.28-08.24.02:749][ 31]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.28-08.24.03:271][ 31]LogSkeletalMesh: Built Skeletal Mesh [0.60s] /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-08.24.03:278][ 31]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
[2025.05.28-08.24.03:288][ 31]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_12:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.24.03:288][ 31]LogWorld: UWorld::CleanupWorld for World_12, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.24.03:288][ 31]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.24.03:290][ 31]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
[2025.05.28-08.24.03:338][ 31]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.24.03:338][ 31]LogWorld: UWorld::CleanupWorld for World_13, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.24.03:339][ 31]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.24.03:359][ 31]LogUObjectHash: Compacting FUObjectHashTables data took   1.10ms
[2025.05.28-08.24.03:404][ 31]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.05.28-08.24.03:421][ 31]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.05.28-08.24.03:439][ 31]LogUObjectHash: Compacting FUObjectHashTables data took   0.43ms
[2025.05.28-08.24.07:085][ 31]LogSkeletalMesh: Building Skeletal Mesh head_lod0_mesh...
[2025.05.28-08.24.07:101][ 31]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.28-08.24.07:502][ 31]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.28-08.24.11:364][ 31]LogSkeletalMesh: Built Skeletal Mesh [4.28s] /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-08.24.11:476][ 31]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.24.11:476][ 31]FBXImport: Warning: The bone size is too small to create Physics Asset 'head_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'head_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.24.11:625][ 32]LogUObjectHash: Compacting FUObjectHashTables data took   0.98ms
[2025.05.28-08.24.11:635][ 32]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.24.11:727][ 35]LogStreaming: Display: FlushAsyncLoading(641): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.24.11:727][ 35]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/saliva_lod0_mesh_PhysicsAsset (0xCEBEF7E7EA79F106) - The package to load does not exist on disk or in the loader
[2025.05.28-08.24.11:727][ 35]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/saliva_lod0_mesh_PhysicsAsset'
[2025.05.28-08.24.12:046][ 35]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/saliva_lod0_mesh.fbx)
[2025.05.28-08.24.12:136][ 35]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/saliva_lod0_mesh.fbx
[2025.05.28-08.24.12:139][ 35]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.24.12:179][ 35]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_saliva_shader.MH_Friend_saliva_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.24.12:192][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB258A3DC00 with pending SubmitJob call.
[2025.05.28-08.24.12:192][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB258A33C00 with pending SubmitJob call.
[2025.05.28-08.24.12:193][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB258A36E00 with pending SubmitJob call.
[2025.05.28-08.24.12:193][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB22F4C0A00 with pending SubmitJob call.
[2025.05.28-08.24.12:194][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB258A36400 with pending SubmitJob call.
[2025.05.28-08.24.12:194][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB258A30A00 with pending SubmitJob call.
[2025.05.28-08.24.12:195][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB258A32800 with pending SubmitJob call.
[2025.05.28-08.24.12:195][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB258A3F000 with pending SubmitJob call.
[2025.05.28-08.24.12:195][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB258A38C00 with pending SubmitJob call.
[2025.05.28-08.24.12:195][ 35]LogFbx: Triangulating skeletal mesh saliva_lod0_mesh
[2025.05.28-08.24.12:195][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB258A34600 with pending SubmitJob call.
[2025.05.28-08.24.12:195][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB258A3BE00 with pending SubmitJob call.
[2025.05.28-08.24.12:196][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB245D61E00 with pending SubmitJob call.
[2025.05.28-08.24.12:197][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB1ED888C00 with pending SubmitJob call.
[2025.05.28-08.24.12:197][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB255B9A000 with pending SubmitJob call.
[2025.05.28-08.24.12:197][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB258A3D200 with pending SubmitJob call.
[2025.05.28-08.24.12:198][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB2617B1400 with pending SubmitJob call.
[2025.05.28-08.24.12:198][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB258A3E600 with pending SubmitJob call.
[2025.05.28-08.24.12:199][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB258A39600 with pending SubmitJob call.
[2025.05.28-08.24.12:199][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB1870EF000 with pending SubmitJob call.
[2025.05.28-08.24.12:199][ 35]LogShaderCompilers: Display: Cancelled job 0x00000AB258A3A000 with pending SubmitJob call.
[2025.05.28-08.24.12:215][ 35]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.24.12:228][ 35]LogSkeletalMesh: Section 0: Material=0, 1004 triangles
[2025.05.28-08.24.12:230][ 35]LogSkeletalMesh: Building Skeletal Mesh saliva_lod0_mesh...
[2025.05.28-08.24.12:241][ 35]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/saliva_lod0_mesh.saliva_lod0_mesh
[2025.05.28-08.24.12:242][ 35]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
[2025.05.28-08.24.12:259][ 35]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_14:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.24.12:259][ 35]LogWorld: UWorld::CleanupWorld for World_14, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.24.12:259][ 35]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.24.12:260][ 35]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_15
[2025.05.28-08.24.12:267][ 35]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_15:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.24.12:267][ 35]LogWorld: UWorld::CleanupWorld for World_15, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.24.12:267][ 35]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.24.12:284][ 35]LogUObjectHash: Compacting FUObjectHashTables data took   0.72ms
[2025.05.28-08.24.12:316][ 35]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.28-08.24.12:334][ 35]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.05.28-08.24.12:351][ 35]LogUObjectHash: Compacting FUObjectHashTables data took   0.39ms
[2025.05.28-08.24.12:355][ 35]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.24.12:355][ 35]FBXImport: Warning: The bone size is too small to create Physics Asset 'saliva_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'saliva_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.24.12:463][ 36]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.28-08.24.12:569][ 37]LogStreaming: Display: FlushAsyncLoading(647): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.24.12:569][ 37]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/teeth_lod0_mesh_PhysicsAsset (0x466EF1832CA8EE25) - The package to load does not exist on disk or in the loader
[2025.05.28-08.24.12:570][ 37]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/teeth_lod0_mesh_PhysicsAsset'
[2025.05.28-08.24.12:806][ 37]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/teeth_lod0_mesh.fbx)
[2025.05.28-08.24.13:005][ 37]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/teeth_lod0_mesh.fbx
[2025.05.28-08.24.13:050][ 37]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.24.13:051][ 37]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_teeth_shader.MH_Friend_teeth_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.24.13:110][ 37]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-08.24.13:223][ 37]LogFbx: Triangulating skeletal mesh teeth_lod0_mesh
[2025.05.28-08.24.13:338][ 37]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.24.13:465][ 37]LogSkeletalMesh: Section 0: Material=0, 8350 triangles
[2025.05.28-08.24.13:468][ 37]LogSkeletalMesh: Building Skeletal Mesh teeth_lod0_mesh...
[2025.05.28-08.24.13:484][ 37]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (teeth_lod0_mesh) ...
[2025.05.28-08.24.13:579][ 37]LogSkeletalMesh: Built Skeletal Mesh [0.11s] /Game/untitled_category/untitled_asset/teeth_lod0_mesh.teeth_lod0_mesh
[2025.05.28-08.24.13:581][ 37]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_16
[2025.05.28-08.24.13:598][ 37]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_16:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.24.13:640][ 37]LogWorld: UWorld::CleanupWorld for World_16, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.24.13:640][ 37]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.24.13:641][ 37]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_17
[2025.05.28-08.24.13:648][ 37]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_17:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.24.13:648][ 37]LogWorld: UWorld::CleanupWorld for World_17, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.24.13:648][ 37]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.24.13:666][ 37]LogUObjectHash: Compacting FUObjectHashTables data took   0.72ms
[2025.05.28-08.24.13:701][ 37]LogUObjectHash: Compacting FUObjectHashTables data took   0.88ms
[2025.05.28-08.24.13:718][ 37]LogUObjectHash: Compacting FUObjectHashTables data took   1.01ms
[2025.05.28-08.24.13:734][ 37]LogUObjectHash: Compacting FUObjectHashTables data took   0.39ms
[2025.05.28-08.24.13:786][ 37]LogSkeletalMesh: Building Skeletal Mesh teeth_lod0_mesh...
[2025.05.28-08.24.13:802][ 37]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (teeth_lod0_mesh) ...
[2025.05.28-08.24.13:973][ 37]LogSkeletalMesh: Built Skeletal Mesh [0.19s] /Game/untitled_category/untitled_asset/teeth_lod0_mesh.teeth_lod0_mesh
[2025.05.28-08.24.13:975][ 37]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.24.13:975][ 37]FBXImport: Warning: The bone size is too small to create Physics Asset 'teeth_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'teeth_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.24.14:124][ 38]LogUObjectHash: Compacting FUObjectHashTables data took   0.80ms
[2025.05.28-08.24.19:689][115]LogSlate: Window 'Message Log' being destroyed
[2025.05.28-08.24.19:825][115]LogSlate: Window 'Message Log' being destroyed
[2025.05.28-08.24.21:731][151]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.24.21:960][155]LogSlate: Warning: Unable to rasterize '../../../Engine/Content/Editor/Slate/Starship/AssetIcons/SkeletalMesh_64.svg'. File could not be found
[2025.05.28-08.24.21:984][156]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_18
[2025.05.28-08.24.22:221][159]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_19
[2025.05.28-08.24.22:359][160]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_20
[2025.05.28-08.24.22:688][164]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_21
[2025.05.28-08.24.22:799][166]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_22
[2025.05.28-08.24.26:489][211]LogUObjectHash: Compacting FUObjectHashTables data took   0.44ms
[2025.05.28-08.24.26:494][211]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/MetaHumans/Test/TestLevel' took 0.021
[2025.05.28-08.24.26:513][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-08.24.26:514][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_23
[2025.05.28-08.24.26:528][211]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Skeleton.cartilage_lod0_mesh_Skeleton]
[2025.05.28-08.24.26:528][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Skeleton]
[2025.05.28-08.24.26:590][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Skeleton_Auto1
[2025.05.28-08.24.26:590][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/cartilage_lod0_mesh_Skeleton_Aut7A516F9948C9150722652FADA8FA637C.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-08.24.26:604][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/cartilage_lod0_mesh] ([1] browsable assets)...
[2025.05.28-08.24.26:604][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_24
[2025.05.28-08.24.26:620][211]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/cartilage_lod0_mesh.cartilage_lod0_mesh]
[2025.05.28-08.24.26:620][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/cartilage_lod0_mesh]
[2025.05.28-08.24.26:639][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Auto1
[2025.05.28-08.24.26:639][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/cartilage_lod0_mesh_Auto1E034802E4AB6CA3FC064F8BD0A4AA76F.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Auto1.uasset'
[2025.05.28-08.24.26:640][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader] ([1] browsable assets)...
[2025.05.28-08.24.31:601][211]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader.MH_Friend_cartilage_shader]
[2025.05.28-08.24.31:602][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader]
[2025.05.28-08.24.31:603][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader_Auto1
[2025.05.28-08.24.31:603][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_cartilage_shader_Auto12A0F686243F92AF3EDCAC1A3A2B95125.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader_Auto1.uasset'
[2025.05.28-08.24.31:603][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-08.24.31:604][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_25
[2025.05.28-08.24.31:620][211]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Skeleton.eyeEdge_lod0_mesh_Skeleton]
[2025.05.28-08.24.31:620][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Skeleton]
[2025.05.28-08.24.31:621][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Skeleton_Auto1
[2025.05.28-08.24.31:621][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeEdge_lod0_mesh_Skeleton_Auto100E7F92F4585D90DBF94EEA469850BE8.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-08.24.31:622][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh] ([1] browsable assets)...
[2025.05.28-08.24.31:623][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_26
[2025.05.28-08.24.31:667][211]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh.eyeEdge_lod0_mesh]
[2025.05.28-08.24.31:667][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh]
[2025.05.28-08.24.31:669][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Auto1
[2025.05.28-08.24.31:669][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeEdge_lod0_mesh_Auto196026F3740AE4E2AEAE6ECBDFC0838C5.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Auto1.uasset'
[2025.05.28-08.24.31:670][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader] ([1] browsable assets)...
[2025.05.28-08.24.31:699][211]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader.MH_Friend_eyeEdge_shader]
[2025.05.28-08.24.31:699][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader]
[2025.05.28-08.24.31:700][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader_Auto1
[2025.05.28-08.24.31:700][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_eyeEdge_shader_Auto1C4E04EA14A150A234E5D01B64083295D.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader_Auto1.uasset'
[2025.05.28-08.24.31:701][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-08.24.31:701][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_27
[2025.05.28-08.24.31:714][211]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Skeleton.eyeLeft_lod0_mesh_Skeleton]
[2025.05.28-08.24.31:714][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Skeleton]
[2025.05.28-08.24.31:715][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Skeleton_Auto1
[2025.05.28-08.24.31:715][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeLeft_lod0_mesh_Skeleton_Auto10D11C88C4098F974E738F1BB7060284E.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-08.24.31:715][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh] ([1] browsable assets)...
[2025.05.28-08.24.31:716][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_28
[2025.05.28-08.24.31:731][211]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh.eyeLeft_lod0_mesh]
[2025.05.28-08.24.31:731][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh]
[2025.05.28-08.24.31:735][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Auto1
[2025.05.28-08.24.31:735][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeLeft_lod0_mesh_Auto1DA30F38245B415884AFB1CB90E6B724F.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Auto1.uasset'
[2025.05.28-08.24.31:736][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader] ([1] browsable assets)...
[2025.05.28-08.24.32:850][211]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader.MH_Friend_eyeLeft_shader]
[2025.05.28-08.24.32:850][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader]
[2025.05.28-08.24.32:851][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader_Auto1
[2025.05.28-08.24.32:851][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_eyeLeft_shader_Auto19198B48649BAA2F7505367A9B7A40042.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader_Auto1.uasset'
[2025.05.28-08.24.32:853][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyes_normal_map] ([1] browsable assets)...
[2025.05.28-08.24.32:855][211]OBJ SavePackage:     Rendered thumbnail for [Texture2D /Game/untitled_category/untitled_asset/eyes_normal_map.eyes_normal_map]
[2025.05.28-08.24.32:855][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyes_normal_map]
[2025.05.28-08.24.33:015][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyes_normal_map_Auto1
[2025.05.28-08.24.33:015][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyes_normal_map_Auto17A5665CB42F40B8E86184D8B6FE1AC6B.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyes_normal_map_Auto1.uasset'
[2025.05.28-08.24.33:016][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-08.24.33:016][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_29
[2025.05.28-08.24.33:033][211]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Skeleton.eyeRight_lod0_mesh_Skeleton]
[2025.05.28-08.24.33:033][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Skeleton]
[2025.05.28-08.24.33:034][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Skeleton_Auto1
[2025.05.28-08.24.33:034][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeRight_lod0_mesh_Skeleton_AutoCC8A6E424A252B685E7ED48D99CDC9B8.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-08.24.33:035][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh] ([1] browsable assets)...
[2025.05.28-08.24.33:035][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_30
[2025.05.28-08.24.33:048][211]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh.eyeRight_lod0_mesh]
[2025.05.28-08.24.33:048][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh]
[2025.05.28-08.24.33:053][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Auto1
[2025.05.28-08.24.33:054][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeRight_lod0_mesh_Auto122E40D6A4CF2C8F7A8C01F9B078E9EC7.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Auto1.uasset'
[2025.05.28-08.24.33:054][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader] ([1] browsable assets)...
[2025.05.28-08.24.33:124][211]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader.MH_Friend_eyeRight_shader]
[2025.05.28-08.24.33:124][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader]
[2025.05.28-08.24.33:125][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader_Auto1
[2025.05.28-08.24.33:126][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_eyeRight_shader_Auto19FF2B5B748806BF871D044BDF316D259.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader_Auto1.uasset'
[2025.05.28-08.24.33:126][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-08.24.33:126][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_31
[2025.05.28-08.24.33:142][211]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Skeleton.eyelashes_lod0_mesh_Skeleton]
[2025.05.28-08.24.33:142][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Skeleton]
[2025.05.28-08.24.33:143][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Skeleton_Auto1
[2025.05.28-08.24.33:143][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyelashes_lod0_mesh_Skeleton_Aut9D83293442E6561CE3F4899E8D9CA38A.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-08.24.33:144][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh] ([1] browsable assets)...
[2025.05.28-08.24.33:144][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_32
[2025.05.28-08.24.33:157][211]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh.eyelashes_lod0_mesh]
[2025.05.28-08.24.33:157][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh]
[2025.05.28-08.24.33:162][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Auto1
[2025.05.28-08.24.33:163][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyelashes_lod0_mesh_Auto12D650FAD4E4A7D4FAEEB62868351CA08.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Auto1.uasset'
[2025.05.28-08.24.33:163][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader] ([1] browsable assets)...
[2025.05.28-08.24.33:193][211]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader.MH_Friend_eyelashes_shader]
[2025.05.28-08.24.33:193][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader]
[2025.05.28-08.24.33:194][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader_Auto1
[2025.05.28-08.24.33:194][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_eyelashes_shader_Auto1FB54286D4CF4D833B620AAA4643BE1D8.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader_Auto1.uasset'
[2025.05.28-08.24.33:195][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-08.24.33:200][211]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Skeleton.eyeshell_lod0_mesh_Skeleton]
[2025.05.28-08.24.33:200][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Skeleton]
[2025.05.28-08.24.33:201][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Skeleton_Auto1
[2025.05.28-08.24.33:201][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeshell_lod0_mesh_Skeleton_Auto411B348A47D1A3D1F8003B898DE02361.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-08.24.33:201][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh] ([1] browsable assets)...
[2025.05.28-08.24.33:206][211]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh.eyeshell_lod0_mesh]
[2025.05.28-08.24.33:206][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh]
[2025.05.28-08.24.33:209][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Auto1
[2025.05.28-08.24.33:209][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeshell_lod0_mesh_Auto1A2B5508342955E01C1D116AB4E0DDC72.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Auto1.uasset'
[2025.05.28-08.24.33:210][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader] ([1] browsable assets)...
[2025.05.28-08.24.33:238][211]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader.MH_Friend_eyeshell_shader]
[2025.05.28-08.24.33:238][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader]
[2025.05.28-08.24.33:239][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader_Auto1
[2025.05.28-08.24.33:239][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_eyeshell_shader_Auto1D6D916824C1E7DE7158C3FAB3EB4A663.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader_Auto1.uasset'
[2025.05.28-08.24.33:240][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/head_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-08.24.33:246][211]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/head_lod0_mesh_Skeleton.head_lod0_mesh_Skeleton]
[2025.05.28-08.24.33:246][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/head_lod0_mesh_Skeleton]
[2025.05.28-08.24.33:258][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/head_lod0_mesh_Skeleton_Auto1
[2025.05.28-08.24.33:259][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/head_lod0_mesh_Skeleton_Auto1F92D312E4DF8834CA0B72E82AE8D2748.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/head_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-08.24.33:259][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/head_lod0_mesh] ([1] browsable assets)...
[2025.05.28-08.24.33:264][211]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh]
[2025.05.28-08.24.33:264][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/head_lod0_mesh]
[2025.05.28-08.24.33:589][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/head_lod0_mesh_Auto1
[2025.05.28-08.24.33:590][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/head_lod0_mesh_Auto163D2F3B24E68D79C0E89A8AADA369039.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/head_lod0_mesh_Auto1.uasset'
[2025.05.28-08.24.33:590][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_head_shader] ([1] browsable assets)...
[2025.05.28-08.24.33:621][211]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_head_shader.MH_Friend_head_shader]
[2025.05.28-08.24.33:621][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_head_shader]
[2025.05.28-08.24.33:622][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_head_shader_Auto1
[2025.05.28-08.24.33:622][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_head_shader_Auto1BF3887184063AB35D7FE04B2F6D7F740.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_head_shader_Auto1.uasset'
[2025.05.28-08.24.33:623][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/head_roughness_map] ([1] browsable assets)...
[2025.05.28-08.24.33:624][211]OBJ SavePackage:     Rendered thumbnail for [Texture2D /Game/untitled_category/untitled_asset/head_roughness_map.head_roughness_map]
[2025.05.28-08.24.33:624][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/head_roughness_map]
[2025.05.28-08.24.33:891][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/head_roughness_map_Auto1
[2025.05.28-08.24.33:892][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/head_roughness_map_Auto1FFA591024141521719D50C812BB35F5F.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/head_roughness_map_Auto1.uasset'
[2025.05.28-08.24.33:892][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-08.24.33:893][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_33
[2025.05.28-08.24.33:941][211]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/saliva_lod0_mesh_Skeleton.saliva_lod0_mesh_Skeleton]
[2025.05.28-08.24.33:941][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Skeleton]
[2025.05.28-08.24.33:942][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Skeleton_Auto1
[2025.05.28-08.24.33:942][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/saliva_lod0_mesh_Skeleton_Auto16E3DD9E7458767261D051DB5A9F4C6D4.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-08.24.33:943][211]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/saliva_lod0_mesh] ([1] browsable assets)...
[2025.05.28-08.24.33:943][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/saliva_lod0_mesh]
[2025.05.28-08.24.33:946][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Auto1
[2025.05.28-08.24.33:946][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/saliva_lod0_mesh_Auto10751BC534363674F6E68519090601D38.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Auto1.uasset'
[2025.05.28-08.24.33:947][211]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_saliva_shader] ([1] browsable assets)...
[2025.05.28-08.24.33:947][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_saliva_shader]
[2025.05.28-08.24.33:948][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_saliva_shader_Auto1
[2025.05.28-08.24.33:948][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_saliva_shader_Auto11DE87E5D48BA57EABA8777868B7092F7.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_saliva_shader_Auto1.uasset'
[2025.05.28-08.24.33:949][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-08.24.33:949][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_34
[2025.05.28-08.24.33:966][211]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/teeth_lod0_mesh_Skeleton.teeth_lod0_mesh_Skeleton]
[2025.05.28-08.24.33:966][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Skeleton]
[2025.05.28-08.24.33:968][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Skeleton_Auto1
[2025.05.28-08.24.33:968][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/teeth_lod0_mesh_Skeleton_Auto1FF23B6DE4F3754CC8E339DA458B45361.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-08.24.33:969][211]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/teeth_lod0_mesh] ([1] browsable assets)...
[2025.05.28-08.24.33:969][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/teeth_lod0_mesh]
[2025.05.28-08.24.33:984][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Auto1
[2025.05.28-08.24.33:984][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/teeth_lod0_mesh_Auto19694972A4C9E16F12D7122885EB4D9FB.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Auto1.uasset'
[2025.05.28-08.24.33:984][211]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_teeth_shader] ([1] browsable assets)...
[2025.05.28-08.24.33:984][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_teeth_shader]
[2025.05.28-08.24.33:986][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_teeth_shader_Auto1
[2025.05.28-08.24.33:986][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_teeth_shader_Auto1418EE1A042C6DF90EEFB6291928E3FD5.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_teeth_shader_Auto1.uasset'
[2025.05.28-08.24.33:987][211]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/teeth_normal_map] ([1] browsable assets)...
[2025.05.28-08.24.33:988][211]OBJ SavePackage:     Rendered thumbnail for [Texture2D /Game/untitled_category/untitled_asset/teeth_normal_map.teeth_normal_map]
[2025.05.28-08.24.33:988][211]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/teeth_normal_map]
[2025.05.28-08.24.34:222][211]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/teeth_normal_map_Auto1
[2025.05.28-08.24.34:222][211]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/teeth_normal_map_Auto1FCEF82D143C9043C980743B3639C51FD.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/teeth_normal_map_Auto1.uasset'
[2025.05.28-08.24.34:223][211]LogFileHelpers: Auto-saving content packages took 7.729
[2025.05.28-08.24.34:407][212]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.24.36:599][237]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_35
[2025.05.28-08.24.36:721][239]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_36
[2025.05.28-08.24.44:487][422]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.24.54:529][616]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.25.04:490][529]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.25.14:499][446]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.25.24:504][363]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.25.34:513][283]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.25.44:513][203]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.25.54:520][119]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.26.04:525][ 37]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: ================================================
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: === FShaderJobCache stats ===
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Total job queries 568, among them cache hits 198 (34.86%), DDC hits 334 (58.80%), Duplicates 8 (1.41%)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Tracking 362 distinct input hashes that result in 267 distinct outputs (73.76%)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: RAM used: 164.35 KiB of 3.20 GiB budget. Usage: 0.00%
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: === Shader Compilation stats ===
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Shaders Compiled: 28
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Jobs assigned 28, completed 28 (100%)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Average time worker was idle: 0.65 s
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Time job spent in pending queue: average 0.03 s, longest 0.06 s
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Job execution time: average 2.54 s, max 4.92 s
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Job life time (pending + execution): average 2.57 s, max 4.93
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Shader code size: average 9.264 KiB, min 6.129 KiB, max 12.843 KiB
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Time at least one job was in flight (either pending or executed): 6.03 s
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Mutex wait stall in FShaderJobCache::SubmitJobs:  0.35%
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Jobs were issued in 28 batches (only local compilation was used), average 1.00 jobs/batch
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Average processing rate: 4.65 jobs/sec
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Total thread time: 16.667 s
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Total thread preprocess time: 11.163 s
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Percentage time preprocessing: 66.98%
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Effective parallelization: 2.77 (times faster than compiling all shaders on one thread). Compare with number of workers: 16
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Top 5 most expensive shader types by average time:
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display:                                             FDebugViewModePS (compiled    4 times, average 1.03 sec, max 1.09 sec, min 0.95 sec)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display:                                      FPathTracingMaterialCHS (compiled    2 times, average 0.84 sec, max 0.92 sec, min 0.76 sec)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display:                         TBasePassPSFNoLightMapPolicySkylight (compiled    6 times, average 0.59 sec, max 0.74 sec, min 0.44 sec)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy (compiled    4 times, average 0.58 sec, max 0.69 sec, min 0.45 sec)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display:                        FRayTracingDynamicGeometryConverterCS (compiled    2 times, average 0.43 sec, max 0.51 sec, min 0.34 sec)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Top 5 shader types by total compile time:
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display:                                             FDebugViewModePS - 24.64% of total time (compiled    4 times, average 1.03 sec, max 1.09 sec, min 0.95 sec)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display:                         TBasePassPSFNoLightMapPolicySkylight - 21.41% of total time (compiled    6 times, average 0.59 sec, max 0.74 sec, min 0.44 sec)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display:                                             FDebugViewModeVS - 14.92% of total time (compiled    6 times, average 0.41 sec, max 0.51 sec, min 0.30 sec)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy - 13.97% of total time (compiled    4 times, average 0.58 sec, max 0.69 sec, min 0.45 sec)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display:                                      FPathTracingMaterialCHS - 10.06% of total time (compiled    2 times, average 0.84 sec, max 0.92 sec, min 0.76 sec)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: === Material stats ===
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Materials Cooked:        0
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Materials Translated:    182
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Material Total Translate Time: 0.10 s
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Material Translation Only: 0.06 s (61%)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Material DDC Serialization Only: 0.00 s (5%)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: Material Cache Hits: 54 (30%)
[2025.05.28-08.26.08:781][427]LogShaderCompilers: Display: ================================================
[2025.05.28-08.26.14:532][950]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.26.24:531][863]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.26.30:748][351]LogUObjectHash: Compacting FUObjectHashTables data took   0.45ms
[2025.05.28-08.26.32:180][351]LogSlate: Window 'Save Content' being destroyed
[2025.05.28-08.26.32:260][351]LogStall: Shutdown...
[2025.05.28-08.26.32:260][351]LogStall: Shutdown complete.
[2025.05.28-08.26.32:311][351]LogSlate: Window 'BlenderLinkProject - Unreal Editor' being destroyed
[2025.05.28-08.26.32:375][351]Cmd: QUIT_EDITOR
[2025.05.28-08.26.32:375][352]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.05.28-08.26.32:382][352]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.05.28-08.26.32:382][352]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.05.28-08.26.32:382][352]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.05.28-08.26.32:390][352]LogWorld: UWorld::CleanupWorld for TestLevel, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:391][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:391][352]LogWorldPartition: UWorldPartition::Uninitialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel
[2025.05.28-08.26.32:404][352]LogStylusInput: Shutting down StylusInput subsystem.
[2025.05.28-08.26.32:404][352]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.05.28-08.26.32:406][352]LogWorld: UWorld::CleanupWorld for World_34, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:406][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:406][352]LogWorld: UWorld::CleanupWorld for World_33, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:406][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:406][352]LogWorld: UWorld::CleanupWorld for World_32, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:406][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:406][352]LogWorld: UWorld::CleanupWorld for World_31, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:407][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:407][352]LogWorld: UWorld::CleanupWorld for World_30, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:407][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:407][352]LogWorld: UWorld::CleanupWorld for World_29, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:407][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:407][352]LogWorld: UWorld::CleanupWorld for World_28, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:407][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:407][352]LogWorld: UWorld::CleanupWorld for World_27, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:407][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:407][352]LogWorld: UWorld::CleanupWorld for World_26, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:407][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:407][352]LogWorld: UWorld::CleanupWorld for World_25, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:407][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:407][352]LogWorld: UWorld::CleanupWorld for World_24, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:407][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:407][352]LogWorld: UWorld::CleanupWorld for World_23, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:407][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:408][352]LogWorld: UWorld::CleanupWorld for World_22, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:408][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:408][352]LogWorld: UWorld::CleanupWorld for World_21, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:408][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:408][352]LogWorld: UWorld::CleanupWorld for World_20, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:408][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:408][352]LogWorld: UWorld::CleanupWorld for World_19, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:408][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:408][352]LogWorld: UWorld::CleanupWorld for World_18, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:408][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:408][352]LogWorld: UWorld::CleanupWorld for World_35, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:408][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:408][352]LogWorld: UWorld::CleanupWorld for World_36, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.26.32:408][352]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.26.32:408][352]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.05.28-08.26.32:418][352]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroying ET Analytics provider
[2025.05.28-08.26.32:418][352]LogAnalytics: Display: [UEEditor.Rocket.Release] Ended ET Analytics provider session
[2025.05.28-08.26.32:418][352]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroyed ET Analytics provider
[2025.05.28-08.26.32:420][352]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.05.28-08.26.32:420][352]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.05.28-08.26.32:420][352]LogAudio: Display: Audio Device unregistered from world 'TestLevel'.
[2025.05.28-08.26.32:420][352]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.05.28-08.26.32:420][352]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.05.28-08.26.32:423][352]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.05.28-08.26.32:430][352]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.05.28-08.26.32:430][352]LogAudio: Display: Audio Device Manager Shutdown
[2025.05.28-08.26.32:432][352]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.05.28-08.26.32:432][352]LogExit: Preparing to exit.
[2025.05.28-08.26.32:504][352]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.05.28-08.26.33:468][352]LogEditorDataStorage: Deinitializing
[2025.05.28-08.26.34:084][352]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.05.28-08.26.34:094][352]LogExit: Editor shut down
[2025.05.28-08.26.34:097][352]LogExit: Transaction tracking system shut down
[2025.05.28-08.26.34:188][352]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.05.28-08.26.34:188][352]LogWebBrowser: Deleting browser for Url=https://quixel.com/?code=305f36c316a34b5992ff7a78ca1f3f63.
[2025.05.28-08.26.34:233][352]LogExit: Object subsystem successfully closed.
[2025.05.28-08.26.34:279][352]LogShaderCompilers: Display: Shaders left to compile 0
[2025.05.28-08.26.35:238][352]LogMemoryProfiler: Shutdown
[2025.05.28-08.26.35:238][352]LogNetworkingProfiler: Shutdown
[2025.05.28-08.26.35:238][352]LoadingProfiler: Shutdown
[2025.05.28-08.26.35:238][352]LogTimingProfiler: Shutdown
[2025.05.28-08.26.35:245][352]LogWebBrowser: Deleting browser for Url=file:///D:/UE_5.5/Engine/Plugins/Bridge/ThirdParty/megascans/index.html#/megascans/metahumans?category=my_metahumans.
[2025.05.28-08.26.35:246][352]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.28-08.26.35:246][352]LogBlenderLink: Closing listener socket
[2025.05.28-08.26.35:246][352]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.28-08.26.35:606][352]LogChaosDD: Chaos Debug Draw Shutdown
[2025.05.28-08.26.35:616][352]LogHttp: Warning: [FHttpManager::Shutdown] Unbinding delegates for 1 outstanding Http Requests:
[2025.05.28-08.26.35:616][352]LogHttp: Warning: 	verb=[POST] url=[https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7BE111E88B-4F73-1EC1-EC8F-1D8E909E5BA8%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.5.4-40574608%2B%2B%2BUE5%2BRelease-5.5&UserID=5eff80b14364fb2f37e5468dbd2f7de6%7C0de775007ac941b984ac36d970a4fb1c%7C6ba94555-8ddf-4db5-b84c-ec5c48d27de3&AppEnvironment=datacollector-binary&UploadType=eteventstream] refs=[2] status=Processing
[2025.05.28-08.26.37:150][352]LogEOSSDK: FEOSSDKManager::Shutdown EOS_Shutdown Result=[EOS_Success]
[2025.05.28-08.26.37:150][352]LogStudioTelemetry: Display: Shutdown StudioTelemetry Module
[2025.05.28-08.26.37:150][352]LogNFORDenoise: NFORDenoise function shutting down
[2025.05.28-08.26.37:150][352]RenderDocPlugin: plugin has been unloaded.
[2025.05.28-08.26.37:157][352]LogXGEController: Cleaning working directory: C:/Users/<USER>/AppData/Local/Temp/UnrealXGEWorkingDir/
[2025.05.28-08.26.37:158][352]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
[2025.05.28-08.26.37:158][352]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
[2025.05.28-08.26.37:158][352]LogPakFile: Destroying PakPlatformFile
[2025.05.28-08.26.37:468][352]LogD3D12RHI: ~FD3D12DynamicRHI
[2025.05.28-08.26.37:528][352]LogExit: Exiting.
[2025.05.28-08.26.37:546][352]Log file closed, 05/28/25 13:56:37
