# Blender MetaHuman DNA Plugin Plan

## Overview

This document outlines the plan for developing a Blender plugin that will allow users to work with MetaHuman DNA files directly in Blender. The plugin will leverage the existing DNACalib library to read, manipulate, and write DNA files, providing a seamless workflow for Blender users working with MetaHuman characters.

## Plugin Structure

The plugin will be organized as follows:

```
EditMH/plugin/
├── __init__.py                 # Plugin initialization
├── blender_dna_plugin/         # Main plugin code
│   ├── __init__.py             # Package initialization
│   ├── operators/              # Blender operators
│   │   ├── __init__.py
│   │   ├── import_dna.py       # DNA import operator
│   │   ├── export_dna.py       # DNA export operator
│   │   └── modify_dna.py       # DNA modification operators
│   ├── panels/                 # Blender UI panels
│   │   ├── __init__.py
│   │   └── dna_panel.py        # Main DNA panel
│   ├── properties/             # Blender property definitions
│   │   ├── __init__.py
│   │   └── dna_properties.py   # DNA-related properties
│   └── utils/                  # Utility functions
│       ├── __init__.py
│       └── dna_utils.py        # DNA manipulation utilities
├── dnacalib/                   # DNACalib library (contents copied from project root)
│   ├── py3.11/                 # Python 3.11 compiled modules
│   │   ├── dna.py
│   │   ├── _py3dna.pyd
│   │   ├── dnacalib.py
│   │   └── _py3dnacalib.pyd
│   └── examples/               # Example scripts (for reference)
└── README.md                   # Plugin documentation
```

## Dependencies

The plugin will depend on:

1. **DNACalib Library**: The core library for reading and manipulating DNA files
2. **Blender Python API**: For integrating with Blender
3. **Python 3.11**: To match Blender's Python version

## Implementation Plan

### Phase 1: Setup and Basic Structure (Done)

1. Create the basic plugin structure
2. Copy the necessary contents from the DNACalib library into the plugin folder
3. Create the plugin initialization code
4. Implement basic UI panels

### Phase 2.0: Core Functionality (Done)

1. Implement Import DNA functionality
   - Create a Import DNA button
   - Read DNA file
   - Parse DNA file data using proper functions,and log important information about it both in console and the UI panel.


### Phase 2.1: Blender Integration (Done)

1. Create DNA-related Blender objets
   - Create Blender meshes from DNA data
   - Create Blender armatures from DNA data

### Phase 2.2: Object Properties (Done)

1. Create object properties from DNA
   - Create Vertex groups and add weightpaints from the DNA data
   - Create Blendshapes from DNA data.

2. Faceboards and Pose Implementation (Done)
   - Create a faceboard from a pre-defined blend file
   - Implement pose application from DNA data
   - Implement pose baking to keyframes

### Phase 2.3: Materials and Textures

1. Implement material creation from DNA (To be implemented in the future)
   - Create basic materials for meshes
   - Set up proper material slots
   - Import and assign textures from DNA

2. Implement texture management (To be implemented in the future)
   - Support for various texture types (color, normal, roughness, etc.)
   - Proper UV mapping and texture coordinates
   - Texture path management

### Phase 3: Animation and Editing

1. Implement animation support
   - Import animations from FBX or JSON
   - Bake animations to keyframes
   - Animation controls in UI

2. Implement shape key editing tools
   - Edit shape keys directly
   - Sculpt shape keys
   - Re-import modified shape keys

3. Implement bone editing tools
   - Mirror selected bones
   - Push bones along normals
   - Auto-fit bones to mesh
   - Revert bone transforms to DNA

### Phase 4: Export and Integration

1. Implement DNA export functionality
   - Export to DNA format (binary or JSON)
   - Support for calibration or overwrite methods
   - Export selected components (meshes, bones, etc.)

2. Implement Unreal Engine integration
   - Send to Unreal functionality
   - Material slot to material instance mapping
   - Blueprint and level sequence integration
   - Sync with body bones in blueprint

### Phase 5: Testing and Refinement

1. Test with various DNA files
2. Optimize performance
3. Refine UI/UX
4. Fix bugs and issues

## Build Automation

We will create a build system to automate the packaging of the plugin for distribution.

The build script (`tools/build/build_plugin.py`) will:

1. Copy the plugin files to a temporary directory
2. Copy the necessary DNACalib files
3. Create a ZIP file with the correct structure for Blender plugin installation
4. Save the ZIP file to the builds directory with version information

```
EditMH/
├── plugin/                     # Plugin source code
├── builds/                     # Output directory for builds
│   ├── blender_dna_plugin_v1.0.zip
│   └── ...
└── tools/
    ├── build/
    │   └── build_plugin.py     # Build script
    └── test/
        └── test_plugin.py      # Test script
```
## Testing Plan

We will create a testing framework to ensure the plugin works correctly: 
1. Unit tests for core functionality
2. Integration tests with Blender
3. Manual testing with sample DNA files

## Distribution

The plugin will be distributed as a ZIP file that can be installed directly in Blender through the Add-ons preferences panel.

## Future Enhancements

1. Support for batch processing multiple DNA files
2. Integration with other Blender add-ons
3. Custom shader support for MetaHuman materials
4. Real-time preview of DNA modifications


