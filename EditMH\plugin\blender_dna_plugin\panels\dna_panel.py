"""
UI Panel for the Blender MetaHuman DNA Plugin.
"""

import bpy
from bpy.types import Panel
from bpy.props import Bool<PERSON>roperty

# Try to import the DNA modules
try:
    import dna
    DNA_MODULES_AVAILABLE = True
except ImportError:
    DNA_MODULES_AVAILABLE = False

class DNA_PT_MainPanel(Panel):
    """Main panel for the MetaHuman DNA Tools"""
    bl_label = "MetaHuman DNA Tools"
    bl_idname = "DNA_PT_MainPanel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'MetaHuman DNA'

    def draw(self, context):
        layout = self.layout
        dna_tools = context.scene.dna_tools

        # Only log if debug logging is enabled
        if dna_tools.enable_debug_logging:
            print(f"\n=== Drawing DNA_PT_MainPanel ===")
            print(f"DNA loaded flag: {dna_tools.is_dna_loaded}")
            print(f"DNA file path: {dna_tools.dna_file_path}")
            print(f"DNA name: {dna_tools.dna_name}")
            print(f"DNA archetype: {dna_tools.dna_archetype}")
            print(f"DNA gender: {dna_tools.dna_gender}")
            print(f"DNA age: {dna_tools.dna_age}")
            print(f"DNA mesh count: {dna_tools.dna_mesh_count}")
            print(f"DNA joint count: {dna_tools.dna_joint_count}")
            print(f"DNA blendshape count: {dna_tools.dna_blendshape_count}")

        # Add a label
        layout.label(text="MetaHuman DNA Tools")

        # Add a separator
        layout.separator()

        # Show import button when no DNA is loaded
        if not dna_tools.is_dna_loaded:
            row = layout.row()
            row.operator("dna.import_file", text="Import DNA", icon='IMPORT')

        # Add a separator
        layout.separator()

        # Show status message if no DNA is loaded
        if not dna_tools.is_dna_loaded:
            box = layout.box()
            box.label(text="Status")
            row = box.row()
            row.label(text=f"{dna_tools.status_message}")

        # Show Send to Unreal button when DNA is loaded
        if dna_tools.is_dna_loaded:
            layout.separator()
            box = layout.box()
            box.label(text="Export")

            # Send to Unreal button
            row = box.row()
            row.operator("dna.send_to_unreal", text="Send to Unreal", icon='EXPORT')


class DNA_PT_DetailsPanel(Panel):
    """Details panel for the MetaHuman DNA Tools"""
    bl_label = "DNA Details"
    bl_idname = "DNA_PT_DetailsPanel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'MetaHuman DNA'
    bl_parent_id = "DNA_PT_MainPanel"
    bl_options = {'DEFAULT_CLOSED'}

    def draw(self, context):
        layout = self.layout
        dna_tools = context.scene.dna_tools

        if not dna_tools.is_dna_loaded:
            layout.label(text="No DNA file loaded")
            return

        # Character information
        box = layout.box()
        box.label(text="Character Information")

        # Show character name if available
        if dna_tools.dna_character_name:
            row = box.row()
            row.label(text=f"Name: {dna_tools.dna_character_name}")

        # Show character type if available
        if dna_tools.dna_character_type:
            row = box.row()
            row.label(text=f"Type: {dna_tools.dna_character_type}")

        # Show character ID if available
        if dna_tools.dna_character_id:
            row = box.row()
            row.label(text=f"ID: {dna_tools.dna_character_id}")

        # Statistics
        box = layout.box()
        box.label(text="Statistics")

        row = box.row()
        row.label(text=f"Meshes: {dna_tools.dna_mesh_count}")

        row = box.row()
        row.label(text=f"Joints: {dna_tools.dna_joint_count}")

        row = box.row()
        row.label(text=f"Blend Shapes: {dna_tools.dna_blendshape_count}")

        # File information
        box = layout.box()
        box.label(text="File Information")

        row = box.row()
        row.label(text=f"File: {bpy.path.basename(dna_tools.dna_file_path)}")

        row = box.row()
        row.label(text=f"Path: {dna_tools.dna_file_path}")

class DNA_PT_ModelPanel(Panel):
    """Model creation panel for the MetaHuman DNA Tools"""
    bl_label = "Model Creation"
    bl_idname = "DNA_PT_ModelPanel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'MetaHuman DNA'
    bl_parent_id = "DNA_PT_MainPanel"
    bl_options = set()  # No options means the panel will be expanded by default if shown

    @classmethod
    def poll(cls, context):
        """Only show this panel if DNA is loaded and faceboard is not created yet"""
        dna_tools = context.scene.dna_tools
        # Check if the panel should be shown
        return dna_tools.is_dna_loaded and not dna_tools.is_faceboard_created

    def draw(self, context):
        layout = self.layout
        dna_tools = context.scene.dna_tools

        if not dna_tools.is_dna_loaded:
            layout.label(text="No DNA file loaded")
            return

        # Model creation options
        box = layout.box()
        box.label(text="Model Creation Options")

        # Show different options based on whether mesh has been created
        if not dna_tools.is_mesh_created:
            # Add LOD selection checkboxes
            box.label(text="Select LOD Levels to Create:")

            # Create a grid layout for LOD checkboxes
            grid = box.grid_flow(row_major=True, columns=2, even_columns=True)

            # Add LOD checkboxes
            grid.prop(context.window_manager, "create_lod0", text="LOD 0 (High)")
            grid.prop(context.window_manager, "create_lod1", text="LOD 1")
            grid.prop(context.window_manager, "create_lod2", text="LOD 2")
            grid.prop(context.window_manager, "create_lod3", text="LOD 3")
            grid.prop(context.window_manager, "create_lod4", text="LOD 4")
            grid.prop(context.window_manager, "create_lod5", text="LOD 5")
            grid.prop(context.window_manager, "create_lod6", text="LOD 6")
            grid.prop(context.window_manager, "create_lod7", text="LOD 7 (Low)")

            # Create model button
            row = box.row()
            row.operator("dna.create_model", text="Create Model", icon='MESH_MONKEY')
        elif not dna_tools.is_armature_created:
            # Armature creation section
            box.label(text="Armature Creation:")

            # Armature options
            row = box.row()
            row.prop(context.scene.dna_tools, "use_custom_bone_shapes", text="Custom Bone Shapes")

            row = box.row()
            row.prop(context.scene.dna_tools, "organize_bone_collections", text="Organize Bone Collections")

            # Create armature button
            row = box.row()
            row.operator("dna.create_armature", text="Create Armature", icon='ARMATURE_DATA')
        elif dna_tools.is_armature_created and not dna_tools.is_weights_applied:
            # Weight application section
            box.label(text="Weight Application:")

            # Apply weights button
            row = box.row()
            row.operator("dna.apply_weights", text="Apply Weights", icon='MESH_DATA')
        elif dna_tools.is_weights_applied and not dna_tools.is_materials_created:
            # Material creation section
            box.label(text="Material Creation:")

            # Create materials button
            row = box.row()
            row.operator("dna.create_materials", text="Create Materials", icon='MATERIAL')
        elif not dna_tools.is_faceboard_created:
            # Faceboard creation section
            box.label(text="Faceboard Creation:")

            # Create faceboard button
            row = box.row()
            row.operator("dna.create_faceboard", text="Create Face Board", icon='ARMATURE_DATA')

class DNA_PT_DebugPanel(Panel):
    """Debug settings panel for the MetaHuman DNA Tools"""
    bl_label = "Debug Settings"
    bl_idname = "DNA_PT_DebugPanel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'MetaHuman DNA'
    bl_parent_id = "DNA_PT_MainPanel"
    bl_options = {'DEFAULT_CLOSED'}

    def draw(self, context):
        layout = self.layout
        dna_tools = context.scene.dna_tools

        # Debug settings
        box = layout.box()
        box.label(text="Debug Options")

        # Debug logging toggle
        row = box.row()
        row.prop(dna_tools, "enable_debug_logging", text="Enable Debug Logging")

        # Save response JSON toggle
        row = box.row()
        row.prop(dna_tools, "save_response_json", text="Save Response JSON")

        # Save sent controls toggle
        row = box.row()
        row.prop(dna_tools, "save_sent_controls", text="Save Sent Controls")


class DNA_PT_UnrealPanel(Panel):
    """Unreal Engine integration panel for the MetaHuman DNA Tools"""
    bl_label = "Unreal Engine Settings"
    bl_idname = "DNA_PT_UnrealPanel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'MetaHuman DNA'
    bl_parent_id = "DNA_PT_MainPanel"
    bl_options = {'DEFAULT_CLOSED'}

    @classmethod
    def poll(cls, context):
        """Only show this panel when DNA is loaded"""
        dna_tools = context.scene.dna_tools
        return dna_tools.is_dna_loaded

    def draw(self, context):
        layout = self.layout
        dna_tools = context.scene.dna_tools

        # Unreal Engine settings
        box = layout.box()
        box.label(text="Export Settings")

        # Content folder
        row = box.row()
        row.prop(dna_tools, "unreal_content_folder", text="Content Folder")

        # Asset paths
        row = box.row()
        row.prop(dna_tools, "unreal_blueprint_asset_path", text="Blueprint Path")

        row = box.row()
        row.prop(dna_tools, "unreal_face_control_rig_asset_path", text="Control Rig Path")

        row = box.row()
        row.prop(dna_tools, "unreal_face_anim_bp_asset_path", text="Anim BP Path")

# Classes to register
classes = [
    DNA_PT_MainPanel,
    DNA_PT_DetailsPanel,
    DNA_PT_ModelPanel,
    DNA_PT_DebugPanel,
    DNA_PT_UnrealPanel,
]

def register():
    """Register the panel classes"""
    for cls in classes:
        bpy.utils.register_class(cls)

    # Register LOD selection properties
    bpy.types.WindowManager.create_lod0 = BoolProperty(
        name="LOD 0 (High)",
        description="Create the highest level of detail",
        default=True
    )

    bpy.types.WindowManager.create_lod1 = BoolProperty(
        name="LOD 1",
        description="Create medium-high level of detail",
        default=False
    )

    bpy.types.WindowManager.create_lod2 = BoolProperty(
        name="LOD 2",
        description="Create medium level of detail",
        default=False
    )

    bpy.types.WindowManager.create_lod3 = BoolProperty(
        name="LOD 3",
        description="Create medium-low level of detail",
        default=False
    )

    bpy.types.WindowManager.create_lod4 = BoolProperty(
        name="LOD 4",
        description="Create low level of detail",
        default=False
    )

    bpy.types.WindowManager.create_lod5 = BoolProperty(
        name="LOD 5",
        description="Create very low level of detail",
        default=False
    )

    bpy.types.WindowManager.create_lod6 = BoolProperty(
        name="LOD 6",
        description="Create extremely low level of detail",
        default=False
    )

    bpy.types.WindowManager.create_lod7 = BoolProperty(
        name="LOD 7 (Low)",
        description="Create the lowest level of detail",
        default=False
    )

def unregister():
    """Unregister the panel classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)

    # Unregister LOD selection properties
    del bpy.types.WindowManager.create_lod0
    del bpy.types.WindowManager.create_lod1
    del bpy.types.WindowManager.create_lod2
    del bpy.types.WindowManager.create_lod3
    del bpy.types.WindowManager.create_lod4
    del bpy.types.WindowManager.create_lod5
    del bpy.types.WindowManager.create_lod6
    del bpy.types.WindowManager.create_lod7
