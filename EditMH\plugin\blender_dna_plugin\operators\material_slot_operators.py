"""
Material slot management operators for Send2UE integration.
Matches the example implementation exactly.
"""

import bpy
from bpy.types import Operator
try:
    from ..constants.metahuman_constants import MESH_SHADER_MAPPING
except ImportError:
    # Fallback if constants are not available
    MESH_SHADER_MAPPING = {
        "head_shader": "/Game/MetaHumans/Common/Face/Materials/MI_Head_Inst",
        "teeth_shader": "/Game/MetaHumans/Common/Face/Materials/MI_Teeth_Inst",
        "saliva_shader": "/Game/MetaHumans/Common/Face/Materials/MI_Saliva_Inst",
        "eyeLeft_shader": "/Game/MetaHumans/Common/Face/Materials/MI_EyeRefractive_Inst_L",
        "eyeRight_shader": "/Game/MetaHumans/Common/Face/Materials/MI_EyeRefractive_Inst_R",
        "eyeshell_shader": "/Game/MetaHumans/Common/Face/Materials/MI_EyeOcclusion_Inst",
        "eyelashes_shader": "/Game/MetaHumans/Common/Materials/M_EyelashLowerLODs_Inst",
        "cartilage_shader": "/Game/MetaHumans/Common/Face/Materials/M_Cartilage",
    }


class DNA_OT_RefreshMaterialSlotNames(Operator):
    """Refresh material slot names from the head mesh (matching example)"""
    bl_idname = "dna.refresh_material_slot_names"
    bl_label = "Refresh Material Slot Names"
    bl_description = "Refresh material slot names from the head mesh"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        """Execute the refresh material slot names operation"""
        dna_tools = context.scene.dna_tools

        # Clear existing mappings
        dna_tools.material_slot_mappings.clear()

        # Get head mesh
        head_mesh = dna_tools.head_mesh
        if not head_mesh or not head_mesh.data.materials:
            self.report({'WARNING'}, 'No head mesh or materials found')
            return {'CANCELLED'}

        # Add material slots from head mesh
        for i, material_slot in enumerate(head_mesh.material_slots):
            if material_slot.material:
                mapping = dna_tools.material_slot_mappings.add()
                mapping.name = material_slot.material.name

                # Try to find matching Unreal material instance path
                material_name = material_slot.material.name.lower()

                # Map common material names to Unreal paths
                if 'head' in material_name:
                    mapping.asset_path = MESH_SHADER_MAPPING.get('head_shader', '')
                elif 'teeth' in material_name:
                    mapping.asset_path = MESH_SHADER_MAPPING.get('teeth_shader', '')
                elif 'saliva' in material_name:
                    mapping.asset_path = MESH_SHADER_MAPPING.get('saliva_shader', '')
                elif 'eye' in material_name and 'left' in material_name:
                    mapping.asset_path = MESH_SHADER_MAPPING.get('eyeLeft_shader', '')
                elif 'eye' in material_name and 'right' in material_name:
                    mapping.asset_path = MESH_SHADER_MAPPING.get('eyeRight_shader', '')
                elif 'eyeshell' in material_name:
                    mapping.asset_path = MESH_SHADER_MAPPING.get('eyeshell_shader', '')
                elif 'eyelash' in material_name:
                    mapping.asset_path = MESH_SHADER_MAPPING.get('eyelashes_shader', '')
                elif 'cartilage' in material_name:
                    mapping.asset_path = MESH_SHADER_MAPPING.get('cartilage_shader', '')
                else:
                    mapping.asset_path = ''

                # Validate the path
                mapping.valid_path = bool(mapping.asset_path)

        self.report({'INFO'}, f'Refreshed {len(dna_tools.material_slot_mappings)} material slot mappings')
        return {'FINISHED'}


class DNA_OT_RevertMaterialSlotValues(Operator):
    """Revert material slot values to defaults (matching example)"""
    bl_idname = "dna.revert_material_slot_values"
    bl_label = "Revert Material Slot Values"
    bl_description = "Revert material slot values to default MetaHuman paths"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        """Execute the revert material slot values operation"""
        dna_tools = context.scene.dna_tools

        # Revert each mapping to default values
        for mapping in dna_tools.material_slot_mappings:
            material_name = mapping.name.lower()

            # Reset to default MetaHuman material instance paths
            if 'head' in material_name:
                mapping.asset_path = MESH_SHADER_MAPPING.get('head_shader', '')
            elif 'teeth' in material_name:
                mapping.asset_path = MESH_SHADER_MAPPING.get('teeth_shader', '')
            elif 'saliva' in material_name:
                mapping.asset_path = MESH_SHADER_MAPPING.get('saliva_shader', '')
            elif 'eye' in material_name and 'left' in material_name:
                mapping.asset_path = MESH_SHADER_MAPPING.get('eyeLeft_shader', '')
            elif 'eye' in material_name and 'right' in material_name:
                mapping.asset_path = MESH_SHADER_MAPPING.get('eyeRight_shader', '')
            elif 'eyeshell' in material_name:
                mapping.asset_path = MESH_SHADER_MAPPING.get('eyeshell_shader', '')
            elif 'eyelash' in material_name:
                mapping.asset_path = MESH_SHADER_MAPPING.get('eyelashes_shader', '')
            elif 'cartilage' in material_name:
                mapping.asset_path = MESH_SHADER_MAPPING.get('cartilage_shader', '')
            else:
                # For unknown materials, use head shader as default
                mapping.asset_path = MESH_SHADER_MAPPING.get('head_shader', '')

            # Validate the path
            mapping.valid_path = bool(mapping.asset_path)

        self.report({'INFO'}, f'Reverted {len(dna_tools.material_slot_mappings)} material slot mappings to defaults')
        return {'FINISHED'}


# Classes to register
classes = [
    DNA_OT_RefreshMaterialSlotNames,
    DNA_OT_RevertMaterialSlotValues,
]


def register():
    """Register the operator classes"""
    for cls in classes:
        bpy.utils.register_class(cls)


def unregister():
    """Unregister the operator classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)


if __name__ == "__main__":
    register()
