{"net": {"http_server_properties": {"broken_alternative_services": [{"broken_count": 2, "host": "fonts.gstatic.com", "isolation": [], "port": 443, "protocol_str": "quic"}], "servers": [{"isolation": [], "server": "https://blog.megascans.se", "supports_spdy": true}, {"isolation": [], "server": "https://quixel.com", "supports_spdy": true}, {"isolation": [], "server": "https://tracking.epicgames.com", "supports_spdy": true}, {"isolation": [], "server": "https://cdnjs.cloudflare.com", "supports_spdy": true}, {"isolation": [], "server": "https://www.epicgames.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3-29"], "expiration": "13395484814508946", "port": 443, "protocol_str": "quic"}], "isolation": [], "network_stats": {"srtt": 21920}, "server": "https://sentry.io", "supports_spdy": true}, {"isolation": [], "server": "https://static-assets-prod.unrealengine.com", "supports_spdy": true}, {"isolation": [], "server": "https://talon-service-prod.ecosec.on.epicgames.com", "supports_spdy": true}, {"isolation": [], "server": "https://api.hcaptcha.com", "supports_spdy": true}, {"isolation": [], "server": "https://js.hcaptcha.com", "supports_spdy": true}, {"isolation": [], "server": "https://talon-website-prod.ecosec.on.epicgames.com", "supports_spdy": true}, {"isolation": [], "server": "https://www.fab.com", "supports_spdy": true}, {"isolation": [], "server": "https://static.fab.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3-29"], "expiration": "13395484923723237", "port": 443, "protocol_str": "quic"}], "isolation": [], "network_stats": {"srtt": 22005}, "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"isolation": [], "server": "https://cdn.quixel.com", "supports_spdy": true}, {"isolation": [], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"isolation": [], "server": "https://d3uwib8iif8w1p.cloudfront.net", "supports_spdy": true}, {"isolation": [], "server": "https://ddinktqu5prvc.cloudfront.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3-29"], "expiration": "13395486468505962", "port": 443, "protocol_str": "quic"}], "isolation": [], "network_stats": {"srtt": 22215}, "server": "https://www.google-analytics.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3-29"], "expiration": "13395486468570730", "port": 443, "protocol_str": "quic"}], "isolation": [], "network_stats": {"srtt": 23349}, "server": "https://www.googletagmanager.com", "supports_spdy": true}], "supports_quic": {"address": "************", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G", "CAESABiAgICA+P////8B": "4G"}}}